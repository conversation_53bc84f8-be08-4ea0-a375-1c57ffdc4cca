// 脚本解析器命令完整性测试脚本
//地名与坐标1110,30,170,25
//任务详情栏35,270,365,470
//中间任务执行栏470,270,350,480
//任务栏1050,400,230,200
slice(1) {
	# 移动鼠标到指定坐标
	# 参数: (x坐标, y坐标)
	MOUSE_MOVE(100, 200)

	# 移动鼠标到指定坐标并执行左键点击
	#参数：x坐标, y坐标
	MOUSE_LEFT(x, y)

	# 移动鼠标到指定坐标并执行右键点击
	#参数：x坐标, y坐标
	MOUSE_RIGHT(x, y)

	# 移动并左键双击
	# 参数: (x坐标, y坐标)
	MOUSE_DOUBLE(x, y)

    # 连接的键盘输入，按键之间随机等待200ms-500ms
	# 参数: (按键1, 按键2, ...)，示例KEYBOARD_INPUT(A, B, C)
	KEYBOARD_INPUT(key1, key2, ...)

	#功能：先点击标题栏激活，再键盘输入
	#参数: (按键1, 按键2, ...)
	TITLEBAR_KEYBOARD_INPUT(key1, key2, ...)

	# 标签定位和跳转
	# 两种模式：
	#ROI模式：LABEL_LOCATE(x, y, width, height) - OCR识别区域内标签
	#文本模式：LABEL_LOCATE(targetText) - 直接查找文本标签
	LABEL_LOCATE(100, 100, 200, 50)  // ROI识别模式
	LABEL_LOCATE(标签内容)      // 文本模式

	# 延迟等待
	# 参数: (延迟毫秒数)
	DELAY(1000)

}

slice(2) {
	# 文字识别
	# 参数: (区域x, 区域y, 区域宽, 区域高)
    TEXT_RECOGNIZE(300, 400, 200, 50)

	# [文字匹配] 在全屏或指定区域内查找文字，并执行指定点击操作，4个区域参数省略即是全屏
	# [文字匹配-支持自定义阈值] 最后一个参数为阈值，支持部分匹配
	# 参数: (文字内容, 点击类型: left/right/double, 区域x, 区域y, 区域宽, 区域高, 阈值)
	# 阈值说明: 0.7(默认) 0.5(宽松，支持部分匹配) 0.3(非常宽松)
	TEXT_MATCH(猫儿偷鸡, left, 300, 400, 200, 50, 0.1)    # 支持部分匹配

	# [图片匹配] 在全屏或指定区域内查找与模板图片最匹配的位置，并执行指定点击操作，4个区域参数省略即是全屏
	# 参数: (模板图片名, 点击类型: left/right/double, 区域x, 区域y, 区域宽, 区域高, 精度: 0.0~1.0)
	VISUAL_MATCH(template_name, left, 0, 0, 1920, 1080, 0.5)

	# 等待某个画面（图片）是否出现，比如画面本没有登录界面，等待几秒后出现登录，该任务就能识别到，才会进行后续代码
	# 参数: (模板图片名, 检查间隔秒, 最大超时秒, 匹配精度阈值)
	WAIT_FOR_VISUAL_MATCH(password_field_active,3,30,0.5)

	# 等待固定坐标矩形区域，两次ocr文字识别出来的结果是否相同，不同则会一直阻塞，直到相同才会往后执行，用于检测画面固定区域是否移动
	# 参数: (x, y, 宽, 高, 检查间隔秒, 最大超时秒) 超时会继续执行后续指令
	WAIT_FOR_SCREEN_STILL(350,630,300,50,5,30)

	#在指定区域内找到图标并拖动到新位置
	# 参数: (模板图片名,区域x, 区域y, 区域宽, 区域高,拖动目标坐标x,拖动目标坐标y, 阈值)
    VISION_MOUSE_DRAG(app_icon, 100, 100, 300, 300, 600, 400, 0.5)
}