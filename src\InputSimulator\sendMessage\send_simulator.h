#ifndef INPUT_SIMULATOR_H
#define INPUT_SIMULATOR_H

#include <windows.h> // Required for HWND, UINT, POINT etc.

// Define a namespace to encapsulate the functions
namespace InputSim {

    // --- PostMessage Based Functions (Asynchronous, Non-Blocking - Recommended) ---

    /**
     * @brief Uses PostMessage to send a key press (Down + Up) with random delay.
     * @param hwnd Target window handle.
     * @param vkCode Virtual Key Code (e.g., 'W', VK_SPACE, VK_F1).
     * @param minDelayMs Minimum delay between KeyDown and KeyUp (milliseconds).
     * @param maxDelayMs Maximum delay between KeyDown and KeyUp (milliseconds).
     */
    void PostKey(HWND hwnd, UINT vkCode, int minDelayMs = 50, int maxDelayMs = 150);

    /**
     * @brief Uses PostMessage to send a left mouse click (Down + Up) at specified coordinates.
     * Includes random delay and slight coordinate randomization.
     * @param hwnd Target window handle.
     * @param x Target X coordinate (client coordinates).
     * @param y Target Y coordinate (client coordinates).
     * @param minDelayMs Minimum delay between ButtonDown and ButtonUp (milliseconds).
     * @param maxDelayMs Maximum delay between ButtonDown and ButtonUp (milliseconds).
     * @param coordRandomness Maximum random offset applied to coordinates (± pixels).
     */
    void PostLeftClick(HWND hwnd, int x, int y, int minDelayMs = 40, int maxDelayMs = 120, int coordRandomness = 2);

    /**
     * @brief Uses PostMessage to send a right mouse click (Down + Up) at specified coordinates.
     * Includes random delay and slight coordinate randomization.
     * @param hwnd Target window handle.
     * @param x Target X coordinate (client coordinates).
     * @param y Target Y coordinate (client coordinates).
     * @param minDelayMs Minimum delay between ButtonDown and ButtonUp (milliseconds).
     * @param maxDelayMs Maximum delay between ButtonDown and ButtonUp (milliseconds).
     * @param coordRandomness Maximum random offset applied to coordinates (± pixels).
     */
    void PostRightClick(HWND hwnd, int x, int y, int minDelayMs = 40, int maxDelayMs = 120, int coordRandomness = 2);

    /**
     * @brief Uses PostMessage to simulate a smooth mouse movement from current position to target.
     * Moves in multiple steps with randomized timing, respecting current mouse button state.
     * @param hwnd Target window handle.
     * @param endX Target X coordinate (client coordinates).
     * @param endY Target Y coordinate (client coordinates).
     * @param steps Number of intermediate steps for the movement. More steps = smoother.
     * @param totalDurationMs Approximate total duration of the movement (milliseconds).
     */
    void PostMouseMoveSmooth(HWND hwnd, int endX, int endY, int steps = 15, int totalDurationMs = 150);


    // --- SendMessage Based Functions (Synchronous, Blocking - Use with Caution) ---
    // !!! WARNING: These functions block the calling thread until the message is processed. !!!
    // !!! This can cause your application to hang if the target window is unresponsive.  !!!

    /**
     * @brief Uses SendMessage to send a key press (Down + Up) with random delay.
     * @warning BLOCKS the calling thread during SendMessage calls.
     * @param hwnd Target window handle.
     * @param vkCode Virtual Key Code.
     * @param minDelayMs Minimum delay between KeyDown and KeyUp (milliseconds).
     * @param maxDelayMs Maximum delay between KeyDown and KeyUp (milliseconds).
     */
    void SendKey(HWND hwnd, UINT vkCode, int minDelayMs = 50, int maxDelayMs = 150);

    /**
     * @brief Uses SendMessage to send a left mouse click (Down + Up) at specified coordinates.
     * @warning BLOCKS the calling thread during SendMessage calls.
     * @param hwnd Target window handle.
     * @param x Target X coordinate (client coordinates).
     * @param y Target Y coordinate (client coordinates).
     * @param minDelayMs Minimum delay between ButtonDown and ButtonUp (milliseconds).
     * @param maxDelayMs Maximum delay between ButtonDown and ButtonUp (milliseconds).
     * @param coordRandomness Maximum random offset applied to coordinates (± pixels).
     */
    void SendLeftClick(HWND hwnd, int x, int y, int minDelayMs = 40, int maxDelayMs = 120, int coordRandomness = 2);

    /**
     * @brief Uses SendMessage to send a right mouse click (Down + Up) at specified coordinates.
     * @warning BLOCKS the calling thread during SendMessage calls.
     * @param hwnd Target window handle.
     * @param x Target X coordinate (client coordinates).
     * @param y Target Y coordinate (client coordinates).
     * @param minDelayMs Minimum delay between ButtonDown and ButtonUp (milliseconds).
     * @param maxDelayMs Maximum delay between ButtonDown and ButtonUp (milliseconds).
     * @param coordRandomness Maximum random offset applied to coordinates (± pixels).
     */
    void SendRightClick(HWND hwnd, int x, int y, int minDelayMs = 40, int maxDelayMs = 120, int coordRandomness = 2);

    /**
     * @brief Uses SendMessage to simulate a smooth mouse movement from current position to target.
     * @warning BLOCKS the calling thread during *each step* of the movement. Likely very slow and unresponsive. Strongly discouraged.
     * @param hwnd Target window handle.
     * @param endX Target X coordinate (client coordinates).
     * @param endY Target Y coordinate (client coordinates).
     * @param steps Number of intermediate steps for the movement.
     * @param totalDurationMs Approximate total duration of the movement (milliseconds).
     */
    void SendMouseMoveSmooth(HWND hwnd, int endX, int endY, int steps = 15, int totalDurationMs = 150);

} // namespace InputSim

#endif // INPUT_SIMULATOR_H