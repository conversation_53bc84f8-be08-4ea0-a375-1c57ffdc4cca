#include "ConfigLoader.h"

#include <nlohmann/json.hpp> // 包含 nlohmann/json 庫
#include <fstream> // 包含文件流庫
#include <iostream> // 包含輸入輸出流庫
#include <GUI/consolelog/consolelog.h>

// 增加必要的头文件
#include <chrono>


// 函数实现：从 JSON 文件加载 VM 连接配置
std::vector<std::pair<std::string, int>> loadIpConfigFromJson(const std::string& filePath) {
    // 返回类型改为 std::vector<std::pair<std::string, int>>
    std::vector<std::pair<std::string, int>> vmConfigs;
    std::ifstream configFile(filePath);

    if (!configFile.is_open()) {
        AddLogInfo(LogLevel::Warning, "配置加载器无法打开IP配置文件:" + filePath);
        return vmConfigs;
    }

    nlohmann::json jsonConfig;
    try {
        configFile >> jsonConfig;

        // 检查是否存在 "vm_configs" 键，并且它是一个数组
        if (jsonConfig.contains("vm_configs") && jsonConfig["vm_configs"].is_array()) {
            // 遍历 JSON 数组中的每个 VM 配置对象
            for (const auto& vm : jsonConfig["vm_configs"]) {
                // 检查每个对象是否包含 "ip" 和 "vm_name" 键
                if (vm.is_object() && vm.contains("vm_name") && vm["vm_name"].is_string() &&
                    vm.contains("port") && vm["port"].is_number_integer()) {

                    // 提取 IP 地址和端口
                    std::string vm_n = vm["vm_name"].get<std::string>();
                    int port = vm["port"].get<int>();

                    // 将配置添加到 vector 中，使用 {} 直接构造 pair
                    vmConfigs.push_back({ vm_n, port });

                }
                else {
                    AddLogInfo(LogLevel::Error, "配置加载器无法打开IP配置文件格式不正確:" + filePath);
                }
            }
        }
        else {
            AddLogInfo(LogLevel::Error, "配置加载器无法打开IP配置文件应包括vm_configs数组:" + filePath);
        }


    }
    catch (const nlohmann::json::exception& e) {
        AddLogInfo(LogLevel::Error, "配置加载器无法打开IP配置文件JSON 解析错误:" + filePath);
        vmConfigs.clear();
    }
    catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "配置加载器无法打开IP配置文件发生未知错误:" + filePath);
        vmConfigs.clear();
    }


    //if (!vmConfigs.empty()) {
    //    AddLogInfo(LogLevel::Warning, "配置加载器成功加载IP配置文件" + filePath);
    //}

    return vmConfigs;
}