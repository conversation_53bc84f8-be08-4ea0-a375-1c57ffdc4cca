
#include <iostream>
#include <vector>
#include <algorithm>
#include "find_window.h"

BOOL CALLBACK EnumWindowsProc(HWND hwnd, LPARAM lParam) {
    std::vector<HWND>* windows = reinterpret_cast<std::vector<HWND>*>(lParam);
    windows->push_back(hwnd);
    return TRUE;
}

HWND findWindowByPartialName(const std::wstring& partialName) {
    std::vector<HWND> allWindows;
    EnumWindows(EnumWindowsProc, reinterpret_cast<LPARAM>(&allWindows));

    for (HWND hwnd : allWindows) {
        int length = GetWindowTextLength(hwnd);
        if (length > 0) {
            std::vector<wchar_t> buffer(length + 1);
            GetWindowTextW(hwnd, buffer.data(), length + 1);
            std::wstring windowTitle(buffer.data());
            if (windowTitle.find(partialName) != std::wstring::npos) {
                return hwnd;
            }
        }
    }
    return nullptr;
}