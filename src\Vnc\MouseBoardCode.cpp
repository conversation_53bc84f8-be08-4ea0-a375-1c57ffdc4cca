#include "MouseBoardCode.h"
#include <map>
#include <string>
#include <GUI/consolelog/consolelog.h>
#include <vector>


// 静态 map 用于存储按键名称到 Key Code 的映射
static const std::map<std::string, int> keyCodeMap = {
    {"SPACE", XK_space}, // 注意：通常是 XK_space 或 XK_Space
    {"ESC", XK_Escape},
    {"TAB", XK_Tab},
    {"tab", XK_Tab},  // 添加小写版本
    {"CAPS_LOCK", XK_Caps_Lock},
    {"SHIFT_L", XK_Shift_L},
    {"CONTROL_L", XK_Control_L},
    {"DELETE", XK_Delete},
    {"END", XK_End},
    {"BEGIN", XK_Begin}, // 对应 Home 键
    {"F1", XK_F1},
    {"F2", XK_F2}, // F2通常是F1+1，依此类推
    {"F3", XK_F3},
    {"F4", XK_F4},
    {"F5", XK_F5},
    {"F6", XK_F6},
    {"F7", XK_F7},
    {"F8", XK_F8},
    {"F9", XK_F9},
    {"F10", XK_F10},
    {"F11", XK_F11},
    {"F12", XK_F12},
    {"0", XK_0},
    {"1", XK_1},
    {"2", XK_2},
    {"3", XK_3},
    {"4", XK_4},
    {"5", XK_5},
    {"6", XK_6},
    {"7", XK_7},
    {"8", XK_8},
    {"9", XK_9},
    // 添加特殊符号支持
    {"@", XK_at},        // @ 符号 (0x040)
    {".", XK_period},    // . 符号 (0x02e)
    // 大写字母
    {"A", XK_A},
    {"B", XK_A + ('B' - 'A')}, // 示例：通过字符偏移量获取
    {"C", XK_A + ('C' - 'A')},
    {"D", XK_A + ('D' - 'A')},
    {"E", XK_A + ('E' - 'A')},
    {"F", XK_A + ('F' - 'A')},
    {"G", XK_A + ('G' - 'A')},
    {"H", XK_A + ('H' - 'A')},
    {"I", XK_A + ('I' - 'A')},
    {"J", XK_A + ('J' - 'A')},
    {"K", XK_A + ('K' - 'A')},
    {"L", XK_A + ('L' - 'A')},
    {"M", XK_A + ('M' - 'A')},
    {"N", XK_A + ('N' - 'A')},
    {"O", XK_A + ('O' - 'A')},
    {"P", XK_A + ('P' - 'A')},
    {"Q", XK_A + ('Q' - 'A')},
    {"R", XK_A + ('R' - 'A')},
    {"S", XK_A + ('S' - 'A')},
    {"T", XK_A + ('T' - 'A')},
    {"U", XK_A + ('U' - 'A')},
    {"V", XK_A + ('V' - 'A')},
    {"W", XK_A + ('W' - 'A')},
    {"X", XK_A + ('X' - 'A')},
    {"Y", XK_A + ('Y' - 'A')},
    {"Z", XK_A + ('Z' - 'A')},
    // 添加小写字母支持
    {"a", XK_a},
    {"b", XK_a + ('b' - 'a')},
    {"c", XK_a + ('c' - 'a')},
    {"d", XK_a + ('d' - 'a')},
    {"e", XK_a + ('e' - 'a')},
    {"f", XK_a + ('f' - 'a')},
    {"g", XK_a + ('g' - 'a')},
    {"h", XK_a + ('h' - 'a')},
    {"i", XK_a + ('i' - 'a')},
    {"j", XK_a + ('j' - 'a')},
    {"k", XK_a + ('k' - 'a')},
    {"l", XK_a + ('l' - 'a')},
    {"m", XK_a + ('m' - 'a')},
    {"n", XK_a + ('n' - 'a')},
    {"o", XK_a + ('o' - 'a')},
    {"p", XK_a + ('p' - 'a')},
    {"q", XK_a + ('q' - 'a')},
    {"r", XK_a + ('r' - 'a')},
    {"s", XK_a + ('s' - 'a')},
    {"t", XK_a + ('t' - 'a')},
    {"u", XK_a + ('u' - 'a')},
    {"v", XK_a + ('v' - 'a')},
    {"w", XK_a + ('w' - 'a')},
    {"x", XK_a + ('x' - 'a')},
    {"y", XK_a + ('y' - 'a')},
    {"z", XK_a + ('z' - 'a')}
};

// 统一的按键函数
// 参数 keyName: 字符串形式的按键名称（例如 "SPACE", "F1", "A"）
InputTask createKeyboardTask(const std::string& keyName) {
    auto it = keyCodeMap.find(keyName);
    if (it != keyCodeMap.end()) {
        // 找到对应的 Key Code
        return { InputType::KEYBOARD, 0, 0, it->second };
    }else {
        // 未知按键，可以返回一个默认的无效任务，或者抛出异常，或者打印错误
        AddLogInfo(LogLevel::Error, "不支持这个按键: '" + keyName + "'，请检查空格或者其他符号");
        return { InputType::KEYBOARD, 0, 0, 0 }; // 返回一个默认值，例如 0
    }
}

// 鼠标左键
InputTask mouseLeftClick() {
    return { InputType::MOUSE_LEFT_CLICK };
}
// 鼠标右键
InputTask mouseRightClick() {
    return { InputType::MOUSE_RIGHT_CLICK };
}
// 鼠标移动到某点
InputTask mouseMoveCoord(int x, int y) {
    return { InputType::MOUSE_MOVE, x, y };
}
// 鼠标拖动到某点
InputTask mouseDragCoord(int x, int y) {
    return { InputType::DRAGMOUSE, x, y };
}

// 示例用法：
// InputTask spaceKey = createKeyboardTask("SPACE");
// InputTask f1Key = createKeyboardTask("F1");
// InputTask aKey = createKeyboardTask("A");

// 组合操作函数实现
// 鼠标移动到坐标并左击一次
std::vector<InputTask> mouseMoveAndLeftClick(int x, int y) {
    return {
        mouseMoveCoord(x, y),
        mouseLeftClick()
    };
}

// 鼠标移动到坐标并双击（左击两次）
std::vector<InputTask> mouseMoveAndDoubleClick(int x, int y) {
    return {
        mouseMoveCoord(x, y),
        mouseLeftClick(),
        mouseLeftClick()
    };
}

// 鼠标移动到坐标并右击一次
std::vector<InputTask> mouseMoveAndRightClick(int x, int y) {
    return {
        mouseMoveCoord(x, y),
        mouseRightClick()
    };
}

// 鼠标双击操作（在当前位置双击，两次点击之间有延迟）
std::vector<InputTask> mouseDoubleClick() {
    return {
        mouseLeftClick(),
        mouseLeftClick()
    };
}

