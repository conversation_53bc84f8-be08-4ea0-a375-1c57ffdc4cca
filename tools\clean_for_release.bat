@echo off
echo ========================================
echo       发布前代码清理脚本
echo ========================================

set PROJECT_ROOT=..

echo 步骤1: 删除测试文件...
if exist "%PROJECT_ROOT%\test_*.txt" del /q "%PROJECT_ROOT%\test_*.txt"
if exist "%PROJECT_ROOT%\*test*.cpp" del /q "%PROJECT_ROOT%\*test*.cpp"
if exist "%PROJECT_ROOT%\debug_*.log" del /q "%PROJECT_ROOT%\debug_*.log"

echo 步骤2: 清理临时文件...
if exist "%PROJECT_ROOT%\*.tmp" del /q "%PROJECT_ROOT%\*.tmp"
if exist "%PROJECT_ROOT%\*.bak" del /q "%PROJECT_ROOT%\*.bak"
if exist "%PROJECT_ROOT%\*.orig" del /q "%PROJECT_ROOT%\*.orig"

echo 步骤3: 删除IDE临时文件...
if exist "%PROJECT_ROOT%\.vs" rd /s /q "%PROJECT_ROOT%\.vs"
if exist "%PROJECT_ROOT%\x64" rd /s /q "%PROJECT_ROOT%\x64"
if exist "%PROJECT_ROOT%\Debug" rd /s /q "%PROJECT_ROOT%\Debug"
if exist "%PROJECT_ROOT%\*.vcxproj.user" del /q "%PROJECT_ROOT%\*.vcxproj.user"

echo 步骤4: 清理CMake缓存...
if exist "%PROJECT_ROOT%\CMakeCache.txt" del /q "%PROJECT_ROOT%\CMakeCache.txt"
if exist "%PROJECT_ROOT%\CMakeFiles" rd /s /q "%PROJECT_ROOT%\CMakeFiles"
if exist "%PROJECT_ROOT%\cmake_install.cmake" del /q "%PROJECT_ROOT%\cmake_install.cmake"

echo 步骤5: 删除敏感配置文件...
if exist "%PROJECT_ROOT%\config.local" del /q "%PROJECT_ROOT%\config.local"
if exist "%PROJECT_ROOT%\secrets.txt" del /q "%PROJECT_ROOT%\secrets.txt"

echo 步骤6: 清理日志文件...
if exist "%PROJECT_ROOT%\*.log" del /q "%PROJECT_ROOT%\*.log"
if exist "%PROJECT_ROOT%\logs\*" del /q "%PROJECT_ROOT%\logs\*"

echo 步骤7: 创建发布目录结构...
if not exist "%PROJECT_ROOT%\Release_Package" mkdir "%PROJECT_ROOT%\Release_Package"
if not exist "%PROJECT_ROOT%\Release_Package\assets" mkdir "%PROJECT_ROOT%\Release_Package\assets"
if not exist "%PROJECT_ROOT%\Release_Package\scripts" mkdir "%PROJECT_ROOT%\Release_Package\scripts"

echo 步骤8: 复制必要的配置文件...
if exist "%PROJECT_ROOT%\assets\ip_config.json" (
    copy "%PROJECT_ROOT%\assets\ip_config.json" "%PROJECT_ROOT%\Release_Package\assets\"
    echo IP配置文件已复制（请检查是否包含敏感信息）
)

if exist "%PROJECT_ROOT%\assets\template_config.json" (
    copy "%PROJECT_ROOT%\assets\template_config.json" "%PROJECT_ROOT%\Release_Package\assets\"
    echo 模板配置文件已复制
)

echo 步骤9: 创建示例脚本...
echo # 示例自动化脚本 > "%PROJECT_ROOT%\Release_Package\scripts\example.txt"
echo SLICE_CONFIG(1,1) >> "%PROJECT_ROOT%\Release_Package\scripts\example.txt"
echo SLICE(1): >> "%PROJECT_ROOT%\Release_Package\scripts\example.txt"
echo MOUSE_MOVE(100, 200) >> "%PROJECT_ROOT%\Release_Package\scripts\example.txt"
echo MOUSE_LEFT >> "%PROJECT_ROOT%\Release_Package\scripts\example.txt"
echo KEYBOARD_INPUT(H, e, l, l, o) >> "%PROJECT_ROOT%\Release_Package\scripts\example.txt"

echo ========================================
echo 清理完成！
echo 
echo 注意事项：
echo 1. 请检查 assets\ip_config.json 是否包含敏感IP信息
echo 2. 确认所有调试代码已移除
echo 3. 验证Release编译配置正确
echo 4. 运行完整测试确保功能正常
echo ========================================
pause 