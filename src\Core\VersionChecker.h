#ifndef VERSIONCHECKER_H
#define VERSIONCHECKER_H

#include <windows.h>
#include <winhttp.h>
#include <string>
#include <regex>
#include <stdexcept>
#include <utility>

#pragma comment(lib, "winhttp.lib")

class VersionChecker {
private:
    // 硬编码的当前版本
    static const std::string CURRENT_VERSION;
    
    // 硬编码的发布时间（Unix时间戳）
    static const time_t RELEASE_TIMESTAMP;
    
    // 服务器信息
    static const std::wstring SERVER_NAME;
    static const std::wstring OBJECT_NAME;
    static const int SERVER_PORT;
    
    // 网络请求超时时间（毫秒）
    static const DWORD NETWORK_TIMEOUT;
    
    // 允许的最大使用时间（30天，单位：秒）
    static const time_t MAX_USAGE_TIME;

public:
    // 版本检查结果枚举
    enum class CheckResult {
        SUCCESS,                    // 检查成功
        NETWORK_ERROR,             // 网络错误
        VERSION_MISMATCH,          // 版本号不匹配
        TIME_EXPIRED,              // 时间过期
        PARSE_ERROR,               // 解析错误
        UNKNOWN_ERROR              // 未知错误
    };
    
    // 检查版本
    static CheckResult checkVersion();
    
    // 获取错误信息
    static const std::string& getLastError() { return lastError; }
    
    // 获取剩余使用天数
    static int getRemainingDays();
    
    // 格式化错误信息用于显示
    static std::string getFormattedErrorMessage(CheckResult result);
    
    // 获取当前版本号
    static const std::string& getVersion();

private:
    // 获取远程版本和服务器时间
    static std::pair<std::string, time_t> fetchVersionAndTime();
    
    // 下载字符串内容
    static std::string downloadString(const std::wstring& serverName, 
                                     const std::wstring& objectName, 
                                     int port, 
                                     time_t& serverTime);
    
    // 从内容中提取版本号
    static std::string extractVersion(const std::string& content);
    
    // 验证时间
    static bool validateTime(time_t serverTime);
    
    // 解析HTTP日期
    static time_t parseHttpDate(const std::string& dateStr);
    
    // 宽字符串转字符串
    static std::string wstringToString(const std::wstring& wstr);
    
    // 字符串转宽字符串
    static std::wstring stringToWstring(const std::string& str);
    
    // 存储最后的错误信息
    static std::string lastError;
    
    // 存储最后获取的服务器时间
    static time_t lastServerTime;
};

#endif // VERSIONCHECKER_H 