#ifndef VMTASKS_H
#define VMTASKS_H

#include <string>
#include <vector>
#include <future>
#include <memory>
#include <variant>
#include <opencv2/opencv.hpp> // For cv::Point
#include <rfb/rfbclient.h> // Include the necessary VNC client header
#include "MouseBoardCode.h" // Assuming InputTask is defined here and might use rfbClient
#include "VNCControl.h" // Include VNCControl.h for handleInput function

// Forward declaration for VMVision to avoid circular dependency
class VMVision;

// Forward declaration
class ScreenFrameDistributor;

// --- Result Structures ---

struct BaseTaskResult {
    bool success = false;
    std::string errorMessage;
    // Potentially a unique ID for the task instance if needed for correlation
    // int taskId = 0;
};

struct KeyboardMouseResult : public BaseTaskResult {};

struct VisionMatchResult : public BaseTaskResult {
    cv::Point matchLocation; // Only valid if success is true
    double confidence = 0.0; // Confidence score of the match
};

struct WordsMatchResult : public BaseTaskResult {
    cv::Point matchLocation; // Only valid if success is true (first word/phrase found)
    std::vector<cv::Rect> allMatchLocations; // Bounding boxes for all occurrences
    std::vector<std::string> recognizedTexts; // Recognized texts corresponding to allMatchLocations
    // double overallConfidence = 0.0;
};

// 文本识别结果结构
struct TextRecognizeResult : public BaseTaskResult {
    std::vector<std::string> recognizedTexts; // 识别到的所有文本
    std::vector<cv::Rect> textLocations; // 每个文本的位置
    std::vector<double> confidences; // 每个文本的置信度
};

// 等待画面静止结果结构
struct WaitForScreenStillResult : public BaseTaskResult {
    bool isStill = false; // 画面是否已静止
    int totalChecks = 0; // 总检测次数
    double elapsedSeconds = 0.0; // 实际等待时间（秒）
};

// 等待视觉匹配结果结构
struct WaitForVisualMatchResult : public BaseTaskResult {
    bool matchFound = false; // 是否找到匹配
    cv::Point matchLocation; // 匹配位置（仅在success和matchFound为true时有效）
    double confidence = 0.0; // 匹配置信度
    int totalChecks = 0; // 总检测次数
    double elapsedSeconds = 0.0; // 实际等待时间（秒）
};

// 标签定位结果结构
struct LabelLocateResult : public BaseTaskResult {
    bool labelFound = false; // 是否找到标签
    std::string foundLabelName; // 找到的标签名称
    int jumpToLine = -1; // 跳转到的行号
    std::string recognizedText; // OCR识别到的文本（仅在使用ROI时有值）
};

// --- Task Enum and Base Task ---

enum class VMTaskType {
    KEYBOARD_MOUSE_ACTION,              // 直接键盘鼠标操作
    IMAGE_MATCH_VISION,
    WORDS_MATCH_VISION,
    TEXT_RECOGNIZE_VISION,
    WAIT_FOR_SCREEN_STILL,
    WAIT_FOR_VISUAL_MATCH,  // 等待视觉匹配类型
    LABEL_LOCATE            // 标签定位任务类型
};

class IVMTask {
public:
    virtual ~IVMTask() = default;
    virtual VMTaskType getType() const = 0;
    // The 'execute' method will be the core logic for each task type.
    // For KeyboardMouseActionTask, it uses rfbClient.
    // For Vision tasks, DispatchCenter::processXYZTask will likely call VMVision methods and set the promise directly.
    // So, the execute signature here primarily serves KeyboardMouseActionTask or acts as a generic interface point.
    virtual void execute(rfbClient* vncClient, VMVision* visionProcessor /* = nullptr */) = 0; 
    
    const std::string& getVmName() const { return vmName_; } // Getter for vmName

protected:
    // Constructor for base class to initialize vmName
    IVMTask(std::string vmName) : vmName_(std::move(vmName)) {}
    // IVMTask() = default; // Allow default constructor for potential direct use of promise_

    std::string vmName_; // Added vmName member
};

// --- Concrete Task Implementations ---

// 1. 直接键盘鼠标操作任务
class DirectKeyboardMouseActionTask : public IVMTask {
public:
    DirectKeyboardMouseActionTask(std::string vmName, 
                                 std::vector<InputTask> actions, 
                                 std::shared_ptr<std::promise<KeyboardMouseResult>> promise,
                                 int sliceNumber = 1)
        : IVMTask(std::move(vmName)), actions_(std::move(actions)), promise_(std::move(promise)),
          sliceNumber_(sliceNumber) {}

    VMTaskType getType() const override { return VMTaskType::KEYBOARD_MOUSE_ACTION; }

    void execute(rfbClient* vncClient, VMVision* visionProcessor /*unused*/) override;
    
    std::shared_ptr<std::promise<KeyboardMouseResult>> getPromise() { return promise_; }
    
    // 坐标转换支持的访问器方法
    int getSliceNumber() const { return sliceNumber_; }
    const std::vector<InputTask>& getActions() const { return actions_; }
    void setActions(const std::vector<InputTask>& actions) { actions_ = actions; }

private:
    std::vector<InputTask> actions_;
    std::shared_ptr<std::promise<KeyboardMouseResult>> promise_;
    int sliceNumber_;
};

// 2. Image Match Vision Task
class ImageMatchVisionTask : public IVMTask {
public:
    ImageMatchVisionTask(std::string vmName, 
                         std::string templateName, 
                         double threshold, 
                         std::string imagePath, // Added based on DispatchCenter usage
                         cv::Rect roi,         // Added based on DispatchCenter usage
                         int sliceNumber,
                         std::string mouseClickType, // 新增：鼠标点击类型 "left", "right", "double"
                         std::shared_ptr<std::promise<VisionMatchResult>> promise
                         )
        : IVMTask(std::move(vmName)), templateName_(std::move(templateName)), 
          threshold_(threshold), imagePath_(std::move(imagePath)),
          roi_(roi), sliceNumber_(sliceNumber), mouseClickType_(std::move(mouseClickType)), 
          promise_(promise ? std::move(promise) : std::make_shared<std::promise<VisionMatchResult>>()) {}

    // 拷贝构造函数 - 创建新的promise以避免共享
    ImageMatchVisionTask(const ImageMatchVisionTask& other)
        : IVMTask(other.getVmName()), templateName_(other.templateName_), 
          threshold_(other.threshold_), imagePath_(other.imagePath_), roi_(other.roi_), 
          sliceNumber_(other.sliceNumber_), mouseClickType_(other.mouseClickType_), 
          promise_(std::make_shared<std::promise<VisionMatchResult>>()) {} // 创建新的promise

    VMTaskType getType() const override { return VMTaskType::IMAGE_MATCH_VISION; }

    // execute() might be minimal if DispatchCenter::processImageMatchTask handles logic.
    // It's given VMVision* in case it needs to interact with it directly.
    void execute(rfbClient* vncClient /*unused*/, VMVision* visionProcessor) override { 
        // If DispatchCenter::processImageMatchTask handles the logic and promise setting,
        // this execute method might do nothing or log an error if called unexpectedly.
        // Or, it could be the designated place to call visionProcessor methods.
        // For now, let's assume DispatchCenter's process method will set the promise.
        if (!visionProcessor && promise_) {
            VisionMatchResult res; 
            res.success = false; 
            res.errorMessage = "VMVision processor not provided to ImageMatchVisionTask::execute.";
            promise_->set_value(res);
        }
        // Actual vision processing logic and promise setting should happen here or in DispatchCenter.
        // If here, it would use visionProcessor->matchTemplateAsync(...) or similar.
    }

    // Getters for parameters needed by DispatchCenter::processImageMatchTask
    const std::string& getTemplateName() const { return templateName_; }
    double getThreshold() const { return threshold_; }
    const std::string& getImagePath() const { return imagePath_; }
    cv::Rect getRoi() const { return roi_; }
    int getSliceNumber() const { return sliceNumber_; }
    const std::string& getMouseClickType() const { return mouseClickType_; } // 新增getter
    std::shared_ptr<std::promise<VisionMatchResult>> getPromise() { return promise_; }

private:
    // VMVision* visionProcessor_; // Removed, will be passed to execute or used by DispatchCenter
    std::string templateName_;
    double threshold_;
    std::string imagePath_;
    cv::Rect roi_;
    int sliceNumber_;
    std::string mouseClickType_; // 新增：鼠标点击类型
    std::shared_ptr<std::promise<VisionMatchResult>> promise_;
};

// 4. Words Match Vision Task
class WordsMatchVisionTask : public IVMTask {
public:
    WordsMatchVisionTask(std::string vmName, 
                         std::vector<std::string> wordsToMatch, 
                         double threshold, 
                         cv::Rect roi, 
                         int sliceNumber, 
                         std::string mouseClickType, // 新增：鼠标点击类型 "left", "right", "double"
                         std::shared_ptr<std::promise<WordsMatchResult>> promise)
        : IVMTask(std::move(vmName)), wordsToMatch_(std::move(wordsToMatch)), 
          threshold_(threshold), roi_(roi), sliceNumber_(sliceNumber), mouseClickType_(std::move(mouseClickType)), 
          promise_(promise ? std::move(promise) : std::make_shared<std::promise<WordsMatchResult>>()) {}

    // 拷贝构造函数 - 创建新的promise以避免共享
    WordsMatchVisionTask(const WordsMatchVisionTask& other)
        : IVMTask(other.getVmName()), wordsToMatch_(other.wordsToMatch_), 
          threshold_(other.threshold_), roi_(other.roi_), sliceNumber_(other.sliceNumber_), 
          mouseClickType_(other.mouseClickType_), 
          promise_(std::make_shared<std::promise<WordsMatchResult>>()) {} // 创建新的promise

    VMTaskType getType() const override { return VMTaskType::WORDS_MATCH_VISION; }

    void execute(rfbClient* vncClient /*unused*/, VMVision* visionProcessor) override {
        // Similar to ImageMatchVisionTask, logic likely in DispatchCenter::processWordsMatchTask
        if (!visionProcessor && promise_) {
            WordsMatchResult res; 
            res.success = false; 
            res.errorMessage = "VMVision processor not provided to WordsMatchVisionTask::execute.";
            promise_->set_value(res);
        }
    }
    
    // Getters for parameters
    const std::vector<std::string>& getWordsToMatch() const { return wordsToMatch_; }
    double getThreshold() const { return threshold_; }
    cv::Rect getRoi() const { return roi_; }
    int getSliceNumber() const { return sliceNumber_; }
    const std::string& getMouseClickType() const { return mouseClickType_; } // 新增getter
    std::shared_ptr<std::promise<WordsMatchResult>> getPromise() { return promise_; }

private:
    // VMVision* visionProcessor_; // Removed
    std::vector<std::string> wordsToMatch_;
    double threshold_;
    cv::Rect roi_;
    int sliceNumber_;
    std::string mouseClickType_; // 新增：鼠标点击类型
    std::shared_ptr<std::promise<WordsMatchResult>> promise_;
};

// 5. Text Recognize Vision Task
class TextRecognizeVisionTask : public IVMTask {
public:
    TextRecognizeVisionTask(std::string vmName, 
                           cv::Rect roi, 
                           int sliceNumber, 
                           std::shared_ptr<std::promise<TextRecognizeResult>> promise)
        : IVMTask(std::move(vmName)), roi_(roi), sliceNumber_(sliceNumber), promise_(std::move(promise)) {}

    VMTaskType getType() const override { return VMTaskType::TEXT_RECOGNIZE_VISION; }

    void execute(rfbClient* vncClient /*unused*/, VMVision* visionProcessor) override {
        // Similar to other vision tasks, logic likely in DispatchCenter::processTextRecognizeTask
        if (!visionProcessor && promise_) {
            TextRecognizeResult res; 
            res.success = false; 
            res.errorMessage = "VMVision processor not provided to TextRecognizeVisionTask::execute.";
            promise_->set_value(res);
        }
    }
    
    // Getters for parameters
    cv::Rect getRoi() const { return roi_; }
    int getSliceNumber() const { return sliceNumber_; }
    std::shared_ptr<std::promise<TextRecognizeResult>> getPromise() { return promise_; }

private:
    cv::Rect roi_;
    int sliceNumber_;
    std::shared_ptr<std::promise<TextRecognizeResult>> promise_;
};

// 6. Wait For Screen Still Task
class WaitForScreenStillTask : public IVMTask {
public:
    WaitForScreenStillTask(std::string vmName,
                          int sliceNumber,
                          cv::Rect roi,
                          int checkIntervalSeconds,
                          int maxTimeoutSeconds,
                          std::shared_ptr<std::promise<WaitForScreenStillResult>> promise)
        : IVMTask(std::move(vmName)), sliceNumber_(sliceNumber), roi_(roi),
          checkIntervalSeconds_(checkIntervalSeconds), maxTimeoutSeconds_(maxTimeoutSeconds),
          promise_(std::move(promise)) {}

    VMTaskType getType() const override { return VMTaskType::WAIT_FOR_SCREEN_STILL; }

    void execute(rfbClient* vncClient /*unused*/, VMVision* visionProcessor) override {
        // Similar to other vision tasks, logic likely in DispatchCenter::processWaitForScreenStillTask
        if (!visionProcessor && promise_) {
            WaitForScreenStillResult res;
            res.success = false;
            res.errorMessage = "VMVision processor not provided to WaitForScreenStillTask::execute.";
            promise_->set_value(res);
        }
    }

    // Getters for parameters
    int getSliceNumber() const { return sliceNumber_; }
    cv::Rect getRoi() const { return roi_; }
    int getCheckIntervalSeconds() const { return checkIntervalSeconds_; }
    int getMaxTimeoutSeconds() const { return maxTimeoutSeconds_; }
    std::shared_ptr<std::promise<WaitForScreenStillResult>> getPromise() { return promise_; }

private:
    int sliceNumber_;
    cv::Rect roi_;
    int checkIntervalSeconds_;
    int maxTimeoutSeconds_;
    std::shared_ptr<std::promise<WaitForScreenStillResult>> promise_;
};

// 7. Wait For Visual Match Task
class WaitForVisualMatchTask : public IVMTask {
public:
    WaitForVisualMatchTask(std::string vmName,
                          int sliceNumber,
                          std::string templateName,
                          int checkIntervalSeconds,
                          int maxTimeoutSeconds,
                          double threshold,
                          std::shared_ptr<std::promise<WaitForVisualMatchResult>> promise)
        : IVMTask(std::move(vmName)), sliceNumber_(sliceNumber), templateName_(std::move(templateName)),
          checkIntervalSeconds_(checkIntervalSeconds), maxTimeoutSeconds_(maxTimeoutSeconds),
          threshold_(threshold), promise_(std::move(promise)) {}

    VMTaskType getType() const override { return VMTaskType::WAIT_FOR_VISUAL_MATCH; }

    void execute(rfbClient* vncClient /*unused*/, VMVision* visionProcessor) override {
        // Similar to other vision tasks, logic likely in DispatchCenter::processWaitForVisualMatchTask
        if (!visionProcessor && promise_) {
            WaitForVisualMatchResult res;
            res.success = false;
            res.errorMessage = "VMVision processor not provided to WaitForVisualMatchTask::execute.";
            promise_->set_value(res);
        }
    }

    // Getters for parameters
    int getSliceNumber() const { return sliceNumber_; }
    std::string getTemplateName() const { return templateName_; }
    int getCheckIntervalSeconds() const { return checkIntervalSeconds_; }
    int getMaxTimeoutSeconds() const { return maxTimeoutSeconds_; }
    double getThreshold() const { return threshold_; }
    std::shared_ptr<std::promise<WaitForVisualMatchResult>> getPromise() { return promise_; }

private:
    int sliceNumber_;
    std::string templateName_;
    int checkIntervalSeconds_;
    int maxTimeoutSeconds_;
    double threshold_;
    std::shared_ptr<std::promise<WaitForVisualMatchResult>> promise_;
};

// 8. Label Locate Task - 标签定位任务
class LabelLocateTask : public IVMTask {
public:
    // 构造函数：用于ROI识别
    LabelLocateTask(std::string vmName,
                    cv::Rect roi,
                    int sliceNumber,
                    const std::string& scriptContent,
                    std::shared_ptr<std::promise<LabelLocateResult>> promise)
        : IVMTask(std::move(vmName)), useRoi_(true), roi_(roi), 
          targetText_(""), sliceNumber_(sliceNumber), 
          scriptContent_(scriptContent), promise_(std::move(promise)) {}

    // 构造函数：用于直接文本匹配
    LabelLocateTask(std::string vmName,
                    std::string targetText,
                    int sliceNumber,
                    const std::string& scriptContent,
                    std::shared_ptr<std::promise<LabelLocateResult>> promise)
        : IVMTask(std::move(vmName)), useRoi_(false), roi_(0, 0, 0, 0), 
          targetText_(std::move(targetText)), sliceNumber_(sliceNumber), 
          scriptContent_(scriptContent), promise_(std::move(promise)) {}

    VMTaskType getType() const override { return VMTaskType::LABEL_LOCATE; }

    void execute(rfbClient* vncClient /*unused*/, VMVision* visionProcessor) override {
        // 标签定位任务的执行逻辑将在DispatchCenter::processLabelLocateTask中实现
        if (!promise_) {
            return;
        }
        
        LabelLocateResult res;
        res.success = false;
        res.errorMessage = "LabelLocateTask execute method should not be called directly";
        promise_->set_value(res);
    }

    // Getters for parameters
    bool isUseRoi() const { return useRoi_; }
    cv::Rect getRoi() const { return roi_; }
    const std::string& getTargetText() const { return targetText_; }
    int getSliceNumber() const { return sliceNumber_; }
    const std::string& getScriptContent() const { return scriptContent_; }
    std::shared_ptr<std::promise<LabelLocateResult>> getPromise() { return promise_; }

private:
    bool useRoi_;                    // 是否使用ROI进行OCR识别
    cv::Rect roi_;                   // ROI矩形区域（仅当useRoi_为true时使用）
    std::string targetText_;         // 目标文本（仅当useRoi_为false时使用）
    int sliceNumber_;                // 切片号
    std::string scriptContent_;      // 脚本内容，用于搜索标签
    std::shared_ptr<std::promise<LabelLocateResult>> promise_;
};

#endif // VMTASKS_H