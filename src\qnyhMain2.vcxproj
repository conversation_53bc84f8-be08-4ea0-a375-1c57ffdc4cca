﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{267E8E2B-EAF6-3358-90DC-FB512B84A7E8}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>qnyhMain2</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\visualStudio\project\qnyhMain2\src\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">qnyhMain2.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">qnyhMain2</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\visualStudio\project\qnyhMain2\src\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">qnyhMain2.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">qnyhMain2</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\visualStudio\project\qnyhMain2\src\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">qnyhMain2.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">qnyhMain2</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\visualStudio\project\qnyhMain2\src\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">qnyhMain2.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">qnyhMain2</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\visualStudio\project\qnyhMain2\src;D:\visualStudio\project\qnyhMain2\extern\cjson;D:\libvncserver\include;D:\libvncserver\include\rfb;D:\libvncserver\build\include;D:\vcpkg\installed\x64-windows\include;D:\visualStudio\project\qnyhMain2\extern\paddleocr_v1\include;D:\opencv\build\include;D:\visualStudio\project\qnyhMain2\extern\imgui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_UNICODE;UNICODE;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_UNICODE;UNICODE;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\visualStudio\project\qnyhMain2\src;D:\visualStudio\project\qnyhMain2\extern\cjson;D:\libvncserver\include;D:\libvncserver\include\rfb;D:\libvncserver\build\include;D:\vcpkg\installed\x64-windows\include;D:\visualStudio\project\qnyhMain2\extern\paddleocr_v1\include;D:\opencv\build\include;D:\visualStudio\project\qnyhMain2\extern\imgui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\visualStudio\project\qnyhMain2\src;D:\visualStudio\project\qnyhMain2\extern\cjson;D:\libvncserver\include;D:\libvncserver\include\rfb;D:\libvncserver\build\include;D:\vcpkg\installed\x64-windows\include;D:\visualStudio\project\qnyhMain2\extern\paddleocr_v1\include;D:\opencv\build\include;D:\visualStudio\project\qnyhMain2\extern\imgui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\Debug\imgui.lib;d3d11.lib;dxgi.lib;vncclient.lib;ws2_32.lib;zlib.lib;winhttp.lib;D:\opencv\build\x64\vc16\lib\opencv_world4100.lib;PaddleOCR.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>D:/libvncserver/build/Release;D:/libvncserver/build/Release/$(Configuration);D:/vcpkg/installed/x64-windows/lib;D:/vcpkg/installed/x64-windows/lib/$(Configuration);D:/visualStudio/project/qnyhMain2/lib;D:/visualStudio/project/qnyhMain2/lib/$(Configuration);D:/opencv/build/x64/vc16/lib;D:/opencv/build/x64/vc16/lib/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/visualStudio/project/qnyhMain2/src/Debug/qnyhMain2.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/visualStudio/project/qnyhMain2/src/Debug/qnyhMain2.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\visualStudio\project\qnyhMain2\src;D:\visualStudio\project\qnyhMain2\extern\cjson;D:\libvncserver\include;D:\libvncserver\include\rfb;D:\libvncserver\build\include;D:\vcpkg\installed\x64-windows\include;D:\visualStudio\project\qnyhMain2\extern\paddleocr_v1\include;D:\opencv\build\include;D:\visualStudio\project\qnyhMain2\extern\imgui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_UNICODE;UNICODE;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_UNICODE;UNICODE;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\visualStudio\project\qnyhMain2\src;D:\visualStudio\project\qnyhMain2\extern\cjson;D:\libvncserver\include;D:\libvncserver\include\rfb;D:\libvncserver\build\include;D:\vcpkg\installed\x64-windows\include;D:\visualStudio\project\qnyhMain2\extern\paddleocr_v1\include;D:\opencv\build\include;D:\visualStudio\project\qnyhMain2\extern\imgui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\visualStudio\project\qnyhMain2\src;D:\visualStudio\project\qnyhMain2\extern\cjson;D:\libvncserver\include;D:\libvncserver\include\rfb;D:\libvncserver\build\include;D:\vcpkg\installed\x64-windows\include;D:\visualStudio\project\qnyhMain2\extern\paddleocr_v1\include;D:\opencv\build\include;D:\visualStudio\project\qnyhMain2\extern\imgui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\Release\imgui.lib;d3d11.lib;dxgi.lib;vncclient.lib;ws2_32.lib;zlib.lib;winhttp.lib;D:\opencv\build\x64\vc16\lib\opencv_world4100.lib;PaddleOCR.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>D:/libvncserver/build/Release;D:/libvncserver/build/Release/$(Configuration);D:/vcpkg/installed/x64-windows/lib;D:/vcpkg/installed/x64-windows/lib/$(Configuration);D:/visualStudio/project/qnyhMain2/lib;D:/visualStudio/project/qnyhMain2/lib/$(Configuration);D:/opencv/build/x64/vc16/lib;D:/opencv/build/x64/vc16/lib/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/visualStudio/project/qnyhMain2/src/Release/qnyhMain2.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/visualStudio/project/qnyhMain2/src/Release/qnyhMain2.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\visualStudio\project\qnyhMain2\src;D:\visualStudio\project\qnyhMain2\extern\cjson;D:\libvncserver\include;D:\libvncserver\include\rfb;D:\libvncserver\build\include;D:\vcpkg\installed\x64-windows\include;D:\visualStudio\project\qnyhMain2\extern\paddleocr_v1\include;D:\opencv\build\include;D:\visualStudio\project\qnyhMain2\extern\imgui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_UNICODE;UNICODE;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_UNICODE;UNICODE;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\visualStudio\project\qnyhMain2\src;D:\visualStudio\project\qnyhMain2\extern\cjson;D:\libvncserver\include;D:\libvncserver\include\rfb;D:\libvncserver\build\include;D:\vcpkg\installed\x64-windows\include;D:\visualStudio\project\qnyhMain2\extern\paddleocr_v1\include;D:\opencv\build\include;D:\visualStudio\project\qnyhMain2\extern\imgui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\visualStudio\project\qnyhMain2\src;D:\visualStudio\project\qnyhMain2\extern\cjson;D:\libvncserver\include;D:\libvncserver\include\rfb;D:\libvncserver\build\include;D:\vcpkg\installed\x64-windows\include;D:\visualStudio\project\qnyhMain2\extern\paddleocr_v1\include;D:\opencv\build\include;D:\visualStudio\project\qnyhMain2\extern\imgui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\MinSizeRel\imgui.lib;d3d11.lib;dxgi.lib;vncclient.lib;ws2_32.lib;zlib.lib;winhttp.lib;D:\opencv\build\x64\vc16\lib\opencv_world4100.lib;PaddleOCR.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>D:/libvncserver/build/Release;D:/libvncserver/build/Release/$(Configuration);D:/vcpkg/installed/x64-windows/lib;D:/vcpkg/installed/x64-windows/lib/$(Configuration);D:/visualStudio/project/qnyhMain2/lib;D:/visualStudio/project/qnyhMain2/lib/$(Configuration);D:/opencv/build/x64/vc16/lib;D:/opencv/build/x64/vc16/lib/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/visualStudio/project/qnyhMain2/src/MinSizeRel/qnyhMain2.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/visualStudio/project/qnyhMain2/src/MinSizeRel/qnyhMain2.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\visualStudio\project\qnyhMain2\src;D:\visualStudio\project\qnyhMain2\extern\cjson;D:\libvncserver\include;D:\libvncserver\include\rfb;D:\libvncserver\build\include;D:\vcpkg\installed\x64-windows\include;D:\visualStudio\project\qnyhMain2\extern\paddleocr_v1\include;D:\opencv\build\include;D:\visualStudio\project\qnyhMain2\extern\imgui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_UNICODE;UNICODE;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_UNICODE;UNICODE;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\visualStudio\project\qnyhMain2\src;D:\visualStudio\project\qnyhMain2\extern\cjson;D:\libvncserver\include;D:\libvncserver\include\rfb;D:\libvncserver\build\include;D:\vcpkg\installed\x64-windows\include;D:\visualStudio\project\qnyhMain2\extern\paddleocr_v1\include;D:\opencv\build\include;D:\visualStudio\project\qnyhMain2\extern\imgui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\visualStudio\project\qnyhMain2\src;D:\visualStudio\project\qnyhMain2\extern\cjson;D:\libvncserver\include;D:\libvncserver\include\rfb;D:\libvncserver\build\include;D:\vcpkg\installed\x64-windows\include;D:\visualStudio\project\qnyhMain2\extern\paddleocr_v1\include;D:\opencv\build\include;D:\visualStudio\project\qnyhMain2\extern\imgui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\RelWithDebInfo\imgui.lib;d3d11.lib;dxgi.lib;vncclient.lib;ws2_32.lib;zlib.lib;winhttp.lib;D:\opencv\build\x64\vc16\lib\opencv_world4100.lib;PaddleOCR.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>D:/libvncserver/build/Release;D:/libvncserver/build/Release/$(Configuration);D:/vcpkg/installed/x64-windows/lib;D:/vcpkg/installed/x64-windows/lib/$(Configuration);D:/visualStudio/project/qnyhMain2/lib;D:/visualStudio/project/qnyhMain2/lib/$(Configuration);D:/opencv/build/x64/vc16/lib;D:/opencv/build/x64/vc16/lib/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/visualStudio/project/qnyhMain2/src/RelWithDebInfo/qnyhMain2.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/visualStudio/project/qnyhMain2/src/RelWithDebInfo/qnyhMain2.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\visualStudio\project\qnyhMain2\src\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/visualStudio/project/qnyhMain2/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/visualStudio/project/qnyhMain2 -BD:/visualStudio/project/qnyhMain2 --check-stamp-file D:/visualStudio/project/qnyhMain2/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\visualStudio\project\qnyhMain2\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/visualStudio/project/qnyhMain2/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/visualStudio/project/qnyhMain2 -BD:/visualStudio/project/qnyhMain2 --check-stamp-file D:/visualStudio/project/qnyhMain2/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\visualStudio\project\qnyhMain2\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/visualStudio/project/qnyhMain2/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/visualStudio/project/qnyhMain2 -BD:/visualStudio/project/qnyhMain2 --check-stamp-file D:/visualStudio/project/qnyhMain2/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\visualStudio\project\qnyhMain2\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/visualStudio/project/qnyhMain2/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/visualStudio/project/qnyhMain2 -BD:/visualStudio/project/qnyhMain2 --check-stamp-file D:/visualStudio/project/qnyhMain2/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\visualStudio\project\qnyhMain2\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Core\TaskManager.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Core\VersionChecker.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Core\fwindows\find_window.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\LoadingManager.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\centerpanel\centerconsole.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\consolelog\consolelog.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\hardwareinfo\hardwareinfo.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\mainui.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\scripteditor\scripteditor.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\systeminfo\systeminfo.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\tasklist\tasklist.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\visionconfig\visionconfig.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Logic\common.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Vnc\DispatchCenter.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Vnc\MouseBoardCode.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Vnc\ScreenFrameDistributor.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Vnc\VMTasks.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Vnc\VMVision.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Vnc\VNCControl.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Vnc\VncMain.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\config\ConfigLoader.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\config\TemplatePathManager.cpp" />
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\main.cpp" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Core\VersionChecker.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Core\fwindows\find_window.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Core\thread_queue\threadsafequeue.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\LoadingManager.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\centerpanel\centerconsole.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\consolelog\consolelog.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\hardwareinfo\hardwareinfo.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\mainui.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\scripteditor\scripteditor.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\systeminfo\systeminfo.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\tasklist\tasklist.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Logic\common.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Vnc\DispatchCenter.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Vnc\MouseBoardCode.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Vnc\ScreenFrameDistributor.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Vnc\VMTasks.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Vnc\VMVision.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Vnc\VNCControl.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Vnc\VncMain.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Vnc\threadsafequeue.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\config\ConfigLoader.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\config\LRUCache.h" />
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\config\TemplatePathManager.h" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\visualStudio\project\qnyhMain2\ZERO_CHECK.vcxproj">
      <Project>{5D28FE65-2C62-3B1E-904B-B52BE717B658}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\visualStudio\project\qnyhMain2\imgui.vcxproj">
      <Project>{73D0C064-B0FF-3617-802D-B47B0AAF6715}</Project>
      <Name>imgui</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>