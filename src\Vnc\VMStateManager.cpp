#include "VMStateManager.h"
#include "../GUI/consolelog/consolelog.h"
#include <chrono>

// 静态成员初始化
VMStateManager* VMStateManager::instance_ = nullptr;
std::mutex VMStateManager::instanceMutex_;

// 全局连接状态变量
namespace {
    bool g_connecting = false;
    bool g_connected = false;
    std::vector<std::string> g_availableVMs;
    bool g_vmsConnected = false;
}

VMStateManager* VMStateManager::getInstance() {
    // 双重检查锁定模式，确保线程安全
    if (instance_ == nullptr) {
        std::lock_guard<std::mutex> lock(instanceMutex_);
        if (instance_ == nullptr) {
            try {
                instance_ = new VMStateManager();
                if (!instance_) {
                    throw std::runtime_error("VMStateManager创建失败");
                }
            } catch (const std::exception& e) {
                AddLogInfo(LogLevel::Error, "[VMStateManager] 创建实例时发生异常: " + std::string(e.what()));
                return nullptr;
            } catch (...) {
                AddLogInfo(LogLevel::Error, "[VMStateManager] 创建实例时发生未知异常");
                return nullptr;
            }
        }
    }
    return instance_;
}

// ===============================
// VM基本状态管理
// ===============================

void VMStateManager::setVMConnectionState(const std::string& vmName, VMConnectionState state) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    if (vmState) {
        vmState->connectionState = state;
    }
}

VMConnectionState VMStateManager::getVMConnectionState(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    return vmState ? vmState->connectionState : VMConnectionState::DISCONNECTED_STATE;
}

void VMStateManager::setVMTaskState(const std::string& vmName, VMTaskState state) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    if (vmState) {
        vmState->taskState = state;
    }
}

VMTaskState VMStateManager::getVMTaskState(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    return vmState ? vmState->taskState : VMTaskState::IDLE_STATE;
}

// ===============================
// 脚本执行状态管理
// ===============================

void VMStateManager::setScriptRunning(const std::string& vmName, bool running) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    if (vmState) {
        vmState->isScriptRunning = running;
    }
}

bool VMStateManager::isScriptRunning(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    return vmState ? vmState->isScriptRunning : false;
}

void VMStateManager::setScriptPaused(const std::string& vmName, bool paused) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    if (vmState) {
        vmState->isScriptPaused = paused;
    }
}

bool VMStateManager::isScriptPaused(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    return vmState ? vmState->isScriptPaused : false;
}

void VMStateManager::setCurrentScriptLine(const std::string& vmName, int lineNumber) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    if (vmState) {
        vmState->currentScriptLine = lineNumber;
    }
}

int VMStateManager::getCurrentScriptLine(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    return vmState ? vmState->currentScriptLine : 0;
}

void VMStateManager::setTotalScriptLines(const std::string& vmName, int totalLines) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    if (vmState) {
        vmState->totalScriptLines = totalLines;
    }
}

int VMStateManager::getTotalScriptLines(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    return vmState ? vmState->totalScriptLines : 0;
}

void VMStateManager::setCurrentSliceForScript(const std::string& vmName, int sliceNumber) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    if (vmState) {
        vmState->currentSliceForScript = sliceNumber;
    }
}

int VMStateManager::getCurrentSliceForScript(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    return vmState ? vmState->currentSliceForScript : -1;
}

// ===============================
// 切片状态管理
// ===============================

void VMStateManager::setSliceState(const std::string& vmName, int sliceNumber, SliceState state) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    SliceStateInfo* sliceState = getSliceStatePtr(vmName, sliceNumber);
    if (sliceState) {
        sliceState->state = state;
        sliceState->lastStateChange = std::chrono::steady_clock::now();
    }
}

SliceState VMStateManager::getSliceState(const std::string& vmName, int sliceNumber) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    SliceStateInfo* sliceState = getSliceStatePtr(vmName, sliceNumber);
    return sliceState ? sliceState->state : SliceState::IDLE_STATE;
}

void VMStateManager::setSliceTaskQueueSize(const std::string& vmName, int sliceNumber, int queueSize) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    SliceStateInfo* sliceState = getSliceStatePtr(vmName, sliceNumber);
    if (sliceState) {
        sliceState->taskQueueSize = queueSize;
    }
}

int VMStateManager::getSliceTaskQueueSize(const std::string& vmName, int sliceNumber) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    SliceStateInfo* sliceState = getSliceStatePtr(vmName, sliceNumber);
    return sliceState ? sliceState->taskQueueSize : 0;
}

void VMStateManager::setSliceError(const std::string& vmName, int sliceNumber, const std::string& error) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    SliceStateInfo* sliceState = getSliceStatePtr(vmName, sliceNumber);
    if (sliceState) {
        sliceState->lastError = error;
    }
}

std::string VMStateManager::getSliceError(const std::string& vmName, int sliceNumber) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    SliceStateInfo* sliceState = getSliceStatePtr(vmName, sliceNumber);
    return sliceState ? sliceState->lastError : "";
}

// ===============================
// 任务轮询器状态管理
// ===============================

void VMStateManager::setPollerRunning(const std::string& vmName, bool running) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    if (vmState) {
        vmState->isPollerRunning = running;
    }
}

bool VMStateManager::isPollerRunning(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    return vmState ? vmState->isPollerRunning : false;
}

void VMStateManager::setPollerPaused(const std::string& vmName, bool paused) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    if (vmState) {
        vmState->isPollerPaused = paused;
    }
}

bool VMStateManager::isPollerPaused(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    return vmState ? vmState->isPollerPaused : false;
}

// ===============================
// 错误管理
// ===============================

void VMStateManager::setLastError(const std::string& vmName, const std::string& error) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    if (vmState) {
        vmState->lastError = error;
        vmState->lastErrorTime = std::chrono::steady_clock::now();
    }
}

std::string VMStateManager::getLastError(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    return vmState ? vmState->lastError : "";
}

// ===============================
// 统计信息
// ===============================

void VMStateManager::incrementTasksExecuted(const std::string& vmName) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    if (vmState) {
        vmState->totalTasksExecuted++;
    }
}

void VMStateManager::incrementTasksFailed(const std::string& vmName) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    if (vmState) {
        vmState->totalTasksFailed++;
    }
}

int VMStateManager::getTotalTasksExecuted(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    return vmState ? vmState->totalTasksExecuted : 0;
}

int VMStateManager::getTotalTasksFailed(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    return vmState ? vmState->totalTasksFailed : 0;
}

// ===============================
// 便利方法
// ===============================

bool VMStateManager::isVMConnected(const std::string& vmName) const {
    return getVMConnectionState(vmName) == VMConnectionState::CONNECTED_STATE;
}

bool VMStateManager::isSlicePaused(const std::string& vmName, int sliceNumber) const {
    return getSliceState(vmName, sliceNumber) == SliceState::PAUSED_STATE;
}

bool VMStateManager::isSliceRunning(const std::string& vmName, int sliceNumber) const {
    return getSliceState(vmName, sliceNumber) == SliceState::RUNNING_STATE;
}

bool VMStateManager::isSliceWaiting(const std::string& vmName, int sliceNumber) const {
    SliceState state = getSliceState(vmName, sliceNumber);
    return state == SliceState::WAITING_SCREEN_STILL_STATE || state == SliceState::WAITING_VISUAL_MATCH_STATE;
}

bool VMStateManager::isSliceWaitingForScreenStill(const std::string& vmName, int sliceNumber) const {
    return getSliceState(vmName, sliceNumber) == SliceState::WAITING_SCREEN_STILL_STATE;
}

bool VMStateManager::isSliceWaitingForVisualMatch(const std::string& vmName, int sliceNumber) const {
    return getSliceState(vmName, sliceNumber) == SliceState::WAITING_VISUAL_MATCH_STATE;
}

std::map<int, SliceState> VMStateManager::getAllSliceStates(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    std::map<int, SliceState> result;
    
    VMState* vmState = getVMStatePtr(vmName);
    if (vmState) {
        for (const auto& pair : vmState->sliceStates) {
            result[pair.first] = pair.second.state;
        }
    }
    
    return result;
}

VMState VMStateManager::getVMState(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    return vmState ? *vmState : VMState();
}

// ===============================
// 初始化和清理
// ===============================

void VMStateManager::initializeVM(const std::string& vmName) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    
    if (vmStates_.find(vmName) == vmStates_.end()) {
        vmStates_[vmName] = std::make_unique<VMState>();
    }
}

void VMStateManager::removeVM(const std::string& vmName) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    
    auto it = vmStates_.find(vmName);
    if (it != vmStates_.end()) {
        vmStates_.erase(it);
        AddLogInfo(LogLevel::Info, "[VMStateManager] 移除VM状态: " + vmName);
    }
}

void VMStateManager::clear() {
    std::lock_guard<std::mutex> lock(statesMutex_);
    vmStates_.clear();
    AddLogInfo(LogLevel::Info, "[VMStateManager] 清空所有VM状态");
}

// ===============================
// 内部辅助方法
// ===============================

VMState* VMStateManager::getVMStatePtr(const std::string& vmName) const {
    auto it = vmStates_.find(vmName);
    if (it != vmStates_.end()) {
        return it->second.get();
    }
    
    // 自动创建VM状态（延迟初始化）
    // 注意：这里需要去掉const限制来修改map
    VMStateManager* nonConstThis = const_cast<VMStateManager*>(this);
    nonConstThis->vmStates_[vmName] = std::make_unique<VMState>();
    return nonConstThis->vmStates_[vmName].get();
}

SliceStateInfo* VMStateManager::getSliceStatePtr(const std::string& vmName, int sliceNumber) const {
    VMState* vmState = getVMStatePtr(vmName);
    if (!vmState) {
        return nullptr;
    }
    
    auto it = vmState->sliceStates.find(sliceNumber);
    if (it != vmState->sliceStates.end()) {
        return &it->second;
    }
    
    // 自动创建切片状态（延迟初始化）
    // vmState已经是非const的，可以直接修改
    vmState->sliceStates[sliceNumber] = SliceStateInfo();
    return &vmState->sliceStates[sliceNumber];
}

void VMStateManager::setConnecting(bool connecting) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    g_connecting = connecting;
}

bool VMStateManager::isConnecting() const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    return g_connecting;
}

void VMStateManager::setConnected(bool connected) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    g_connected = connected;
}

bool VMStateManager::isConnected() const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    return g_connected;
}

void VMStateManager::setAvailableVMs(const std::vector<std::string>& vms) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    g_availableVMs = vms;
}

std::vector<std::string> VMStateManager::getAvailableVMs() const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    return g_availableVMs;
}

void VMStateManager::setVMsConnected(bool connected) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    g_vmsConnected = connected;
}

bool VMStateManager::getVMsConnected() const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    return g_vmsConnected;
}

void VMStateManager::setUIState(const std::string& vmName, VMUIState state) {
    std::lock_guard<std::mutex> lock(statesMutex_);
    VMState* vmState = getVMStatePtr(vmName);
    if (vmState) {
        vmState->uiState = state;
    }
}

VMUIState VMStateManager::getUIState(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(statesMutex_);
    const VMState* vmState = getVMStatePtr(vmName);
    if (vmState) {
        return vmState->uiState;
    }
    return VMUIState::DISCONNECTED;
} 