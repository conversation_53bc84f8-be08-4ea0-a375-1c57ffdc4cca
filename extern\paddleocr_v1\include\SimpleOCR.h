#pragma once

#include <string>
#include <vector>
#include <memory>
#include <opencv2/opencv.hpp>
#include "utility.h" // 包含OCRPredictResult定义

#ifdef _WIN32
#  ifdef PADDLEOCR_DLL_EXPORTS
#    define PADDLEOCR_API __declspec(dllexport)
#  else
#    define PADDLEOCR_API __declspec(dllimport)
#  endif
#else
#  define PADDLEOCR_API
#endif

namespace PaddleOCR {

// 为OCRPredictResult添加获取中心点的辅助方法
inline cv::Point GetCenterPoint(const OCRPredictResult& result) {
    if (result.box.empty() || result.box[0].size() < 2) {
        return cv::Point(0, 0);
    }
    
    // 找出边界框的最小和最大坐标
    int min_x = INT_MAX, min_y = INT_MAX;
    int max_x = INT_MIN, max_y = INT_MIN;
    
    for (const auto& point : result.box) {
        if (point.size() >= 2) {
            min_x = std::min(min_x, point[0]);
            min_y = std::min(min_y, point[1]);
            max_x = std::max(max_x, point[0]);
            max_y = std::max(max_y, point[1]);
        }
    }
    
    // 计算边界框的中心
    return cv::Point((min_x + max_x) / 2, (min_y + max_y) / 2);
}

// Main SimpleOCR class with minimal interface
class PADDLEOCR_API SimpleOCR {
public:
    // 参数配置索引
    enum ParamConfigIndex {
        CONFIG_HIGH_PRECISION = 0, // 高精度配置
        CONFIG_HIGH_RECALL,       // 高召回率配置
        CONFIG_HYBRID,            // 高精度-高召回率混合配置
        CONFIG_COUNT              // 配置总数
    };
    
    // Constructor with model paths and parameter configuration
    SimpleOCR(
        const std::string& det_model_dir = "models/ch_PP-OCRv4_det_infer",
        const std::string& rec_model_dir = "models/ch_PP-OCRv4_rec_infer",
        const std::string& dict_path = "models/ppocr_keys_v1.txt",
        bool use_gpu = false,
        int gpu_id = 0,
        ParamConfigIndex config_index = CONFIG_HIGH_PRECISION
    );
    
    // Destructor
    ~SimpleOCR();
    
    // 设置参数配置
    void setParamConfig(ParamConfigIndex config_index);
    
    // 获取当前参数配置名称
    std::string getCurrentConfigName() const;
    
    // 识别图像中的所有文本
    std::vector<OCRPredictResult> recognize(const cv::Mat& image);
    
    // 查找图像中的特定文本，返回中心点和置信度
    // 如果未找到文本，则返回Point(0,0)和置信度0
    std::pair<cv::Point, float> findText(const cv::Mat& image, const std::string& text);
    
private:
    // Private implementation details
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

} // namespace PaddleOCR
