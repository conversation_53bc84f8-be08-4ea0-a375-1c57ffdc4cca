//地名与坐标1110,30,170,25
//任务详情栏35,270,365,470
//中间任务执行栏470,270,350,480
//任务栏1050,400,230,200
//右下角一键装备栏830,600,320,300
// ========== 虚拟机1：测试切片1 ==========
slice(1) {
	LABEL_LOCATE(1050,400,230,200) 
	##1级任务
	
	LABEL:燃犀之灯
	TEXT_MATCH(燃犀之灯, left, 1050, 400, 230, 200,0.5)
	TEXT_MATCH(燃犀之灯, left, 470,270,350,480,0.5)
	DELAY(3000)
	TEXT_MATCH(燃犀之灯, left, 1050, 400, 230, 200,0.5)
	TEXT_MATCH(燃犀之灯, left, 470,270,350,480,0.5)
	MOUSE_MOVE(650, 910)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:红月之照
	TEXT_MATCH(红月之照, left, 1050, 400, 230, 200,0.5)
	TEXT_MATCH(红月之照, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f)
	
	LABEL:月下织娘
	TEXT_MATCH(月下织娘, left, 1050, 400, 230, 200,0.5)
	TEXT_MATCH(月下织娘, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	DELAY(10000)

	LABEL:蒲家村落
	TEXT_MATCH(蒲家村落, left, 1050, 400, 230, 200,0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,10)
	TEXT_MATCH(蒲家村落, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:厨娘小宝
	TEXT_MATCH(一键装备, left, 830,600,320,300, 0.7) 
	TEXT_MATCH(厨娘小宝, left, 1050, 400, 230, 200,0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,60)
	TEXT_MATCH(厨娘小宝, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:木匠爷爷
	TEXT_MATCH(木匠爷爷, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,30)
	TEXT_MATCH(木匠爷爷, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:失控木人
	TEXT_MATCH(失控木人, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,30)
	TEXT_MATCH(失控木人, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(tab,2,tab,3,tab,2,tab,2,tab,2,tab,2,tab,2,tab,2,tab,2,tab,2,tab,2,tab,3)
	DELAY(3000)
	TEXT_MATCH(失控木人, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,30)
	TEXT_MATCH(失控木人, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:木花城
	TEXT_MATCH(木花城, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,30)
	TEXT_MATCH(木花城, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:掩藏秘密
	TEXT_MATCH(掩藏秘密, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,30)
	TEXT_MATCH(掩藏秘密, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	

	LABEL:姑娘
	TEXT_MATCH(姑娘, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,30)
	TEXT_MATCH(姑娘, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(一键装备, left, 830,600,320,300, 0.7) 
	TEXT_MATCH(姑娘, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,60)
	TEXT_MATCH(姑娘, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:花田扑蝶
	TEXT_MATCH(花田扑蝶, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,60)
	TITLEBAR_KEYBOARD_INPUT(tab,2,tab,3,tab,2,tab,2,tab,2,tab,2,tab,2,tab,2,tab,2,tab,2,tab,2,tab,3)
	DELAY(3000)
	TEXT_MATCH(花田扑蝶, left, 470,270,350,480,0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,60)
	TEXT_MATCH(姑娘, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:小妖
	TEXT_MATCH(小妖, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,30)
	TEXT_MATCH(小妖, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:猫儿偷鸡
	TEXT_MATCH(猫儿偷鸡, left, 1050, 400, 230, 200,0.5)	
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,60)
	TEXT_MATCH(猫儿偷鸡, left, 1050, 400, 230, 200,0.5)	
	VISUAL_MATCH(lingxi,right)
	DELAY(3000)
	TEXT_MATCH(猫儿偷鸡, left, 1050, 400, 230, 200,0.5)	
	TEXT_MATCH(猫儿偷鸡, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:小鸡哒哒
	TEXT_MATCH(小鸡哒哒, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,30)
	TEXT_MATCH(小鸡哒哒, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:灵兽出战
	TITLEBAR_KEYBOARD_INPUT(p)
	VISUAL_MATCH(chuzhan,left)
	TITLEBAR_KEYBOARD_INPUT(p)
	TEXT_MATCH(灵兽出战, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(灵兽出战, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(p)
	VISUAL_MATCH(xiuxi,left)
	TITLEBAR_KEYBOARD_INPUT(p)
	TEXT_MATCH(灵兽出战, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(灵兽出战, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:意外失踪
	TEXT_MATCH(意外失踪, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(意外失踪, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(意外失踪, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,60)
	TEXT_MATCH(意外失踪, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(意外失踪, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:狐妖阿纤
	TEXT_MATCH(狐妖阿纤, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(狐妖阿纤, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(狐妖阿纤, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:小蜘蛛娘
	TEXT_MATCH(小蜘蛛娘, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,60)
	TEXT_MATCH(小蜘蛛娘, left, 470,270,350,480,0.5)
	TEXT_MATCH(小蜘蛛娘, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,60)
	TEXT_MATCH(小蜘蛛娘, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:祸之试
	TEXT_MATCH(祸之试, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(祸之试, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:蛛丝
	TEXT_MATCH(蛛丝, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,120)
	TEXT_MATCH(蛛丝, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(蛛丝, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(蛛丝, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	//动画加载，到蛛丝洞
	LABEL:丝洞探秘
	TEXT_MATCH(丝洞探秘, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,120)
	//动画加载，进洞
	TEXT_MATCH(蛛母, left, 830,600,320,300, 0.5) 
	TITLEBAR_KEYBOARD_INPUT(2,3)
	DELAY(60000)
	//动画加载
	TITLEBAR_KEYBOARD_INPUT(tab,2,tab,3)
	TITLEBAR_KEYBOARD_INPUT(tab,2,tab,3)
	TITLEBAR_KEYBOARD_INPUT(tab,2,tab,3)
	TITLEBAR_KEYBOARD_INPUT(tab,2,tab,3)
	TITLEBAR_KEYBOARD_INPUT(tab,2,tab,3)
	TITLEBAR_KEYBOARD_INPUT(tab,2,tab,3)
	TEXT_MATCH(丝洞探秘, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(丝洞探秘, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:沉睡蛛
	TEXT_MATCH(沉睡蛛, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,60)
	TEXT_MATCH(沉睡蛛, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:聊斋
	TEXT_MATCH(聊斋, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(聊斋, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f)
	TEXT_MATCH(聊斋, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(聊斋, left, 470,270,350,480,0.5)

	TEXT_MATCH(聊斋, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(聊斋, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	//加载动画
	TEXT_MATCH(聊斋, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(聊斋, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:妖灵之城
	TEXT_MATCH(妖灵之城, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(妖灵之城, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(妖灵之城, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,120)
	TEXT_MATCH(妖灵之城, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:大隐于市
	TEXT_MATCH(大隐于市, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,120)
	TEXT_MATCH(大隐于市, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(大隐于市, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	//载入动画
	DELAY(10000)
	LABEL:集市之乐
	TEXT_MATCH(集市之乐, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(集市之乐, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	DELAY(40000)
	
	LABEL:出来了
	TEXT_MATCH(出来了, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(出来了, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:天狐相救
	TEXT_MATCH(天狐相救, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(天狐相救, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:何以为报
	TEXT_MATCH(何以为报, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,120)
	TEXT_MATCH(何以为报, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:青丘之国
	TEXT_MATCH(青丘之国, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(青丘之国, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:狐嫁之宴
	TEXT_MATCH(狐嫁之宴, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(狐嫁之宴, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:镜像新娘
	TEXT_MATCH(镜像新娘, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(镜像新娘, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	 LABEL:新郎子固
	TEXT_MATCH(新郎子固, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,20)
	TEXT_MATCH(新郎子固, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:偷虫之
	TEXT_MATCH(偷虫之, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,20)
	TEXT_MATCH(偷虫之, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:打后相识
	TEXT_MATCH(打后相识, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(打后相识, left, 470,270,350,480,0.5)
	TEXT_MATCH(进入青丘一, left, 470,270,350,480,0.5)
	//加载动画
	DELAY(10000)
	TEXT_MATCH(打后相识, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,60)
	TITLEBAR_KEYBOARD_INPUT(tab,2,3)
	DELAY(60000)
	TEXT_MATCH(打后相识, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,60)
	TEXT_MATCH(我要离开, left, 470,270,350,480,0.5)
	DELAY(5000)
	TEXT_MATCH(打后相识, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:神犬之
	TEXT_MATCH(神犬之, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,20)
	TEXT_MATCH(神犬之, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	DELAY(5000)
	
	LABEL:狐之思慕
	TEXT_MATCH(狐之思慕, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(狐之思慕, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(狐之思慕, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(狐之思慕, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:阿绣入册
	TEXT_MATCH(阿绣入册, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(阿绣入册, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(阿绣入册, left, 830,600,320,300, 0.5) 
	
	LABEL:掌门之唤
	TEXT_MATCH(掌门之唤, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,90)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,30)
	TEXT_MATCH(掌门之唤, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(一键装备, left, 830,600,320,300, 0.7) 

	LABEL:拜入师门
	TEXT_MATCH(拜入师门, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(拜入师门, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:独门秘籍
	TEXT_MATCH(独门秘籍, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(独门秘籍, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	//练功与召唤灵伴

	LABEL:功力大增
	TEXT_MATCH(功力大增, left, 830,600,320,300, 0.5) 
	//升级技能
	TEXT_MATCH(功力大增, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	
	LABEL:恩门相助
	TEXT_MATCH(恩门相助, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(恩门相助, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)


	LABEL:不安异象
	TEXT_MATCH(不安异象, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TITLEBAR_KEYBOARD_INPUT(tab,2,tab,2,tab,2)
	TITLEBAR_KEYBOARD_INPUT(tab,2,tab,2,tab,2)
	TEXT_MATCH(不安异象, left, 830,600,320,300, 0.5)
	AIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(不安异象, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(一键装备, left, 830,600,320,300, 0.7) 
	
	LABEL:回禀掌门
	TEXT_MATCH(回禀掌门, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(回禀掌门, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:再度出发
	TEXT_MATCH(再度出发, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(再度出发, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:口舌相争
	TEXT_MATCH(口舌相争, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(口舌相争, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:崔家阿猛逢采臣
	TEXT_MATCH(崔家阿猛逢采臣, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(崔家阿猛逢采臣, left, 470,270,350,480,0.5)
	TEXT_MATCH(崔猛家, left, 470,270,350,480,0.5)
	//加载动画
	TEXT_MATCH(崔家阿猛逢采臣, left, 830,600,320,300, 0.5) 
	DELAY(1000)
	TITLEBAR_KEYBOARD_INPUT(tab,2,tab,3)
	TITLEBAR_KEYBOARD_INPUT(tab,2,tab,3)
	DELAY(10000)
	TEXT_MATCH(崔家阿猛逢采臣, left, 830,600,320,300, 0.5) 
	DELAY(10000)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:富不仁
	TEXT_MATCH(富不仁, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(富不仁, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:蹊跷命案
	TEXT_MATCH(蹊跷命案, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(蹊跷命案, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:前往拜访
	TEXT_MATCH(前往拜访, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(前往拜访, left, 470,270,350,480,0.5)
	//加载动画
	DELAY(10000)
	
	LABEL:怕猫小妇
	TEXT_MATCH(怕猫小妇, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(怕猫小妇, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(怕猫小妇, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(怕猫小妇, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:抠门首富
	TEXT_MATCH(抠门首富, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(抠门首富, left, 470,270,350,480,0.5)
	//回答问题
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:求救之声
	TEXT_MATCH(求救之声, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(求救之声, left, 470,270,350,480,0.5)
	TEXT_MATCH(求救之声, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(求救之声, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:阿舒是鼠
	TEXT_MATCH(阿舒是鼠, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(阿舒是鼠, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(阿舒是鼠, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:小小笨妖
	TEXT_MATCH(小小笨妖, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(小小笨妖, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:身体缩小
	TEXT_MATCH(身体缩小, left, 830,600,320,300, 0.5) 
	//载入动画
	DELAY(20000)
	TEXT_MATCH(身体缩小, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(身体缩小, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:方寸洞天
	TEXT_MATCH(方寸洞天, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(方寸洞天, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:小小宅妖斗瓢虫
	TEXT_MATCH(镇中鼠, left, 470,270,350,480,0.5)
	//自动寻路
	TEXT_MATCH(小小宅妖斗瓢虫, left, 470,270,350,480,0.5)
	TEXT_MATCH(镇中鼠, left, 470,270,350,480,0.5)
	TEXT_MATCH(小小宅妖斗瓢虫, left, 830,600,320,300, 0.5)
	TITLEBAR_KEYBOARD_INPUT(tab,2,3)
	//打瓢虫
	DELAY(20000)
	TEXT_MATCH(小小宅妖斗瓢虫, left, 830,600,320,300, 0.5)
	TEXT_MATCH(小小宅妖斗瓢虫, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:底下小车
	TEXT_MATCH(底下小车, left, 830,600,320,300, 0.5)
	DELAY(20000)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(底下小车, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:睡鼠小弟
	TEXT_MATCH(睡鼠小弟, left, 830,600,320,300, 0.5)
	//捡松果
	TEXT_MATCH(睡鼠小弟, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,3,120)
	TEXT_MATCH(睡鼠小弟, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:苞米之恩
	TEXT_MATCH(苞米之恩, left, 830,600,320,300, 0.5) 
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(苞米之恩, left, 830,600,320,300, 0.5) 
	DELAY(10000)
	TEXT_MATCH(苞米之恩, left, 830,600,320,300, 0.5) 
	TEXT_MATCH(苞米之恩, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:恢复大小
	TEXT_MATCH(恢复大小, left, 830,600,320,300, 0.5)
	DELAY(1000)
	TEXT_MATCH(恢复大小, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:傅贵有悔
	TEXT_MATCH(傅贵有悔, left, 830,600,320,300, 0.5)
	TEXT_MATCH(傅贵有悔, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:千金散席
	TEXT_MATCH(千金散席, left, 830,600,320,300, 0.5)
	TEXT_MATCH(千金散席, left, 470,270,350,480,0.5)
	//商城买药
	TEXT_MATCH(千金散席, left, 830,600,320,300, 0.5)
	TEXT_MATCH(千金散席, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:迷雾泛起
	TEXT_MATCH(迷雾泛起, left, 830,600,320,300, 0.5)
	TEXT_MATCH(迷雾泛起, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(迷雾泛起, left, 830,600,320,300, 0.5)
	TEXT_MATCH(迷雾泛起, left, 470,270,350,480,0.5)
	//选择
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:前往金陵
	TEXT_MATCH(前往金陵, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(前往金陵, left, 470,270,350,480,0.5)
	//载入动画
	TEXT_MATCH(前往金陵, left, 830,600,320,300, 0.5)
	TEXT_MATCH(前往金陵, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:天犬之主
	TEXT_MATCH(天犬之主, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(天犬之主, left, 470,270,350,480,0.5)
	TEXT_MATCH(天犬之主, left, 830,600,320,300, 0.5)
	TEXT_MATCH(天犬之主, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	//载入动画

	LABEL:魅香楼中
	TEXT_MATCH(魅香楼中, left, 830,600,320,300, 0.5)
	TEXT_MATCH(魅香楼中, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:香儿老板
	TEXT_MATCH(香儿老板, left, 830,600,320,300, 0.5)
	TEXT_MATCH(香儿老板, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:多番打听
	TEXT_MATCH(王夫人, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(多番打听, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(抱月, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(多番打听, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(多番打听, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(多番打听, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:何处哀弦
	TEXT_MATCH(琴弦, left, 830,600,320,300, 0.5)
	TEXT_MATCH(何处哀弦, left, 470,270,350,480,0.5)
	TEXT_MATCH(何处哀弦, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(何处哀弦, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
 
	LABEL:小小圆圈
	TEXT_MATCH(小小圆圈, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(小小圆圈, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(小小圆圈, left, 830,600,320,300, 0.5)
	TEXT_MATCH(小小圆圈, left, 830,600,320,300, 0.5)
	TEXT_MATCH(小小圆圈, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)  
	//手动操作琴弦

	LABEL:琵琶弦上
	TEXT_MATCH(琵琶弦上, left, 830,600,320,300, 0.5)
	TEXT_MATCH(琵琶弦上, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:油壁香车
	TEXT_MATCH(油壁香车, left, 830,600,320,300, 0.5)
	TEXT_MATCH(油壁香车, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:伤势有别
	TEXT_MATCH(伤势有别, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(伤势有别, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)	

	LABEL:事发推测
	TEXT_MATCH(事发推测, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	//换东西
	TEXT_MATCH(事发推测, left, 830,600,320,300, 0.5)
	TEXT_MATCH(事发推测, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)	

	LABEL:贾天师乃假天师
	TEXT_MATCH(贾天师乃假天师, left, 830,600,320,300, 0.5)
	TEXT_MATCH(贾天师乃假天师, left, 470,270,350,480,0.5)
	TEXT_MATCH(贾天师, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(tab,2,tab,3)
	TEXT_MATCH(贾天师乃假天师, left, 830,600,320,300, 0.5)
	TEXT_MATCH(贾天师乃假天师, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)	
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)	

	LABEL:告别小圆
	TEXT_MATCH(告别小圆, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(告别小圆, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:临别相赠
	TEXT_MATCH(临别相赠, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	//载入动画
	TEXT_MATCH(临别相赠, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	//载入动画
	TEXT_MATCH(一键装备, left, 830,600,320,300, 0.7) 

	LABEL:西冷芳华
	TEXT_MATCH(西冷芳华, left, 830,600,320,300, 0.5)
	TEXT_MATCH(西冷芳华, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(西冷芳华, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(西冷芳华, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:指兰若
	TEXT_MATCH(指兰若, left, 830,600,320,300, 0.5)
	TEXT_MATCH(指兰若, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(指兰若, left, 830,600,320,300, 0.5)
	TEXT_MATCH(指兰若, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:追至兰若
	TEXT_MATCH(追至兰若, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(追至兰若, left, 470,270,350,480,0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(追至兰若, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	DELAY(1000)

	LABEL:初入内殿
	TEXT_MATCH(初入内殿, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(初入内殿, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:睡前故事
	TEXT_MATCH(睡前故事, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(睡前故事, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:吃饱喝足
	TEXT_MATCH(吃饱喝足, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(吃饱喝足, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:交替守夜
	TEXT_MATCH(交替守夜, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(交替守夜, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	//载入动画
	TEXT_MATCH(交替守夜, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(交替守夜, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	//载入动画

	LABEL:丽影初现
	TEXT_MATCH(丽影初现, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(丽影初现, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:孤女无援
	TEXT_MATCH(孤女无援, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(孤女无援, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(孤女无援, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(孤女无援, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:谋生之法
	TEXT_MATCH(谋生之法, left, 830,600,320,300, 0.5)
	TEXT_MATCH(谋生之法, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(缝缝补补的旧衣服, left, 830,600,320,300, 0.5)
	TEXT_MATCH(谋生之法, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(药草篮子, left, 830,600,320,300, 0.5)
	TEXT_MATCH(谋生之法, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(医药书, left, 830,600,320,300, 0.5)
	TEXT_MATCH(谋生之法, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(账本, left, 830,600,320,300, 0.5)
	TEXT_MATCH(谋生之法, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(谋生之法, left, 830,600,320,300, 0.5)
	TEXT_MATCH(谋生之法, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:善人善心
	TEXT_MATCH(善人善心, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(善人善心, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:梦醒归来
	TEXT_MATCH(梦醒归来, left, 830,600,320,300, 0.5)
	TEXT_MATCH(梦醒归来, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:兰若月下倩影柔
	TEXT_MATCH(兰若月下倩影柔, left, 830,600,320,300, 0.5)
	TEXT_MATCH(兰若月下倩影柔, left, 470,270,350,480,0.5)
	TEXT_MATCH(迎战紫衣女子, left, 470,270,350,480,0.5)
	//载入动画
 	TEXT_MATCH(兰若月下倩影柔, left, 830,600,320,300, 0.5)
	TITLEBAR_KEYBOARD_INPUT(tab,2,3)
	//打怪
	TEXT_MATCH(兰若月下倩影柔, left, 830,600,320,300, 0.5)
	TEXT_MATCH(兰若月下倩影柔, left, 470,270,350,480,0.5)
 	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:遗留线索
	TEXT_MATCH(遗留线索, left, 830,600,320,300, 0.5)
	TEXT_MATCH(拾取铃铛, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(遗留线索, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(遗留线索, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:行鬼城
	TEXT_MATCH(行鬼城, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(行鬼城, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:初至寻踪
	TEXT_MATCH(初至寻踪, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(初至寻踪, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(初至寻踪, left, 830,600,320,300, 0.5)
	TEXT_MATCH(初至寻踪, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	//载入动画

	LABEL:收账之行
	TEXT_MATCH(收账之行, left, 830,600,320,300, 0.5)
	TEXT_MATCH(收账之行, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(收账之行, left, 830,600,320,300, 0.5)
	TEXT_MATCH(收账之行, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:再寻倩影
	TEXT_MATCH(再寻倩影, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(再寻倩影, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	//升级
	TEXT_MATCH(再寻倩影, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(再寻倩影, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:重逢之旅
	TEXT_MATCH(重逢之旅, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(重逢之旅, left, 830,600,320,300, 0.5)
	TEXT_MATCH(重逢之旅, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:又见汪汪
	TEXT_MATCH(又见汪汪, left, 830,600,320,300, 0.5)
	TEXT_MATCH(又见汪汪, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:陡生变故
	TEXT_MATCH(陡生变故, left, 830,600,320,300, 0.5)
	TEXT_MATCH(陡生变故, left, 470,270,350,480,0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(陡生变故, left, 830,600,320,300, 0.5)
	TITLEBAR_KEYBOARD_INPUT(tab,2,3)
	//打小倩
	TEXT_MATCH(陡生变故, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(陡生变故, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)

	LABEL:地牢探查
	TEXT_MATCH(地牢探查, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(地牢探查, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(地牢探查, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(应该可以防身, left, 470,270,350,480,0.5)
	TEXT_MATCH(地牢探查, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(地牢探查, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:援军相助
	TEXT_MATCH(援军相助, left, 830,600,320,300, 0.5)
	TEXT_MATCH(援军相助, left, 470,270,350,480,0.5)
	//动画
	TEXT_MATCH(援军相助, left, 830,600,320,300, 0.5)
	TEXT_MATCH(援军相助, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(援军相助, left, 830,600,320,300, 0.5)
	TEXT_MATCH(援军相助, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:抓住希望
	TEXT_MATCH(抓住希望, left, 830,600,320,300, 0.5)
	TEXT_MATCH(抓住希望, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:逃脱地牢
	TEXT_MATCH(逃脱地牢, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(逃脱地牢, left, 470,270,350,480,0.5)
	TEXT_MATCH(逃脱地牢, left, 830,600,320,300, 0.5)
	TEXT_MATCH(逃脱地牢, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	//升级

	LABEL:三人汇合
	TEXT_MATCH(三人汇合, left, 830,600,320,300, 0.5)
	TEXT_MATCH(三人汇合, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:拦路双妖
	TEXT_MATCH(拦路双妖, left, 830,600,320,300, 0.5)
	TEXT_MATCH(拦路双妖, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:坟地往事
	TEXT_MATCH(坟地往事, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(坟地往事, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(坟地往事, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:命运相逢
	TEXT_MATCH(命运相逢, left, 830,600,320,300, 0.5)
	TEXT_MATCH(命运相逢, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	DELAY(1000)

	LABEL:孤魂归处
	TEXT_MATCH(孤魂归处, left, 830,600,320,300, 0.5)
	TEXT_MATCH(孤魂归处, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:兰若永回
	TEXT_MATCH(兰若永回, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(兰若永回, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(兰若永回, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(兰若永回, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:童年往事
	TEXT_MATCH(童年往事, left, 830,600,320,300, 0.5)
	TEXT_MATCH(童年往事, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:秘密回忆
	TEXT_MATCH(秘密回忆, left, 830,600,320,300, 0.5)
	TEXT_MATCH(秘密回忆, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:心情记录
	TEXT_MATCH(心情记录, left, 830,600,320,300, 0.5)
	TEXT_MATCH(打开木匣, left, 470,270,350,480,0.5)
	//操作
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(心情记录, left, 830,600,320,300, 0.5)
	TEXT_MATCH(心情记录, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:原来是你
	TEXT_MATCH(原来是你, left, 830,600,320,300, 0.5)
	TEXT_MATCH(原来是你, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:破局之眼
	TEXT_MATCH(破局之眼, left, 830,600,320,300, 0.5)
	TEXT_MATCH(破局之眼, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:双生孩童
	TEXT_MATCH(双生孩童, left, 830,600,320,300, 0.5)
	TEXT_MATCH(双生孩童, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	//升级

	LABEL:食梦二妖织幻梦
	TEXT_MATCH(食梦二妖织幻梦, left, 830,600,320,300, 0.5)
	TEXT_MATCH(食梦二妖织幻梦, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(食梦二妖织幻梦, left, 830,600,320,300, 0.5)
	TEXT_MATCH(食梦二妖织幻梦, left, 470,270,350,480,0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(食梦二妖织幻梦, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TITLEBAR_KEYBOARD_INPUT(tab,2.3)
	//打双妖
	TEXT_MATCH(食梦二妖织幻梦, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(食梦二妖织幻梦, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:逃离梦境
	TEXT_MATCH(逃离梦境, left, 830,600,320,300, 0.5)
	TEXT_MATCH(逃离梦境, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	//动画

	LABEL:观战姥姥
	TEXT_MATCH(观战姥姥, left, 830,600,320,300, 0.5)
	TEXT_MATCH(逃离梦境, left, 470,270,350,480,0.5)
	
	LABEL:挑战姥姥
	TEXT_MATCH(挑战姥姥, left, 470,270,350,480,0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(挑战姥姥, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TITLEBAR_KEYBOARD_INPUT(tab,2,3)
	//打姥姥
	//升级
	TEXT_MATCH(挑战姥姥, left, 830,600,320,300, 0.5)
	TEXT_MATCH(挑战姥姥, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:决心救人
	TEXT_MATCH(决心救人, left, 830,600,320,300, 0.5)
	TEXT_MATCH(决心救人, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(决心救人, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	//载入画面
	TEXT_MATCH(决心救人, left, 470,270,350,480,0.5) 
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:安眠之夜
	TEXT_MATCH(小蝶, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(安眠之夜, left, 470,270,350,480,0.5)
	TEXT_MATCH(燕赤霞, left, 470,270,350,480,0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(安眠之夜, left, 470,270,350,480,0.5)
	TEXT_MATCH(安眠之夜, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(安眠之夜, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	
	LABEL:孤身犯险
	TEXT_MATCH(孤身犯险, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(孤身犯险, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(孤身犯险, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(孤身犯险, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:营救行动
	TEXT_MATCH(营救行动, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(营救行动, left, 470,270,350,480,0.5)
	TEXT_MATCH(营救行动, left, 830,600,320,300, 0.5)
	TEXT_MATCH(营救行动, left, 470,270,350,480,0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(营救行动, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:再入兰若
	TEXT_MATCH(再入兰若, left, 830,600,320,300, 0.5)
	TEXT_MATCH(再入兰若, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(再入兰若, left, 830,600,320,300, 0.5)
	TEXT_MATCH(再入兰若, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(再入兰若, left, 830,600,320,300, 0.5)
	TEXT_MATCH(再入兰若, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(再入兰若, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(再入兰若, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:兰若盛世
	TEXT_MATCH(兰若盛世, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	//扔骰子，选择
	TEXT_MATCH(兰若盛世, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(兰若盛世, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	//载入画面
	
	LABEL:探入虎穴
	TEXT_MATCH(探入虎穴, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(探入虎穴, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:危机四伏
	TEXT_MATCH(危机四伏, left, 830,600,320,300, 0.5)
	TEXT_MATCH(挑战小鬼, left, 470,270,350,480,0.5)
	//四个方向小鬼出兵挑战
	TEXT_MATCH(危机四伏, left, 830,600,320,300, 0.5)
	TEXT_MATCH(危机四伏, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:一波又起
	TEXT_MATCH(一波又起, left, 830,600,320,300, 0.5)
	TEXT_MATCH(一波又起, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	//升级

	LABEL:深入幻境
	TEXT_MATCH(深入幻境, left, 830,600,320,300, 0.5)
	TEXT_MATCH(深入幻境, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:双目失辉
	TEXT_MATCH(双目失辉, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(双目失辉, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(双目失辉, left, 830,600,320,300, 0.5)
	TEXT_MATCH(双目失辉, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:佛像引路
	TEXT_MATCH(佛像引路, left, 830,600,320,300, 0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(佛像引路, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:三探佛像
	TEXT_MATCH(三探佛像, left, 830,600,320,300, 0.5)
	TEXT_MATCH(三探佛像, left, 470,270,350,480,0.5)
	TEXT_MATCH(三探佛像, left, 830,600,320,300, 0.5)
	TEXT_MATCH(三探佛像, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:三座佛像
	TEXT_MATCH(三座佛像, left, 830,600,320,300, 0.5)
	TEXT_MATCH(三座佛像, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:无感流失
	TEXT_MATCH(无感流失, left, 830,600,320,300, 0.5)
	TEXT_MATCH(无感流失, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:绝境沐心
	TEXT_MATCH(绝境沐心, left, 830,600,320,300, 0.5)
	TEXT_MATCH(绝境沐心, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:乾坤互换
	TEXT_MATCH(乾坤互换, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(乾坤互换, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	//载入动画
	TEXT_MATCH(乾坤互换, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(乾坤互换, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:重见天日
	TEXT_MATCH(重见天日, left, 830,600,320,300, 0.5)
	TEXT_MATCH(重见天日, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:血之抉择
	TEXT_MATCH(血之抉择, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(血之抉择, left, 470,270,350,480,0.5)
	//升级
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:再别倩容
	TEXT_MATCH(再别倩容, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	//捡起石头，操作
	TEXT_MATCH(再别倩容, left, 470,270,350,480,0.5)


	LABEL:脱离幻境
	TEXT_MATCH(脱离幻境, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(脱离幻境, left, 470,270,350,480,0.5)
	TEXT_MATCH(脱离幻境, left, 830,600,320,300, 0.5)
	TEXT_MATCH(脱离幻境, left, 470,270,350,480,0.5)

	LABEL:命悬一线
	TEXT_MATCH(命悬一线, left, 830,600,320,300, 0.5)
	TEXT_MATCH(命悬一线, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(一键装备, left, 830,600,320,300, 0.7) 

	LABEL:回程休息
	TEXT_MATCH(回程休息, left, 830,600,320,300, 0.5)
	TEXT_MATCH(回程休息, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	//载入动画

	LABEL:从长计议
	TEXT_MATCH(从长计议, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(从长计议, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	//升级

	LABEL:打探虚空
	TEXT_MATCH(打探虚空, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	//选择
	TEXT_MATCH(打探虚空, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:黑风佚事
	TEXT_MATCH(黑风佚事, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(黑风佚事, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,120)
	TEXT_MATCH(黑风佚事, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:返回
	TEXT_MATCH(返回, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,240)
	TEXT_MATCH(返回, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,240)
	TEXT_MATCH(返回, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,240)
	TEXT_MATCH(返回, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:整顿出发
	TEXT_MATCH(整顿出发, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,240)
	TEXT_MATCH(整顿出发, left, 830,600,320,300, 0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)

	LABEL:小心打探
	TEXT_MATCH(小心打探, left, 830,600,320,300, 0.5)
	TEXT_MATCH(小心打探, left, 830,600,320,300, 0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(小心打探, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,240)
	TEXT_MATCH(小心打探, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(小心打探, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,240)
	TEXT_MATCH(小心打探, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(小心打探, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,240)
	TEXT_MATCH(小心打探, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TEXT_MATCH(小心打探, left, 830,600,320,300, 0.5)
	WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,240)
	TEXT_MATCH(小心打探, left, 470,270,350,480,0.5)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f,f,f)


}


// ========== 虚拟机1：测试切片2 ==========  
slice(2) {
    # 这些任务应该与切片1并行执行（因为是不同切片）
}