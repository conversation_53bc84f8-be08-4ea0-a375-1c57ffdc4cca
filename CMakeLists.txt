cmake_minimum_required(VERSION 3.15)

# MSVC 热重载支持
if (POLICY CMP0141)
    cmake_policy(SET CMP0141 NEW)
    set(CMAKE_MSVC_DEBUG_INFORMATION_FORMAT "$<IF:$<AND:$<C_COMPILER_ID:MSVC>,$<CXX_COMPILER_ID:MSVC>>,$<$<CONFIG:Debug,RelWithDebInfo>:EditAndContinue>,$<$<CONFIG:Debug,RelWithDebInfo>:ProgramDatabase>>")
endif()
add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/utf-8>")
project(qnyhMain2 LANGUAGES CXX)

# 编译器设置
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
#取消调试控制台
set(CMAKE_WIN32_EXECUTABLE ON)
# ImGui 配置
set(IMGUI_DIR "${CMAKE_SOURCE_DIR}/extern/imgui")

# 添加 ImGui 源文件
set(IMGUI_SOURCES
    ${IMGUI_DIR}/imgui.cpp
    ${IMGUI_DIR}/imgui_demo.cpp
    ${IMGUI_DIR}/imgui_draw.cpp
    ${IMGUI_DIR}/imgui_tables.cpp
    ${IMGUI_DIR}/imgui_widgets.cpp
    ${IMGUI_DIR}/imgui_impl_win32.cpp
    ${IMGUI_DIR}/imgui_impl_dx11.cpp
    ${IMGUI_DIR}/imgui_stdlib.cpp
)

# 设置 ImGui 包含目录
set(IMGUI_INCLUDE_DIRS
    ${IMGUI_DIR}
)

# 添加 ImGui 静态库
add_library(imgui STATIC ${IMGUI_SOURCES})
target_include_directories(imgui PUBLIC ${IMGUI_INCLUDE_DIRS})

# 添加 ImGui 静态库
set(CJSON_DIR "${CMAKE_SOURCE_DIR}/extern/cjson")


#全局设
#include_directories(${CMAKE_SOURCE_DIR}/extern/cjson)

# 添加子目录
add_subdirectory(src)