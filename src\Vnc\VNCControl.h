#pragma once
#include <rfb/rfbclient.h>
#include <string>
#include <memory>

enum class InputType { MOUSE_MOVE, MOUSE_LEFT_CLICK, MOUSE_RIGHT_CLICK, KEYBOARD, DRAGMOUSE };

struct InputTask {
    InputType type;
    int x, y;        // 对于鼠标操作
    int key;         // 对于键盘操作
    // 添加构造函数，支持初始化列表推导
    InputTask(InputType t, int xVal = 0, int yVal = 0, int k = 0)
        : type(t), x(xVal), y(yVal), key(k) {
    }
    // 添加默认构造函数
    InputTask() : type(InputType::MOUSE_MOVE), x(0), y(0), key(0) {}
};

// VNCControl class definition
class VNCControl {
private:
    std::string ip;
    int port;
    std::string password;
    std::shared_ptr<rfbClient> client;
    bool connected;

public:
    // Constructor
    VNCControl(const std::string& ip, int port, const std::string& password);
    
    // Destructor
    ~VNCControl();
    
    // Connection methods
    bool connectToServer();
    void disconnect();
    bool isConnected() const;
    
    // Client access
    std::shared_ptr<rfbClient> getRfbClient() const;
    
    // Input methods
    void sendInput(const InputTask& task);
};

// 初始化拟人化模拟库，应在程序入口调用一次
void initHumanLikeSimulation();
void handleInput(rfbClient* client, const InputTask& task);