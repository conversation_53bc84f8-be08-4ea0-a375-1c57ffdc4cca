#define NOMINMAX  // 防止 Windows 头文件定义 min/max 宏，与 std::min/max 冲突

#include "VNCControl.h" // 引入 VNC 控制相关的头文件，具体内容依赖于你的 VNC 库
#include "VncMain.h"    // 引入 VncMain.h 以访问全局状态变量
#include "Core/MemoryUtils.h" // 引入现代内存管理工具
#include "Core/ConfigManager.h" // 引入配置管理器
#include "VMStateManager.h" // 引入状态管理器
#include <thread>       // 引入线程库，用于实现延迟和睡眠
#include <chrono>       // 引入时间库，用于处理时间间隔
#include <cmath>        // 引入数学库
#include <cstdlib>      // 引入标准库，用于 std::abs
#include <ctime>        // 引入时间库，用于获取时间作为随机数种子
#include <random>       // 引入新的随机数库
#include <utility>      // 引入 std::pair
#include <algorithm>    // 引入 std::min (现在不会冲突了)
#include <GUI/consolelog/consolelog.h>
#include <rfb/rfbclient.h> // Added for RFB_SCREEN_TYPE and other rfb types
#include <cstring> // 用于 memset
#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <unistd.h>
    #include <fcntl.h>
    #include <errno.h>
#endif
// 使用 std::chrono 命名空间下的毫秒单位
using namespace std::chrono;

// VNCControl implementation
VNCControl::VNCControl(const std::string& ip, int port, const std::string& password)
    : ip(ip), port(port), password(password), client(nullptr), connected(false) {
    
}

VNCControl::~VNCControl() {
    // Destructor implementation
    disconnect();
    // 删除：Destroyed VNC control 日志
}

bool VNCControl::connectToServer() {
    if (connected && client) {
        return true; // Already connected
    }
    
    // 预检测端口连接性
    try {
        // 使用简单的socket连接测试端口是否可用
        #ifdef _WIN32
            WSADATA wsaData;
            if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
                AddLogInfo(LogLevel::Error, "[VNCControl] WSAStartup 初始化失败");
                VMStateManager::getInstance()->setConnected(false);
                return false;
            }
        #endif
        
        int testSocket = socket(AF_INET, SOCK_STREAM, 0);
        if (testSocket == -1) {
            AddLogInfo(LogLevel::Error, "[VNCControl] 创建测试套接字失败");
            #ifdef _WIN32
                WSACleanup();
            #endif
            VMStateManager::getInstance()->setConnected(false);
            return false;
        }
        
        // 设置非阻塞模式和超时
        #ifdef _WIN32
            u_long mode = 1;
            ioctlsocket(testSocket, FIONBIO, &mode);
        #else
            int flags = fcntl(testSocket, F_GETFL, 0);
            fcntl(testSocket, F_SETFL, flags | O_NONBLOCK);
        #endif
        
        struct sockaddr_in serverAddr;
        memset(&serverAddr, 0, sizeof(serverAddr));
        serverAddr.sin_family = AF_INET;
        serverAddr.sin_port = htons(port);
        
        // 解析IP地址
        if (inet_pton(AF_INET, ip.c_str(), &serverAddr.sin_addr) <= 0) {
            #ifdef _WIN32
                closesocket(testSocket);
                WSACleanup();
            #else
                close(testSocket);
            #endif
            VMStateManager::getInstance()->setConnected(false);
            return false;
        }
        
        // 尝试连接
        int connectResult = connect(testSocket, (struct sockaddr*)&serverAddr, sizeof(serverAddr));
        
        if (connectResult == -1) {
            #ifdef _WIN32
                int error = WSAGetLastError();
                if (error == WSAEWOULDBLOCK) {
            #else
                if (errno == EINPROGRESS) {
            #endif
                    // 连接正在进行中，等待完成
                    fd_set writeSet;
                    FD_ZERO(&writeSet);
                    FD_SET(testSocket, &writeSet);
                    
                    // 从配置获取超时时间
                    auto& configManager = ConfigManager::getInstance();
                    int timeoutMs = configManager.getConfig<int>("network.vnc_timeout", 5000);
                    
                    struct timeval timeout;
                    timeout.tv_sec = timeoutMs / 1000;  // 秒
                    timeout.tv_usec = (timeoutMs % 1000) * 1000; // 微秒
                    
                    int selectResult = select(testSocket + 1, NULL, &writeSet, NULL, &timeout);
                    if (selectResult <= 0) {
                        AddLogInfo(LogLevel::Error, "[VNCControl] 端口连接超时或失败，虚拟机未开机或防火墙未允许:" + std::to_string(port));
                        #ifdef _WIN32
                            closesocket(testSocket);
                            WSACleanup();
                        #else
                            close(testSocket);
                        #endif
                        VMStateManager::getInstance()->setConnected(false);
                        return false;
                    }
                    
                    // 检查连接是否真的成功
                    int socketError;
                    socklen_t len = sizeof(socketError);
                    if (getsockopt(testSocket, SOL_SOCKET, SO_ERROR, (char*)&socketError, &len) < 0 || socketError != 0) {
                        AddLogInfo(LogLevel::Error, "[VNCControl] 端口连接失败，虚拟机未开机或防火墙未允许:" + std::to_string(port));
                        #ifdef _WIN32
                            closesocket(testSocket);
                            WSACleanup();
                        #else
                            close(testSocket);
                        #endif
                        VMStateManager::getInstance()->setConnected(false);
                        return false;
                    }
                } else {
                    AddLogInfo(LogLevel::Error, "[VNCControl] 端口连接立即失败，虚拟机未开机或防火墙未允许:" + std::to_string(port));
                    #ifdef _WIN32
                        closesocket(testSocket);
                        WSACleanup();
                    #else
                        close(testSocket);
                    #endif
                    VMStateManager::getInstance()->setConnected(false);
                    return false;
                }
        }
        
        // 连接成功，关闭测试套接字
        #ifdef _WIN32
            closesocket(testSocket);
            WSACleanup();
        #else
            close(testSocket);
        #endif
        
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[VNCControl] 端口预检测异常: " + std::string(e.what()));
        VMStateManager::getInstance()->setConnected(false);
        return false;
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[VNCControl] 端口预检测发生未知异常");
        VMStateManager::getInstance()->setConnected(false);
        return false;
    }
    
    // 初始化VNC客户端，参数分别是：bitsPerPixel, samplesPerPixel, bytesPerPixel
    // 32位RGB颜色（8位每个样本，3个样本，4字节每个像素）
    try {
        rfbClient* rawClient = rfbGetClient(8, 3, 4);
        if (!rawClient) {
            AddLogInfo(LogLevel::Error, "[VNCControl] 失敗創建RFB client");
            VMStateManager::getInstance()->setConnected(false);
            return false;
        }
        client = std::shared_ptr<rfbClient>(rawClient, [](rfbClient* ptr) {
            if (ptr) rfbClientCleanup(ptr);
        });
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[VNCControl] 创建RFB client时发生异常: " + std::string(e.what()));
        VMStateManager::getInstance()->setConnected(false);
        return false;
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[VNCControl] 创建RFB client时发生未知异常");
        VMStateManager::getInstance()->setConnected(false);
        return false;
    }
    
    // 设置像素格式为 BGR 格式，确保与 OpenCV 兼容
    try {
        client->format.redShift = 16;
        client->format.greenShift = 8;
        client->format.blueShift = 0;
        client->format.redMax = 255;
        client->format.greenMax = 255;
        client->format.blueMax = 255;
        client->format.bitsPerPixel = 32; // VNC通常以32位传输，即使实际深度是24位，为了对齐和效率
        client->format.depth = 24; // 24位色深（8位每通道 * 3通道）
        client->format.trueColour = 1; // 使用真彩色

        // Set connection parameters
        //client->serverHost = strdup(ip.c_str());
        //if (!client->serverHost) { //如果 strdup 内存分配失败 (返回 nullptr)
        //    AddLogInfo(LogLevel::Error, "VM控制线程无法分配服务器主机内存: " );
        //    return false;
        //}
        //client->serverPort = port;
        //设置图像质量级别 (通常 0-9，9 最高)。
        client->appData.qualityLevel = 9;
        //设置客户端支持的编码方式列表，用空格分隔。这些编码用于图像数据压缩和传输。
        client->appData.encodingsString = "tight zrle ultra copyrect hextile zlib corre rre raw";
        //客户端是否能处理服务器帧缓冲区大小改变的通知
        client->canHandleNewFBSize = TRUE;
        client->connectTimeout = 5;  // 5秒超时
        
        // 启用 JPEG 压缩 (如果 "tight" 编码支持)
        client->appData.enableJPEG = TRUE;
        // 禁用 BGR233 像素格式 (一种低颜色深度的格式)
        client->appData.useBGR233 = FALSE;
        // 设置压缩级别为最高质量
        client->appData.compressLevel = 0;
        
        // 不设置空指针回调函数，使用默认值
        // 将当前 VNCControl 实例保存在 client->clientData 中，需要显式类型转换
        client->clientData = (rfbClientData*)this;
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[VNCControl] 设置客户端参数时发生异常: " + std::string(e.what()));
        if (client) {
            rfbClientCleanup(client.get());
            client.reset();
        }
        VMStateManager::getInstance()->setConnected(false);
        return false;
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[VNCControl] 设置客户端参数时发生未知异常");
        if (client) {
            rfbClientCleanup(client.get());
            client.reset();
        }
        VMStateManager::getInstance()->setConnected(false);
        return false;
    }
    
    // 在外层声明变量，以便在 try 块和 catch 块中都可以访问
    // 使用现代C++内存管理工具类进行RAII管理
    try {
        // 动态构建 host_and_port 字符串
        std::string host_port_str = ip + ":" + std::to_string(port);

        // 使用MemoryUtils工具创建智能C字符串 - 自动内存管理
        auto program_name_ptr = MemoryUtils::make_smart_cstring("VNCClient");
        auto host_port_ptr = MemoryUtils::make_smart_cstring(host_port_str);

        if (!program_name_ptr || !host_port_ptr) {
            AddLogInfo(LogLevel::Error, "VM控制线程无法分配内存");
            rfbClientCleanup(client.get());
            client.reset();
            VMStateManager::getInstance()->setConnected(false);
            return false;
        }

        // my_argv 必须以 nullptr 结尾
        char* my_argv_data[3] = {
            program_name_ptr.get(),
            host_port_ptr.get(),
            nullptr
        };
        int my_argc = 2;

        // 调用 rfbInitClient，它将解析 my_argv 中的主机信息并尝试连接
        if (!rfbInitClient(client.get(), &my_argc, my_argv_data)) {
            AddLogInfo(LogLevel::Error, "[VNCControl] 错误连接到VM虚拟机" + ip + ":" + std::to_string(port));
            // rfbInitClient 失败时会自行清理 client
            client.reset(); // 避免悬空指针
            VMStateManager::getInstance()->setConnected(false);
            return false;
        }
        
        // MemoryUtils智能指针自动释放内存，无需手动调用free
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "VM控制线程调用rfbInitClient时发生异常: " + std::string(e.what()));
        
        if (client) {
            rfbClientCleanup(client.get());
            client.reset();
        }
        VMStateManager::getInstance()->setConnected(false);
        return false;
    } catch (...) {
        AddLogInfo(LogLevel::Error, "VM控制线程调用rfbInitClient时发生未知异常");
        
        if (client) {
            rfbClientCleanup(client.get());
            client.reset();
        }
        VMStateManager::getInstance()->setConnected(false);
        return false;
    }

    connected = true;
    VMStateManager::getInstance()->setConnected(true);
    return true;
}

void VNCControl::disconnect() {
    if (client) {
        rfbClient* tempClient = client.get();
        // 解绑回调和 clientData，防止悬挂访问
        tempClient->GotFrameBufferUpdate = nullptr;
        tempClient->clientData = nullptr;
        client.reset(); // 释放智能指针，自动调用 rfbClientCleanup
        connected = false;
        AddLogInfo(LogLevel::Info, "[VNCControl] 已断开VNC server");
        VMStateManager::getInstance()->setConnected(false);
    }
}

bool VNCControl::isConnected() const {
    try {
        bool connectedState = connected;
        auto currentClient = client;
        return connectedState && currentClient != nullptr;
    } catch (...) {
        return false;
    }
}

std::shared_ptr<rfbClient> VNCControl::getRfbClient() const {
    if (connected && client) {
        return client;
    }
    return nullptr;
}

void VNCControl::sendInput(const InputTask& task) {
    if (!isConnected()) {
        AddLogInfo(LogLevel::Error, "[VNCControl] Cannot send input: not connected");
        return;
    }
    handleInput(client.get(), task);
}

// 存储拟人化操作的各种配置
struct HumanLikeConfig {
    // 鼠标移动配置
    int minMoveSteps = 40;       // 最小移动步数
    int maxMoveSteps = 60;       // 最大移动步数
    int moveJitterRange = 2;     // 鼠标移动时的随机抖动范围 (正负值)
    int moveSleepMinMs = 8;      // 鼠标移动每步之间的最小延迟 (毫秒)
    int moveSleepMaxMs = 20;     // 鼠标移动每步之间的最大延迟 (毫秒)
    int bezierControlPointOffset = 150; // 贝塞尔控制点相对于中间点的最大随机偏移
    int finalPosMinX = -15;  //鼠标移到的最终位置x，y坐标的最大值最小值范围
    int finalPosMaxX = 15;
    int finalPosMinY = -6;
    int finalPosMaxY = 6;

    // 点击配置
    int clickSleepMinMs = 30;    // 鼠标按下到释放的最小延迟 (毫秒)
    int clickSleepMaxMs = 80;    // 鼠标按下到释放的最大延迟 (毫秒)
    int postClickSleepMinMs = 60; // 点击完成后最小延迟 (毫秒)
    int postClickSleepMaxMs = 200;// 点击完成后最大延迟 (毫秒)

    // 键盘输入配置
    int keyPressMinMs = 40;      // 按键按下到释放的最小延迟 (毫秒)
    int keyPressMaxMs = 120;     // 按键按下到释放的最大延迟 (毫秒)
    int keyReleaseMinMs = 60;    // 按键释放后最小延迟 (毫秒)
    int keyReleaseMaxMs = 200;   // 按键释放后最大延迟 (毫秒)

    // 拖拽配置
    int dragStartHoldMinMs = 100; // 拖拽开始前按下鼠标的最小延迟 (毫秒)
    int dragStartHoldMaxMs = 200; // 拖拽开始前按下鼠标的最大延迟 (毫秒)
    int dragStepsMin = 40;       // 拖拽过程中的最小步数
    int dragStepsMax = 70;       // 拖拽过程中的最大步数
    int dragJitterRange = 3;     // 拖拽过程中的随机抖动范围 (正负值)
    int dragSleepMinMs = 5;      // 拖拽每步之间的最小延迟 (毫秒)
    int dragSleepMaxMs = 15;     // 拖拽每步之间的最大延迟 (毫秒)
    int dfinalPosMinX = -6;  //鼠标移到的最终位置x，y坐标的最大值最小值范围
    int dfinalPosMaxX = 6;
    int dfinalPosMinY = -6;
    int dfinalPosMaxY = 6;
};

// 匿名命名空间，用于限制内部变量和函数的可见范围，仅当前文件可见
namespace {
    // 记录当前鼠标的位置，全局变量，用于模拟鼠标从当前位置开始移动
    int currentX = 10, currentY = 10;

    // 随机数引擎，静态成员，确保只被初始化一次
    /*static std::mt19937 rng;*/
    static std::mt19937 rng(std::random_device{}());
    // 随机整数分布器
    static std::uniform_int_distribution<int>* int_dist = nullptr;
    // 随机实数分布器
    static std::uniform_real_distribution<double>* real_dist = nullptr;


    // 初始化随机数生成器，应在程序启动时调用一次
    void initRNG() {
        // 使用时间作为种子
        rng.seed(static_cast<unsigned int>(std::time(0)));
        // 初始化分布器，可以根据需要创建不同范围的分布器实例
        // 这里我们先不初始化具体范围，而是在需要时创建临时的分布器
        // 或者创建不同范围的静态分布器
        // 为了简化，我们让sleepRand和jitterPos内部创建分布器
        // 更优的方式是创建静态分布器或将分布器作为参数传递
    }

    // 随机延迟函数 (使用 <random> 库)
    // minMs: 最小延迟毫秒数
    // maxMs: 最大延迟毫秒数
    void sleepRand(int minMs, int maxMs) {
        // 创建一个在 [minMs, maxMs] 范围内的整数分布器
        std::uniform_int_distribution<int> dist(minMs, maxMs);
        // 生成随机延迟时间
        int duration = dist(rng);
        // 使当前线程睡眠指定的毫秒数
        std::this_thread::sleep_for(milliseconds(duration));
    }

    // 生成指定范围的随机整数 (使用 <random> 库)
    int randInt(int min, int max) {
        std::uniform_int_distribution<int> dist(min, max);
        return dist(rng);
    }

    // 生成指定范围的随机实数 (使用 <random> 库)
    double randDouble(double min, double max) {
        std::uniform_real_distribution<double> dist(min, max);
        return dist(rng);
    }


    // 三阶贝塞尔曲线计算函数
    // t: 插值参数，范围通常在 [0, 1]
    // p0: 起始点坐标
    // p1: 控制点1坐标
    // p2: 控制点2坐标
    // p3: 结束点坐标
    // 返回值: 在参数 t 处的曲线坐标
    double bezier(double t, double p0, double p1, double p2, double p3) {
        double u = 1.0 - t;
        double tt = t * t;
        double uu = u * u;
        double uuu = uu * u;
        double ttt = tt * t;

        double p = uuu * p0;        // (1-t)^3 * P0
        p += 3 * uu * t * p1;     // 3 * (1-t)^2 * t * P1
        p += 3 * u * tt * p2;     // 3 * (1-t) * t^2 * P2
        p += ttt * p3;            // t^3 * P3

        return p;
    }

    // 给定坐标添加随机抖动 (使用 <random> 库)
    // x: 原始 X 坐标
    // y: 原始 Y 坐标
    // range: 抖动的最大范围 (正负 range)
    // 返回值: 添加抖动后的新坐标对 {x, y}
    std::pair<int, int> jitterPos(double x, double y, int range) {
        return {
            // 将 double 坐标转换为 int，并添加范围内的随机整数抖动
            static_cast<int>(x + randInt(-range, range)),
            static_cast<int>(y + randInt(-range, range))
        };
    }

    // 自动生成贝塞尔曲线的两个控制点 (简单启发式方法)
    // p0x, p0y: 起始点坐标
    // p3x, p3y: 结束点坐标
    // offsetRange: 控制点相对于路径中间或端点的最大随机偏移范围
    // 返回值: 控制点1和控制点2的坐标对 {{p1x, p1y}, {p2x, p2y}}
    std::pair<std::pair<int, int>, std::pair<int, int>> generateControlPoints(int p0x, int p0y, int p3x, int p3y, int offsetRange) {
        // 中点
        int midX = (p0x + p3x) / 2;
        int midY = (p0y + p3y) / 2;

        // 计算起始点和结束点之间的距离
        double distance = std::sqrt(std::pow(p3x - p0x, 2) + std::pow(p3y - p0y, 2));

        // 根据距离调整控制点的偏移范围和位置，确保路径不会过于夸张或平直
        // 这是一个简化的方法，更复杂的模拟可能需要考虑速度、方向等
        int effectiveOffset = std::min(offsetRange, static_cast<int>(distance / 2 + 50)); // 偏移不超过距离的一半 + 50

        // 控制点1：倾向于靠近起始点和中点之间，并添加随机偏移
        int p1x = p0x + (midX - p0x) / 2 + randInt(-effectiveOffset, effectiveOffset);
        int p1y = p0y + (midY - p0y) / 2 + randInt(-effectiveOffset, effectiveOffset);

        // 控制点2：倾向于靠近结束点和中点之间，并添加随机偏移
        int p2x = p3x + (midX - p3x) / 2 + randInt(-effectiveOffset, effectiveOffset);
        int p2y = p3y + (midY - p3y) / 2 + randInt(-effectiveOffset, effectiveOffset);

        // 为了避免控制点过于接近，可以在需要时添加一些逻辑，
        // 例如确保 p1x 不接近 p2x 如果 x 方向移动较大等。
        // 这里为了简化，使用随机偏移，依赖概率来产生多样性。

        return { {p1x, p1y}, {p2x, p2y} };
    }
}

// 初始化拟人化模拟库，应在程序入口调用一次
void initHumanLikeSimulation() {
    initRNG(); // 初始化随机数生成器
}


// 拟人轨迹鼠标移动函数（使用三阶贝塞尔曲线+抖动）
// client: VNC 客户端结构体指针
// targetX: 目标 X 坐标
// targetY: 目标 Y 坐标
// config: 拟人化操作配置
void moveMouseHumanLike(rfbClient* client, int targetX, int targetY, const HumanLikeConfig& config) {
    // 随机确定移动步数
    int steps = randInt(config.minMoveSteps, config.maxMoveSteps);
    if (steps <= 0) steps = 1; // 至少一步

    // 生成三阶贝塞尔曲线的两个控制点
    auto controlPoints = generateControlPoints(currentX, currentY, targetX, targetY, config.bezierControlPointOffset);
    int control1X = controlPoints.first.first;
    int control1Y = controlPoints.first.second;
    int control2X = controlPoints.second.first;
    int control2Y = controlPoints.second.second;

    // 循环遍历贝塞尔曲线的每个点
    for (int i = 0; i <= steps; ++i) {
        // 计算当前步的插值参数 t，范围从 0 到 1
        double t = static_cast<double>(i) / steps;
        // 根据三阶贝塞尔曲线公式计算当前步的理想 X, Y 坐标
        double x = bezier(t, currentX, control1X, control2X, targetX);
        double y = bezier(t, currentY, control1Y, control2Y, targetY);

        // 给计算出的坐标添加随机抖动
        auto [jx, jy] = jitterPos(x, y, config.moveJitterRange);

        // 向 VNC 服务器发送鼠标移动事件（按钮状态为 0 表示没有按钮按下）
        SendPointerEvent(client, jx, jy, 0);
        

        // 在两步移动之间随机延迟
        sleepRand(config.moveSleepMinMs, config.moveSleepMaxMs);
    }

    // 确保最终位置是目标位置，消除累积误差，增加一点误差位置
    int  offsetX = randInt(config.finalPosMinX, config.finalPosMaxX);
    int  offsetY = randInt(config.finalPosMinY, config.finalPosMaxY);
    targetX += offsetX;
    targetY += offsetY;
    SendPointerEvent(client, targetX, targetY, 0);

    
    // 更新全局的当前鼠标位置为目标位置
    currentX = targetX;
    currentY = targetY;
}

// 模拟鼠标左键点击
// client: VNC 客户端结构体指针
// config: 拟人化操作配置
void mouseLeftClick(rfbClient* client, const HumanLikeConfig& config) {
    // 在当前鼠标位置发送左键按下事件 (按钮状态 1)
    SendPointerEvent(client, currentX, currentY, 1);
    // 延迟一段时间，模拟按住按键
    sleepRand(config.clickSleepMinMs, config.clickSleepMaxMs);
    // 在当前鼠标位置发送左键释放事件 (按钮状态 0)
    SendPointerEvent(client, currentX, currentY, 0);
    // 模拟点击后"思考"的延迟
    sleepRand(config.postClickSleepMinMs, config.postClickSleepMaxMs);
}

// 模拟鼠标右键点击
// client: VNC 客户端结构体指针
// config: 拟人化操作配置
void mouseRightClick(rfbClient* client, const HumanLikeConfig& config) {
    // 在当前鼠标位置发送右键按下事件 (按钮状态 4，根据 VNC 协议)
    SendPointerEvent(client, currentX, currentY, 4);
    // 延迟一段时间，模拟按住按键
    sleepRand(config.clickSleepMinMs, config.clickSleepMaxMs);
    // 在当前鼠标位置发送右键释放事件 (按钮状态 0)
    SendPointerEvent(client, currentX, currentY, 0);
    // 模拟点击后"思考"的延迟
    sleepRand(config.postClickSleepMinMs, config.postClickSleepMaxMs);
}

// 模拟类人键盘输入（按下并释放单个键）
// client: VNC 客户端结构体指针
// keysym: 键的符号标识 (例如，从 keysym.h 或类似的头文件获取)
// config: 拟人化操作配置
void pressKey(rfbClient* client, uint32_t keysym, const HumanLikeConfig& config) {
    // 检查是否为大写字母 (XK_A 到 XK_Z)
    if (keysym >= XK_A && keysym <= XK_Z) {
        // 大写字母：使用 Shift + 小写字母 组合
        uint32_t lowercaseKey = keysym + 32; // 转换为对应的小写字母
        
        AddLogInfo(LogLevel::Debug, "[VNCControl] 检测到大写字母，使用Shift组合: " + 
                   std::string(1, (char)(keysym)) + " -> Shift+" + std::string(1, (char)(lowercaseKey)));
        
        // 按下 Shift
        SendKeyEvent(client, XK_Shift_L, true);
        sleepRand(5, 15); // Shift 按下的短暂延迟
        
        // 按下小写字母
        SendKeyEvent(client, lowercaseKey, true);
        sleepRand(config.keyPressMinMs, config.keyPressMaxMs);
        
        // 释放小写字母
        SendKeyEvent(client, lowercaseKey, false);
        sleepRand(5, 15); // 按键间的短暂延迟
        
        // 释放 Shift
        SendKeyEvent(client, XK_Shift_L, false);
        sleepRand(config.keyReleaseMinMs, config.keyReleaseMaxMs);
    } else {
        // 其他按键：正常处理
        SendKeyEvent(client, keysym, true);
        sleepRand(config.keyPressMinMs, config.keyPressMaxMs);
        SendKeyEvent(client, keysym, false);
        sleepRand(config.keyReleaseMinMs, config.keyReleaseMaxMs);
    }
}
 
// 模拟鼠标拖拽操作
// client: VNC 客户端结构体指针
// startX, startY: 拖拽起点坐标
// endX, endY: 拖拽终点坐标
// config: 拟人化操作配置
void dragMouse(rfbClient* client, int endX, int endY, const HumanLikeConfig& config) {
    // 首先拟人化移动到拖拽的起始位置
    moveMouseHumanLike(client, currentX, currentY, config);

    // 在起始位置按下鼠标左键 (按钮状态 1)
    SendPointerEvent(client, currentX, currentY, 1);

    // 按下后短暂延迟，模拟用户准备拖拽
    sleepRand(config.dragStartHoldMinMs, config.dragStartHoldMaxMs);

    // --- 拖拽过程使用贝塞尔曲线模拟 ---
    int steps = randInt(config.dragStepsMin, config.dragStepsMax);
    if (steps <= 0) steps = 1;

    // 生成拖拽轨迹的三阶贝塞尔曲线控制点
    // 注意：这里的控制点生成可能需要和普通移动略有不同，
    // 例如更倾向于直线或特定方向，这里沿用moveMouseHumanLike的生成逻辑作为示例
    auto controlPoints = generateControlPoints(currentX, currentY, endX, endY, config.bezierControlPointOffset);
    int control1X = controlPoints.first.first;
    int control1Y = controlPoints.first.second;
    int control2X = controlPoints.second.first;
    int control2Y = controlPoints.second.second;

    // 循环进行拖拽过程中的移动
    for (int i = 0; i <= steps; ++i) {
        double t = static_cast<double>(i) / steps;
        // 根据三阶贝塞尔曲线公式计算当前步的理想 X, Y 坐标
        double x = bezier(t, currentX, control1X, control2X, endX);
        double y = bezier(t, currentY, control1Y, control2Y, endY);

        // 给计算出的坐标添加随机抖动 (使用拖拽的抖动范围)
        auto [jx, jy] = jitterPos(x, y, config.dragJitterRange);

        // 发送鼠标移动事件，同时保持左键按下状态 (按钮状态 1)
        SendPointerEvent(client, jx, jy, 1);

        // 在两步之间随机延迟 (使用拖拽的延迟范围)
        sleepRand(config.dragSleepMinMs, config.dragSleepMaxMs);
    }
    // --- 拖拽过程结束 ---

    // 确保最终位置是终点，并且左键仍然按下,增加一点偏移
    int  doffsetX = randInt(config.dfinalPosMinX, config.dfinalPosMaxX);
    int  doffsetY = randInt(config.dfinalPosMinY, config.dfinalPosMaxY);
    endX += doffsetX;
    endY += doffsetY;
    SendPointerEvent(client, endX, endY, 1);

    // 松开鼠标左键 (按钮状态 0)
    SendPointerEvent(client, endX, endY, 0);

    // 更新全局的当前鼠标位置为拖拽的终点
    currentX = endX;
    currentY = endY;
}

// 模拟多次鼠标左键点击
// client: VNC 客户端结构体指针
// count: 点击次数
// intervalMs: 每次点击之间的理论间隔毫秒数
// config: 拟人化操作配置
void clickMultiple(rfbClient* client, int count, int intervalMs, const HumanLikeConfig& config) {
    // 循环指定次数
    for (int i = 0; i < count; ++i) {
        // 调用左键点击函数执行一次点击 (传递 config)
        mouseLeftClick(client, config);
        // 在点击之间进行随机延迟，延迟时间围绕 intervalMs 波动
        sleepRand(intervalMs - 10, intervalMs + 10); // 这里的波动范围10是硬编码的，也可以加到 config 里
    }
}




// 处理输入任务，封装所有操作
void handleInput(rfbClient* client, const InputTask& task) {
    HumanLikeConfig defaultConfig; // 使用默认配置
            // 删除冗余的操作坐标日志

    // 根据任务类型选择对应的操作
    switch (task.type) {
    case InputType::MOUSE_MOVE:
        moveMouseHumanLike(client, task.x, task.y, defaultConfig);
        break;
    case InputType::MOUSE_LEFT_CLICK:
        mouseLeftClick(client, defaultConfig);
        break;
    case InputType::MOUSE_RIGHT_CLICK:
        mouseRightClick(client, defaultConfig);
        break;
    case InputType::KEYBOARD:
        pressKey(client, task.key, defaultConfig);
        break;
    case InputType::DRAGMOUSE:
        dragMouse(client, task.x, task.y, defaultConfig); // 示例拖拽
        break;
    case InputType::DELAY:
        // ✅ 添加延时处理
        std::this_thread::sleep_for(std::chrono::milliseconds(task.delayMs));
        AddLogInfo(LogLevel::Debug, "[VNCControl] 执行延时: " + std::to_string(task.delayMs) + "ms");
        break;
    }
}