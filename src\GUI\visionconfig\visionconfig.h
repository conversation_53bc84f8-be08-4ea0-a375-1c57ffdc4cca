#pragma once
#include <vector>
#include <string>
#include <imgui.h>  // 添加ImGui头文件以支持ImVec4

// Vision Config结构体
struct VisionConfigItem {
    std::string name;
    std::string file;
    int isCache;
    bool isDuplicate = false; // 标记是否有重复的name
};

// 状态枚举
enum class VisionConfigStatus {
    NotLoaded,
    Loading,
    LoadSuccess,
    LoadFailed,
    SaveSuccess,
    SaveFailed,
    DuplicateNames
};

// 显示匹配设置编辑器的主函数
void ShowVisionConfigEditor();

// 内部功能函数
namespace VisionConfigEditor {
    // 加载vision_config.json
    bool LoadVisionConfig(const std::string& configPath = "./assets/vision_config.json");
    
    // 保存vision_config.json
    bool SaveVisionConfig(const std::string& configPath = "./assets/vision_config.json");
    
    // 检查重复名称
    void CheckDuplicateNames();
    
    // 获取当前状态
    VisionConfigStatus GetCurrentStatus();
    
    // 获取状态文本和颜色
    std::pair<std::string, ImVec4> GetStatusDisplay();
    
    // 重新加载到TemplatePathManager
    void ReloadToTemplateManager();
    
    // 检查配置是否可以用于任务执行（新增函数）
    bool IsConfigReadyForTask(std::string* errorMessage = nullptr);
} 