#include "TemplatePathManager.h"
#include <nlohmann/json.hpp>
#include <fstream>
#include <GUI/consolelog/consolelog.h>

TemplatePathManager& TemplatePathManager::getInstance() {
    static TemplatePathManager instance;
    return instance;
}


bool TemplatePathManager::loadFromJson(const std::string& jsonPath) {
    std::lock_guard<std::mutex> lock(mutex_);
    std::ifstream in(jsonPath);
    if (!in.is_open()) {
        AddLogInfo(LogLevel::Error, "无法打开模板配置文件: " + jsonPath);
        return false;
    }
    nlohmann::json configJson;
    try {
        in >> configJson;
        if (!configJson.contains("imgTemp") || !configJson["imgTemp"].is_array()) {
            AddLogInfo(LogLevel::Error, "模板配置文件缺少 imgTemp 数组");
            return false;
        }
        for (const auto& tpl : configJson["imgTemp"]) {
            if (tpl.contains("name") && tpl.contains("file")) {
                nameToFile_[tpl["name"]] = tpl["file"];
                // 支持配置isCache字段，1则预加载进缓存
                if (tpl.contains("isCache") && tpl["isCache"].get<int>() == 1) {
                    cv::Mat templ = cv::imread(tpl["file"], cv::IMREAD_UNCHANGED);
                    if (!templ.empty()) {
                        templateCache_.put(tpl["name"], templ);
                    }
                }
            }
        }
        return true;
    }
    catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "模板配置文件解析异常: " + std::string(e.what()));
        return false;
    }
}

std::string TemplatePathManager::getFilePath(const std::string& name) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = nameToFile_.find(name);
    if (it != nameToFile_.end()) {
        return it->second;
    }
    return "";
}
//自动优先用缓存，缓存没有则按需加载
cv::Mat TemplatePathManager::getTemplate(const std::string& name) {
    std::lock_guard<std::mutex> lock(mutex_);
    cv::Mat tpl = templateCache_.get(name);
    if (!tpl.empty()) {
        return tpl;
    }
    auto it = nameToFile_.find(name);
    if (it == nameToFile_.end()) {
        return cv::Mat();
    }
    tpl = cv::imread(it->second, cv::IMREAD_UNCHANGED);
    return tpl;
}

void TemplatePathManager::clearCache() {
    std::lock_guard<std::mutex> lock(mutex_);
    templateCache_.clear();
}
