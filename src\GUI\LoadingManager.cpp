#include "LoadingManager.h"
#include "imgui.h"
#include <iostream>
#include <thread>
#include <cmath>

// 单例实例
LoadingManager& LoadingManager::getInstance() {
    static LoadingManager instance;
    return instance;
}

void LoadingManager::initialize() {
    m_isLoading = true;
    m_currentText = "正在初始化...";
    m_currentProgress = 0.0f;
    m_currentStep = 0;
    m_steps.clear();
    m_startTime = std::chrono::steady_clock::now();
    m_minDisplayTime = 5000; // 默认最小显示时间为5秒
    m_uiReady.store(false);
}

void LoadingManager::addStep(const std::string& name, const std::string& description, float progress, std::function<void()> action) {
    m_steps.emplace_back(name, description, progress, action);
}

void LoadingManager::executeStep(int stepIndex) {
    if (stepIndex < 0 || stepIndex >= static_cast<int>(m_steps.size())) {
        return;
    }
    
    LoadingStep& step = m_steps[stepIndex];
    m_currentText = step.description;
    m_currentProgress = step.progress;
    m_currentStep = stepIndex;
    
    // 执行步骤动作
    if (step.action) {
        step.action();
    }
    
    step.completed = true;
}

void LoadingManager::executeAllSteps() {
    for (size_t i = 0; i < m_steps.size(); ++i) {
        executeStep(static_cast<int>(i));
    }
}

void LoadingManager::updateProgress(const std::string& description, float progress) {
    m_currentText = description;
    m_currentProgress = progress;
}

void LoadingManager::markUIReady() {
    m_uiReady.store(true);
}

bool LoadingManager::checkUIReady() const {
    // 如果有回调函数，使用回调函数检查
    if (m_uiReadyCallback) {
        return m_uiReadyCallback();
    }
    // 否则使用简单的标记状态
    return m_uiReady.load();
}

void LoadingManager::reset() {
    m_isLoading = false;
    m_currentText.clear();
    m_currentProgress = 0.0f;
    m_currentStep = 0;
    m_steps.clear();
    m_minDisplayTime = 5000;
    m_uiReady.store(false);
    m_uiReadyCallback = nullptr;
}

std::chrono::milliseconds LoadingManager::getLoadingTime() const {
    auto endTime = std::chrono::steady_clock::now();
    return std::chrono::duration_cast<std::chrono::milliseconds>(endTime - m_startTime);
}

void LoadingManager::renderLoadingScreen() {
    // 检查是否应该切换到主UI
    if (checkUIReady()) {
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsedTime = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - m_startTime);
        
        // 如果UI已准备完成且达到最小显示时间，则切换
        if (elapsedTime.count() >= m_minDisplayTime) {
            m_isLoading = false;
            return;
        }
    }
    
    ImGuiViewport* viewport = ImGui::GetMainViewport();
    ImGui::SetNextWindowPos(viewport->Pos);
    ImGui::SetNextWindowSize(viewport->Size);
    
    // 设置加载窗口样式
    ImGuiWindowFlags loading_flags = ImGuiWindowFlags_NoTitleBar | 
                                   ImGuiWindowFlags_NoResize | 
                                   ImGuiWindowFlags_NoMove | 
                                   ImGuiWindowFlags_NoBringToFrontOnFocus |
                                   ImGuiWindowFlags_NoNavFocus |
                                   ImGuiWindowFlags_NoInputs;
    
    ImGui::Begin("LoadingScreen", nullptr, loading_flags);
    
    // 设置半透明背景色为深灰色
    ImGui::GetWindowDrawList()->AddRectFilled(
        ImGui::GetWindowPos(), 
        ImVec2(ImGui::GetWindowPos().x + ImGui::GetWindowSize().x, ImGui::GetWindowPos().y + ImGui::GetWindowSize().y), 
        IM_COL32(30, 30, 30, 180) // 改为深灰色，与UI主背景保持一致
    );
    
    // 计算中心位置
    ImVec2 window_center = ImVec2(
        ImGui::GetWindowPos().x + ImGui::GetWindowSize().x * 0.5f,
        ImGui::GetWindowPos().y + ImGui::GetWindowSize().y * 0.5f
    );
    ImVec2 text_size = ImGui::CalcTextSize("qnyh_auto");
    
    // 绘制标题
    ImGui::SetCursorPos(ImVec2(window_center.x - text_size.x * 0.5f, window_center.y - 100));
    ImGui::PushStyleColor(ImGuiCol_Text, IM_COL32(255, 255, 255, 255));
    ImGui::Text("qnyh_auto");
    ImGui::PopStyleColor();
    
    // 绘制当前步骤信息
    if (m_currentStep < static_cast<int>(m_steps.size())) {
        const LoadingStep& currentStepInfo = m_steps[m_currentStep];
        std::string stepInfo = "步骤 " + std::to_string(m_currentStep + 1) + "/" + std::to_string(m_steps.size()) + ": " + currentStepInfo.name;
        text_size = ImGui::CalcTextSize(stepInfo.c_str());
        ImGui::SetCursorPos(ImVec2(window_center.x - text_size.x * 0.5f, window_center.y - 40));
        ImGui::PushStyleColor(ImGuiCol_Text, IM_COL32(100, 150, 255, 255));
        ImGui::Text("%s", stepInfo.c_str());
        ImGui::PopStyleColor();
    }
    
    // 绘制加载文本
    text_size = ImGui::CalcTextSize(m_currentText.c_str());
    ImGui::SetCursorPos(ImVec2(window_center.x - text_size.x * 0.5f, window_center.y - 10));
    ImGui::PushStyleColor(ImGuiCol_Text, IM_COL32(200, 200, 200, 255));
    ImGui::Text("%s", m_currentText.c_str());
    ImGui::PopStyleColor();
    
    // 绘制进度条
    float progressBarWidth = 400.0f;
    float progressBarHeight = 6.0f;
    ImVec2 progressBarPos = ImVec2(window_center.x - progressBarWidth * 0.5f, window_center.y + 20);
    
    // 背景条
    ImGui::GetWindowDrawList()->AddRectFilled(
        progressBarPos,
        ImVec2(progressBarPos.x + progressBarWidth, progressBarPos.y + progressBarHeight),
        IM_COL32(60, 60, 60, 255)
    );
    
    // 进度条
    float currentWidth = progressBarWidth * m_currentProgress;
    if (currentWidth > 0) {
        ImGui::GetWindowDrawList()->AddRectFilled(
            progressBarPos,
            ImVec2(progressBarPos.x + currentWidth, progressBarPos.y + progressBarHeight),
            IM_COL32(0, 150, 255, 255)
        );
    }
    
    // 进度百分比
    std::string progressText = std::to_string(static_cast<int>(m_currentProgress * 100)) + "%";
    text_size = ImGui::CalcTextSize(progressText.c_str());
    ImGui::SetCursorPos(ImVec2(window_center.x - text_size.x * 0.5f, window_center.y + 35));
    ImGui::PushStyleColor(ImGuiCol_Text, IM_COL32(150, 150, 150, 255));
    ImGui::Text("%s", progressText.c_str());
    ImGui::PopStyleColor();
    

    
    ImGui::End();
} 