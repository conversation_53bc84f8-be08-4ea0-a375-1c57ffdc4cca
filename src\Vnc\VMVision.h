// VMVision.h
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#pragma once

#include <chrono> // Added for std::chrono::steady_clock
#include <thread>
#include <mutex>
#include <shared_mutex> // 添加读写锁支持
#include <condition_variable>
#include <string>
#include <atomic>
#include <set>
#include <map>
#include <functional>
#include <deque>
#include <queue>
#include <vector>
#include <memory>
#include <future>

#include <rfb/rfbclient.h>
#include <opencv2/core.hpp>     // OpenCV Core 功能，如 cv::Mat, cv::Point
#include <opencv2/imgproc.hpp>  // OpenCV 图像处理功能，如 cv::matchTemplate
#include "SimpleOCR.h"         // PaddleOCR的SimpleOCR头文件
#include "Core/ThreadPool.h"        // 线程池实现
#include "ScreenFrameDistributor.h"  // 屏幕切片分发器

// Forward declarations to avoid circular dependency
struct TextRecognizeResult;

// 前向声明
struct DispatchCenter;

// 🔧 已删除MatchRequest结构体 - 简化调用链不再需要此结构体

class VMVision {
public:
    VMVision(const std::string& vmName, std::shared_ptr<rfbClient> client);
    ~VMVision();

    // 禁止拷贝和赋值
    VMVision(const VMVision&) = delete;
    VMVision& operator=(const VMVision&) = delete;

    // 检查连接状态
    bool checkConnectionStatus();
    
    // 检查当前帧是否有效
    bool hasValidFrame() const;
    
    // 设置线程池大小（已弃用，现在自己管理线程池）
    // void setThreadPools(ThreadPool* ocrThreadPool, ThreadPool* visionThreadPool);
    
    // 加载模板图像用于匹配
    bool loadTemplate(const std::string& name, const std::string& filePath);

    // 异步模板匹配接口
    void matchTemplateAsync(
        const std::string& templateName, 
        double threshold, 
        cv::Rect roi,
        int sliceNumber,
        std::shared_ptr<std::promise<std::pair<bool, cv::Point>>> resultPromise
    );

    // 异步文本匹配接口
    void findTextAsync(
        const std::string& wordsToMatch, 
        double threshold,
        cv::Rect roi,
        int sliceNumber,
        std::shared_ptr<std::promise<std::pair<bool, cv::Point>>> resultPromise);

    // 异步文本识别接口
    void recognizeTextAsync(
        cv::Rect roi,
        int sliceNumber,
        std::shared_ptr<std::promise<TextRecognizeResult>> resultPromise);

    // 初始化 OCR 引擎 - 延迟初始化方法
    // 在需要时才初始化 OCR 引擎，确保线程安全
    bool initializeOCRIfNeeded();

    // 停止处理函数 (添加声明)
    void stopProcessing();
    
    // 等待处理完全停止
    void waitForStopCompletion();

    // 设置线程池大小
    void setThreadPoolSizes(size_t matchThreads, size_t ocrThreads) {
        matchThreadPoolSize_ = matchThreads;
        ocrThreadPoolSize_ = ocrThreads;
    }
    // OCR文本查找函数
    std::vector<PaddleOCR::OCRPredictResult> recognizeAllText(const cv::Mat& image);
    
    // 切片配置管理
    void setSliceConfiguration(int rows, int cols);
    std::pair<int, int> getSliceConfiguration() const;

    // 新增：获取ScreenFrameDistributor实例
    ScreenFrameDistributor* getScreenFrameDistributor() const;

    // 异步任务处理接口

private:
    // VNC客户端相关
    std::weak_ptr<rfbClient> client_;   // VNC 客户端弱引用，防止悬挂
    std::string vmName_;               // 虚拟机名称
    
    // 线程控制（已迁移到统一线程管理器）
    std::string processingThreadName_; // 处理线程名称
    std::atomic<bool> running_;        // 线程运行状态
    std::atomic<bool> newFrameAvailable_; // 新帧可用标志
    std::atomic<bool> stopRequested_;    // 标志以请求停止处理

    // 帧缓冲区
    mutable std::shared_mutex frameMutex_;     // 帧缓冲区读写锁，允许多个读取操作并发，写入操作独占
    std::condition_variable frameCv_;  // 帧更新条件变量
    std::condition_variable frameCV_;  // 帧缓冲区条件变量
    cv::Mat currentFrame_;             // 当前帧

    // 模板匹配
    std::map<std::string, cv::Mat> templates_; // 模板图像映射
    std::mutex templatesMutex_;        // 模板图像互斥锁

    // 🔧 已删除匹配请求队列相关变量 - 简化调用链不再需要

    // 线程池
    std::unique_ptr<ThreadPool> ocrThreadPool_;        // OCR线程池（自己管理）
    std::unique_ptr<ThreadPool> visionThreadPool_;     // 视觉匹配线程池（自己管理）
    size_t ocrThreadPoolSize_;         // OCR线程池大小
    size_t matchThreadPoolSize_;       // 模板匹配线程池大小

    // OCR引擎 - 使用智能指针自动管理内存
    std::unique_ptr<PaddleOCR::SimpleOCR> ocrEngine_; // 现代C++智能指针管理OCR引擎
    std::mutex ocrMutex_; // Mutex to protect OCR engine access
    std::atomic<bool> ocrInitialized_;  // OCR引擎初始化状态

    // 🔧 已删除所有匹配请求相关变量 - 简化调用链不再需要

    // 图像处理相关函数
    cv::Mat rfbToCvMat(rfbClient* client); // 将 rfb 帧缓冲转换为 OpenCV Mat
    static void onFrameBufferUpdate(rfbClient* client, int x, int y, int w, int h); // VNC 帧缓冲更新回调函数

    // 获取当前帧的函数 - 供线程池任务使用
    cv::Mat getCurrentFrame();

    // 验证图像格式是否适合模板匹配
    bool validateImageForTemplateMatching(const cv::Mat& img, const std::string& imgName);

    // 🔧 简化的直接处理函数 - 移除中间层
    void processTemplateMatchDirect(const std::string& templateName, double threshold, cv::Rect roi, int sliceNumber, std::shared_ptr<std::promise<std::pair<bool, cv::Point>>> resultPromise);
    void processTextMatchDirect(const std::string& wordsToMatch, double threshold, cv::Rect roi, int sliceNumber, std::shared_ptr<std::promise<std::pair<bool, cv::Point>>> resultPromise);
    
    // 文本识别相关方法 - 已简化
    void processTextRecognizeDirect(cv::Rect roi, int sliceNumber, std::shared_ptr<std::promise<TextRecognizeResult>> resultPromise);
    
	cv::Mat preprocessImage(const cv::Mat& input); // 图像预处理函数声明
	cv::Mat preprocessImageMatch(const cv::Mat& input); // 图像预处理函数声明，用于模板匹配
    // OCR文本查找函数
    friend std::tuple<cv::Point, float> findTextLocation(
        PaddleOCR::SimpleOCR& ocr,
        const cv::Mat& image,
        const std::string& text,
        const std::string& vmName);



    // 图像预处理相关参数
    struct PreprocessParams {
        bool enableGaussianBlur = false;
        int gaussianKernelSize = 3;
        bool enableAdaptiveThreshold = false;
        int adaptiveBlockSize = 11;
        int adaptiveC = 2;
        bool enableHistogramEqualization = false;
        bool enableContrastEnhancement = false;
        double alpha = 1.2;  // 对比度增强因子
        int beta = 10;       // 亮度增强因子
    } preprocessParams_;

    // 图像预处理函数
    cv::Mat enhanceContrast(const cv::Mat& input);
    // 准备图像用于模板匹配，确保两个图像格式兼容
    bool prepareImagesForMatching(const cv::Mat& searchRegion, const cv::Mat& templ, 
                                cv::Mat& processedSearchRegion, cv::Mat& processedTemplate);
    cv::Mat applyAdaptiveThreshold(const cv::Mat& input);
    cv::Mat applyHistogramEqualization(const cv::Mat& input);
    
    // 静态标志，用于控制是否处理帧更新回调
    static std::atomic<bool> s_allowFrameUpdates;

    // For throttling frame logs
    std::chrono::steady_clock::time_point lastFrameLogTime_;
    std::chrono::steady_clock::time_point lastLogTime_; // 新增
    
    // 屏幕切片分发器
    std::unique_ptr<ScreenFrameDistributor> screenDistributor_;

public:
    // 静态方法，用于全局启用/禁用帧更新处理
    static void enableFrameUpdates(bool enable) {
        s_allowFrameUpdates = enable;
    }
};