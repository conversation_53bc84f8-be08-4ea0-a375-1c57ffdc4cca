#include "hardwareinfo.h"
#include "imgui.h"
#include <comdef.h>
#include <Wbemidl.h>
#include <sstream>
#include <iostream>
#include <mutex>
#pragma comment(lib, "wbemuuid.lib")

// 全局互斥锁
std::mutex wmiMutex;

// COM 初始化封装类（RAII）
class COMInitializer {
public:
    COMInitializer() {
        HRESULT hr = CoInitializeEx(nullptr, COINIT_MULTITHREADED);
        if (FAILED(hr)) {
            throw std::runtime_error("COM 初始化失败");
        }
    }
    ~COMInitializer() {
        CoUninitialize();
    }
};

// WMI 查询通用函数
template<typename T>
std::string QueryWMI(const wchar_t* query, const wchar_t* property, T converter) {
    std::lock_guard<std::mutex> lock(wmiMutex);
    COMInitializer comInit;  // COM 自动初始化/清理

    IWbemLocator* pLoc = nullptr;
    IWbemServices* pSvc = nullptr;
    IEnumWbemClassObject* pEnumerator = nullptr;
    std::string result;

    HRESULT hres = CoCreateInstance(
        CLSID_WbemLocator, 0, CLSCTX_INPROC_SERVER,
        IID_IWbemLocator, (LPVOID*)&pLoc);
    if (FAILED(hres)) return "创建 IWbemLocator 失败";

    hres = pLoc->ConnectServer(
        _bstr_t(L"ROOT\\CIMV2"), nullptr, nullptr,
        0, 0, 0, 0, &pSvc);
    if (FAILED(hres)) {
        pLoc->Release();
        return "连接到 WMI 命名空间失败";
    }

    hres = CoSetProxyBlanket(
        pSvc, RPC_C_AUTHN_WINNT, RPC_C_AUTHZ_NONE, nullptr,
        RPC_C_AUTHN_LEVEL_CALL, RPC_C_IMP_LEVEL_IMPERSONATE, nullptr, EOAC_NONE);

    hres = pSvc->ExecQuery(
        _bstr_t("WQL"), _bstr_t(query),
        WBEM_FLAG_FORWARD_ONLY, nullptr, &pEnumerator);
    if (FAILED(hres)) {
        pSvc->Release();
        pLoc->Release();
        return "执行查询失败";
    }

    ULONG uReturn = 0;
    IWbemClassObject* pObj = nullptr;
    VARIANT vtProp;
    VariantInit(&vtProp);

    if (pEnumerator) {
        hres = pEnumerator->Next(WBEM_INFINITE, 1, &pObj, &uReturn);
        if (uReturn != 0) {
            hres = pObj->Get(property, 0, &vtProp, 0, 0);
            if (SUCCEEDED(hres)) {
                result = converter(vtProp);
            }
            VariantClear(&vtProp);
            pObj->Release();
        }
    }

    pSvc->Release();
    pLoc->Release();
    if (pEnumerator) pEnumerator->Release();

    return result;
}

// 属性转换器
std::string ConvertBSTR(const VARIANT& vtProp) {
    if (vtProp.vt == VT_BSTR && vtProp.bstrVal != nullptr) {
        // 计算需要的缓冲区大小
        int size = WideCharToMultiByte(CP_UTF8, 0, vtProp.bstrVal, -1, nullptr, 0, nullptr, nullptr);
        if (size > 0) {
            std::string result(size, 0);
            WideCharToMultiByte(CP_UTF8, 0, vtProp.bstrVal, -1, &result[0], size, nullptr, nullptr);
            result.resize(size - 1); // 去掉末尾的null字符
            return result;
        }
    }
    return "";
}

std::string ConvertMemory(const VARIANT& vtProp) {
    ULONGLONG capacity = 0;
    if (vtProp.vt == VT_BSTR) {
        capacity = _wtoll(vtProp.bstrVal);
    }
    else if (vtProp.vt == VT_UI8) {
        capacity = vtProp.ullVal;
    }
    double totalGB = static_cast<double>(capacity) / (1024 * 1024 * 1024);
    std::ostringstream oss;
    oss << totalGB << " GB";
    return oss.str();
}

// 各硬件信息查询函数
std::string GetOSInfoWMI() {
    return QueryWMI(L"SELECT * FROM Win32_OperatingSystem", L"Caption", ConvertBSTR);
}

std::string GetCpuInfoWMI() {
    return QueryWMI(L"SELECT * FROM Win32_Processor", L"Name", ConvertBSTR);
}

std::string GetMotherboardInfo() {
    return QueryWMI(L"SELECT * FROM Win32_BaseBoard", L"Product", ConvertBSTR);
}

std::string GetGpuInfoWMI() {
    return QueryWMI(L"SELECT * FROM Win32_VideoController", L"Name", ConvertBSTR);
}

std::string GetMemoryInfoWMI() {
    auto result = QueryWMI(L"SELECT * FROM Win32_PhysicalMemory", L"Capacity", ConvertMemory);
    return result.empty() ? "0 GB" : result;
}

