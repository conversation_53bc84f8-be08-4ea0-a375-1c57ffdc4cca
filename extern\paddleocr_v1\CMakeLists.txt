cmake_minimum_required(VERSION 4.0.0)
project(PaddleOCR_DLL)

# Set C++ standard to 17 for std::filesystem
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Set Paddle Inference paths
set(PADDLE_INFERENCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/third_party/paddle_inference/paddle")
set(PADDLE_INFERENCE_LIB_DIR "${PADDLE_INFERENCE_DIR}/lib")
set(PADDLE_INFERENCE_INCLUDE_DIR "${PADDLE_INFERENCE_DIR}/include")

# Set OpenCV paths
set(OpenCV_DIR "${CMAKE_CURRENT_SOURCE_DIR}/third_party/opencv/build")
set(OpenCV_INCLUDE_DIRS "${OpenCV_DIR}/include")
set(OpenCV_LIBS_DIR "${OpenCV_DIR}/x64/vc16/lib")
set(OpenCV_BIN_DIR "${OpenCV_DIR}/x64/vc16/bin")

# Set OpenCV libraries
set(OpenCV_LIBS "${OpenCV_LIBS_DIR}/opencv_world4100.lib")
set(OpenCV_LIBS_DEBUG "${OpenCV_LIBS_DIR}/opencv_world4100d.lib")

# Disable automatic DLL copying - we'll do it manually
if(WIN32)
    message(STATUS "Automatic DLL copying is disabled. Please copy required DLLs manually.")
    # List of required DLLs for reference:
    # - opencv_world4100.dll
    # - opencv_videoio_msmf4100_64.dll
    # - opencv_videoio_ffmpeg4100_64.dll
endif()

# Set third-party library paths
set(GLOG_DIR ${CMAKE_CURRENT_SOURCE_DIR}/third_party/paddle_inference/third_party/install/glog)
set(PROTOBUF_DIR ${CMAKE_CURRENT_SOURCE_DIR}/third_party/paddle_inference/third_party/install/protobuf)

# Disable auto_log and add Windows-specific definitions
add_definitions(-DDISABLE_AUTO_LOG -DNOMINMAX -D_USE_MATH_DEFINES -D_CRT_SECURE_NO_WARNINGS)

# 禁用GPU支持
add_definitions(-DDISABLE_GPU)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${OpenCV_INCLUDE_DIRS}
    ${PADDLE_INFERENCE_INCLUDE_DIR}
    ${GLOG_DIR}/include
    ${PROTOBUF_DIR}/include
)

# Library paths
link_directories(
    ${GLOG_DIR}/lib
    ${PROTOBUF_DIR}/lib
    ${OpenCV_LIBS_DIR}
    ${PADDLE_INFERENCE_LIB_DIR}
)

# Add macro definitions
add_definitions(-DPADDLEOCR_DLL_EXPORTS)

# Add source files for the library
set(SOURCES
    src/clipper.cpp
    src/ocr_det.cpp
    src/ocr_rec.cpp
    src/postprocess_op.cpp
    src/preprocess_op.cpp
    src/utility.cpp
    src/SimpleOCR.cpp
    src/SimpleOCR_DLL.cpp
)

# 添加资源文件
set(RESOURCE_FILES)

# 检查并添加模型文件（如果存在）
set(MODEL_FILES
    ${CMAKE_CURRENT_SOURCE_DIR}/models/ch_PP-OCRv4_det_infer/inference.pdmodel
    ${CMAKE_CURRENT_SOURCE_DIR}/models/ch_PP-OCRv4_det_infer/inference.pdiparams
    ${CMAKE_CURRENT_SOURCE_DIR}/models/ch_PP-OCRv4_rec_infer/inference.pdmodel
    ${CMAKE_CURRENT_SOURCE_DIR}/models/ch_PP-OCRv4_rec_infer/inference.pdiparams
    ${CMAKE_CURRENT_SOURCE_DIR}/models/ppocr_keys_v1.txt
)

foreach(file ${MODEL_FILES})
    if(EXISTS ${file})
        list(APPEND RESOURCE_FILES ${file})
        message(STATUS "Found model file: ${file}")
    else()
        message(WARNING "Model file not found: ${file}")
    endif()
endforeach()

# 创建资源目录
set(RESOURCE_DIR ${CMAKE_CURRENT_BINARY_DIR}/resources)
file(MAKE_DIRECTORY ${RESOURCE_DIR})

# 复制资源文件到构建目录
foreach(resource ${RESOURCE_FILES})
    get_filename_component(filename ${resource} NAME)
    configure_file(${resource} ${RESOURCE_DIR}/${filename} COPYONLY)
    list(APPEND RESOURCE_FILES_COPY ${RESOURCE_DIR}/${filename})
    
    # 将资源文件添加到安装目标
    install(FILES ${resource}
            DESTINATION ${CMAKE_INSTALL_PREFIX}/resources
            CONFIGURATIONS Release)
    
    # 在调试配置下也安装资源文件
    install(FILES ${resource}
            DESTINATION ${CMAKE_INSTALL_PREFIX}/resources
            CONFIGURATIONS Debug)
endforeach()

# Create the DLL
add_library(PaddleOCR_DLL SHARED ${SOURCES} ${RESOURCE_FILES})

# Set the output name for the DLL
set_target_properties(PaddleOCR_DLL PROPERTIES OUTPUT_NAME "PaddleOCR")

# Add include directories for PaddleOCR_DLL
target_include_directories(PaddleOCR_DLL PRIVATE 
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${OpenCV_INCLUDE_DIRS}
    ${PADDLE_INFERENCE_INCLUDE_DIR}
    ${GLOG_DIR}/include
    ${PROTOBUF_DIR}/include
)

# Create the demo executable
add_executable(SimpleOCRDemo src/SimpleOCRDemo.cpp)
target_link_libraries(SimpleOCRDemo PRIVATE PaddleOCR_DLL)

# Link OpenCV libraries to SimpleOCRDemo
if(MSVC)
    target_link_libraries(SimpleOCRDemo PRIVATE
        debug ${OpenCV_LIBS_DEBUG}
        optimized ${OpenCV_LIBS}
    )
else()
    target_link_libraries(SimpleOCRDemo PRIVATE ${OpenCV_LIBS})
endif()

# Add include directories for SimpleOCRDemo
target_include_directories(SimpleOCRDemo PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${OpenCV_INCLUDE_DIRS}
)
target_include_directories(PaddleOCR_DLL PRIVATE ${CMAKE_CURRENT_BINARY_DIR})

# 添加自定义命令，在构建后复制资源文件
add_custom_command(TARGET PaddleOCR_DLL POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E make_directory $<TARGET_FILE_DIR:PaddleOCR_DLL>/resources
    COMMAND ${CMAKE_COMMAND} -E copy_directory
    ${RESOURCE_DIR}
    $<TARGET_FILE_DIR:PaddleOCR_DLL>/resources
    COMMENT "Copying resource files to output directory"
)

# Link libraries
if(WIN32)
    target_link_libraries(PaddleOCR_DLL PRIVATE
        ${OpenCV_LIBS}
        ${PADDLE_INFERENCE_LIB_DIR}/paddle_inference.lib
        ${PADDLE_INFERENCE_LIB_DIR}/common.lib
        ${GLOG_DIR}/lib/glog.lib
        ${PROTOBUF_DIR}/lib/libprotobuf.lib
        shlwapi.lib
        ws2_32.lib
        $<$<CONFIG:Debug>:${OpenCV_LIBS_DEBUG}>
        $<$<NOT:$<CONFIG:Debug>>:${OpenCV_LIBS}>
    )
else()
    target_link_libraries(PaddleOCR_DLL PRIVATE
        ${OpenCV_LIBS}
        ${PADDLE_INFERENCE_LIB_DIR}/paddle_inference.lib
        ${GLOG_DIR}/lib/libglog.a
        ${PROTOBUF_DIR}/lib/libprotobuf.a
    )
endif()

# Set target properties
set_target_properties(PaddleOCR_DLL PROPERTIES
    OUTPUT_NAME "PaddleOCR"
    VERSION 1.0.0
    SOVERSION 1
    DEBUG_POSTFIX "d"
    RELEASE_POSTFIX ""
    MINSIZEREL_POSTFIX ""
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
)

# Install rules
install(TARGETS PaddleOCR_DLL
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.h"
)

# DLLs need to be copied manually on Windows
if(WIN32)
    message(STATUS "Note: Please copy the following DLLs to ${CMAKE_BINARY_DIR}/bin/Release/ manually:")
    message(STATUS "  - From ${PADDLE_INFERENCE_LIB_DIR}/*.dll")
    message(STATUS "  - From ${OpenCV_DIR}/x64/vc16/bin/opencv_world4100.dll (and opencv_world4100d.dll for Debug)")
    message(STATUS "  - From ${OpenCV_DIR}/x64/vc16/bin/opencv_videoio_ffmpeg4100_64.dll")
    message(STATUS "  - From ${OpenCV_DIR}/x64/vc16/bin/opencv_videoio_msmf4100_64.dll")
endif()

# Example programs (optional)
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/examples/SimpleExample.cpp")
    add_executable(SimpleExample examples/SimpleExample.cpp)
    target_link_libraries(SimpleExample PaddleOCR_DLL ${OpenCV_LIBS})
    set_target_properties(SimpleExample PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    )
    
    # Copy test image to output directory
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/test_image.jpg")
        add_custom_command(TARGET SimpleExample POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
                ${CMAKE_CURRENT_SOURCE_DIR}/test_image.jpg
                $<TARGET_FILE_DIR:SimpleExample>
            COMMENT "Copying test image"
        )
    endif()
endif()

# Legacy example (optional)
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/examples/PaddleOCR_Example.cpp")
    add_executable(PaddleOCR_Example examples/PaddleOCR_Example.cpp)
    target_link_libraries(PaddleOCR_Example PaddleOCR_DLL ${OpenCV_LIBS})
    set_target_properties(PaddleOCR_Example PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    )
endif()
