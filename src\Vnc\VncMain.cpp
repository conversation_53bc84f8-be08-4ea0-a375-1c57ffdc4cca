#pragma once
#include <vector>
#include <string>
#include <iostream>
#include <memory>
#include <chrono>
#include <thread>
#include <GUI/consolelog/consolelog.h>
#include "DispatchCenter.h"
#include "Core/UnifiedThreadManager.h"
#include "MouseBoardCode.h"
#include <imgui.h>
#include <algorithm>
#include <mutex>
#include <config/ConfigLoader.h>
#include <config/TemplatePathManager.h>
#include <GUI/visionconfig/visionconfig.h>
#include "VMStateManager.h"
#include "../GUI/mainui.h"
#include "../Logic/common.h"
#include "VMVision.h"

// 智能轮询策略常量
const double POLL_INTERVAL_CONNECTING = 0.5;    // 连接中时：0.5秒快速轮询
const double POLL_INTERVAL_CONNECTED = 2.0;     // 已连接时：2秒正常轮询  
const double POLL_INTERVAL_DISCONNECTED = 5.0;  // 未连接时：5秒慢速轮询

// 全局虚拟机连接状态和列表，供UI和轮询共享
// std::vector<std::string> available_vms;
// bool vms_connected = false;
// std::mutex vm_data_mutex;

static double lastVmCheckTime = 0.0;
static std::vector<bool> vmConnectedStatus;
static bool checkedAllVM = false;
static std::vector<std::pair<std::string, int>> vmIpConfigurationsCache;

// 启动视觉处理线程
bool turnOnVisionThread(const std::string& vmName) {
    try {
        // 获取视觉处理器实例，明确指定不要求循环已运行
        VMVision* vmVisionInstance = DispatchCenter::getInstance()->getVisionProcessor(vmName, std::chrono::milliseconds(5000));
        if (!vmVisionInstance) {
            AddLogInfo(LogLevel::Error, "[" + vmName + "] 获取视觉处理器实例失败，无法启动视觉线程！");
            return false;
        }

        // 检查连接状态
        bool connectionOk = vmVisionInstance->checkConnectionStatus();
        if (!connectionOk) {
            AddLogInfo(LogLevel::Warning, "[" + vmName + "] 视觉处理器连接异常，但将继续尝试处理");
        }
        return true;
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[" + vmName + "] 启动视觉线程时发生异常: " + std::string(e.what()));
        return false;
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[" + vmName + "] 启动视觉线程时发生未知异常");
        return false;
    }
}

// 清理函数
void cleanupVnc() {
    // 如果正在连接，不进行清理
    if (VMStateManager::getInstance()->isConnecting()) {
        AddLogInfo(LogLevel::Warning, "[主程序] 正在连接中，不进行清理");
        return;
    }
    
    // 如果已经连接成功，不进行清理
    if (VMStateManager::getInstance()->isConnected()) {
        AddLogInfo(LogLevel::Info, "[主程序] 已经连接成功，不需要执行清理");
        return;
    }
    
    try {
        // 停止所有VM控制器并清理相关资源
        DispatchCenter::getInstance()->stopAllVMControllers();
    }
    catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[主程序] 清理资源时发生异常: " + std::string(e.what()));
    }
    catch (...) {
        AddLogInfo(LogLevel::Error, "[主程序] 清理资源时发生未知异常");
    }
}

// 断开全部虚拟机的连接
void disconnectAllVMs() {
    try {
        // 如果正在连接，不进行断开操作
        if (VMStateManager::getInstance()->isConnecting()) {
            AddLogInfo(LogLevel::Warning, "[主程序] 正在连接中，不进行断开操作");
            return;
        }
        
        // 如果没有连接，不需要断开
        if (!VMStateManager::getInstance()->isConnected()) {
            AddLogInfo(LogLevel::Info, "[主程序] 当前没有连接的虚拟机，不需要断开");
            return;
        }
        
        AddLogInfo(LogLevel::Info, "[主程序] 正在断开全部虚拟机的连接...");
        
        // 调用 DispatchCenter 的断开所有虚拟机的方法
        DispatchCenter::getInstance()->disconnectAllVMs();
        
        // 重置连接状态
        VMStateManager::getInstance()->setConnected(false);
        VMStateManager::getInstance()->setConnecting(false);
        
        AddLogInfo(LogLevel::Info, "[主程序] 全部虚拟机已断开连接");
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[主程序] 断开全部虚拟机连接时发生异常: " + std::string(e.what()));
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[主程序] 断开全部虚拟机连接时发生未知异常");
    }
}

// 前向声明UI重置函数
void resetUIState();

// 重置全局连接状态（用于停止后重新连接）
void resetGlobalConnectionState() {
    try {
        AddLogInfo(LogLevel::Info, "[主程序] 重置全局连接状态...");
        
        // 重置全局状态变量
        VMStateManager::getInstance()->setConnected(false);
        VMStateManager::getInstance()->setConnecting(false);
        VMStateManager::getInstance()->setAvailableVMs({});  // 清空可用虚拟机列表
        VMStateManager::getInstance()->setVMsConnected(false);
        checkedAllVM = false;
        
        // 重置vmConnectedStatus向量，让ShowVMConnectionProgressUI显示0/2
        if (!vmConnectedStatus.empty()) {
            std::fill(vmConnectedStatus.begin(), vmConnectedStatus.end(), false);
            AddLogInfo(LogLevel::Info, "[主程序] vmConnectedStatus状态已重置");
        }
        
        // 清理VM数据 - 直接重置，依赖g_isConnected标志控制UI
        // 由于g_isConnected=false，PollVMConnectionAndVisionThreads不会再更新这些变量
        // 所以可以安全地重置它们，即使不能立即获取锁也没关系
        // available_vms.clear();
        // vms_connected = false;
        AddLogInfo(LogLevel::Info, "[主程序] VM数据状态已重置");
        
        // 重置其他状态
        lastVmCheckTime = 0.0;
        
        // 重置UI选择器状态
        resetUIState();
        
        AddLogInfo(LogLevel::Info, "[主程序] 全局连接状态已重置，可以重新连接");
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[主程序] 重置全局连接状态时发生异常: " + std::string(e.what()));
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[主程序] 重置全局连接状态时发生未知异常");
    }
}

// 在ImGui主循环中调用，异步检测虚拟机连接和视觉线程启动
void PollVMConnectionAndVisionThreads() {
    // 只在全局连接状态为真时才进行轮询
    if (!VMStateManager::getInstance()->isConnected() || vmIpConfigurationsCache.empty()) return;
    
    // 使用智能轮询间隔
    double pollInterval = VMStateManager::getInstance()->isConnecting() ? 
                         POLL_INTERVAL_CONNECTING : POLL_INTERVAL_CONNECTED;
    
    double now = ImGui::GetTime();
    if (now - lastVmCheckTime > pollInterval && !checkedAllVM) {
        lastVmCheckTime = now;
        size_t vmCount = vmIpConfigurationsCache.size();
        if (vmConnectedStatus.size() != vmCount)
            vmConnectedStatus.assign(vmCount, false);
        int connectedVMCount = 0;
        std::vector<std::string> connectedVMs;
        
        try {
            // 首先检查DispatchCenter是否存在并可用
            auto* dispatchCenter = DispatchCenter::getInstance();
            if (!dispatchCenter) {
                AddLogInfo(LogLevel::Error, "[主程序] DispatchCenter实例无效");
                return;
            }
            
            for (size_t i = 0; i < vmCount; ++i) {
                std::string vmName = vmIpConfigurationsCache[i].first; // 直接使用配置中的虚拟机名称
                bool isConnected = false;
                
                try {
                    isConnected = dispatchCenter->isVMInitialized(vmName) ||
                                   dispatchCenter->isVMConnected(vmName);
                } catch (const std::exception& e) {
                    AddLogInfo(LogLevel::Error, "[主程序] 检查" + vmName + "连接状态时发生异常: " + std::string(e.what()));
                    isConnected = false;
                } catch (...) {
                    AddLogInfo(LogLevel::Error, "[主程序] 检查" + vmName + "连接状态时发生未知异常");
                    isConnected = false;
                }
                
                if (isConnected) {
                    connectedVMCount++;
                    connectedVMs.push_back(vmName);
                    
                    if (!vmConnectedStatus[i]) {
                        vmConnectedStatus[i] = true;
                        
                        try {
                            bool visionStarted = turnOnVisionThread(vmName);
                            if (visionStarted) {
                                AddLogInfo(LogLevel::Info, "[主程序] " + vmName + " 视觉线程启动成功");
                            } else {
                                AddLogInfo(LogLevel::Warning, "[主程序] " + vmName + " 视觉线程启动失败");
                            }
                        } catch (const std::exception& e) {
                            AddLogInfo(LogLevel::Error, "[主程序] 启动" + vmName + "视觉线程时发生异常: " + std::string(e.what()));
                        } catch (...) {
                            AddLogInfo(LogLevel::Error, "[主程序] 启动" + vmName + "视觉线程时发生未知异常");
                        }
                    } else {
                        // 即使之前已经标记为连接，也要确保视觉线程正常运行
                        try {
                            VMVision* visionProcessor = dispatchCenter->getVisionProcessor(vmName, std::chrono::milliseconds(1000));
                            if (!visionProcessor) {
                                AddLogInfo(LogLevel::Warning, "[主程序] " + vmName + " 视觉处理器不可用，尝试重新启动视觉线程");
                                bool visionStarted = turnOnVisionThread(vmName);
                                if (visionStarted) {
                                    AddLogInfo(LogLevel::Info, "[主程序] " + vmName + " 视觉线程重新启动成功");
                                } else {
                                    AddLogInfo(LogLevel::Warning, "[主程序] " + vmName + " 视觉线程重新启动失败");
                                }
                            }
                        } catch (const std::exception& e) {
                            AddLogInfo(LogLevel::Error, "[主程序] 检查" + vmName + "视觉处理器状态时发生异常: " + std::string(e.what()));
                        } catch (...) {
                            AddLogInfo(LogLevel::Error, "[主程序] 检查" + vmName + "视觉处理器状态时发生未知异常");
                        }
                    }
                } else {
                    // 虚拟机未连接，重置状态
                    if (vmConnectedStatus[i]) {
                        AddLogInfo(LogLevel::Warning, "[主程序] " + vmName + " 连接已断开");
                        vmConnectedStatus[i] = false;
                    }
                }
            }
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "[主程序] 轮询虚拟机连接状态时发生异常: " + std::string(e.what()));
        } catch (...) {
            AddLogInfo(LogLevel::Error, "[主程序] 轮询虚拟机连接状态时发生未知异常");
        }
        
        // 同步全局可用虚拟机列表和连接状态
        try {
            // std::lock_guard<std::mutex> lock(vm_data_mutex);
            if (!connectedVMs.empty()) {
                // vms_connected = true;
                // if (available_vms != connectedVMs) {
                //     available_vms = connectedVMs;
                // }
            } else {
                // vms_connected = false;
                // available_vms.clear();
            }
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "[主程序] 更新虚拟机状态时发生异常: " + std::string(e.what()));
        } catch (...) {
            AddLogInfo(LogLevel::Error, "[主程序] 更新虚拟机状态时发生未知异常");
        }
        
        if (connectedVMCount == vmCount) {
            checkedAllVM = true;
        }
    }
}

// 可选：ImGui界面显示进度
void ShowVMConnectionProgressUI() {
    size_t connected = std::count(vmConnectedStatus.begin(), vmConnectedStatus.end(), true);
    size_t total = vmConnectedStatus.size();
    ImGui::Text("VM 连接进度: %d/%d", (int)connected, (int)total);
    if (!checkedAllVM) {
        ImGui::TextColored(ImVec4(1,1,0,1), "正在等待所有虚拟机连接...");
    } else {
        ImGui::TextColored(ImVec4(0,1,0,1), "所有虚拟机已连接！");
    }
}

// 合并的虚拟机状态轮询和显示函数 - 智能轮询版本
void PollAndShowVMStatus() {
    // 获取当前状态
    bool isConnecting = VMStateManager::getInstance()->isConnecting();
    bool isConnected = VMStateManager::getInstance()->isConnected();
    
    // 根据状态选择轮询间隔
    double pollInterval;
    if (isConnecting) {
        pollInterval = POLL_INTERVAL_CONNECTING;  // 连接中：快速轮询
    } else if (isConnected) {
        pollInterval = POLL_INTERVAL_CONNECTED;   // 已连接：正常轮询
    } else {
        pollInterval = POLL_INTERVAL_DISCONNECTED; // 未连接：慢速轮询
    }
    
    // 未连接且未在连接中时，仅显示状态，减少不必要的处理
    if (!isConnected && !isConnecting) {
        // 只在较长间隔后才执行一次简单检查
        static double lastDisconnectedCheck = 0.0;
        double now = ImGui::GetTime();
        if (now - lastDisconnectedCheck > POLL_INTERVAL_DISCONNECTED) {
            lastDisconnectedCheck = now;
            // 可以在这里做一些轻量级的状态检查，但不执行复杂的VM轮询
        }
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "虚拟机: 未连接");
        return;
    }
    
    // 执行正常的轮询逻辑
    if (vmIpConfigurationsCache.empty()) {
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "虚拟机: 配置加载中...");
        return;
    }
    
    double now = ImGui::GetTime();
    if (now - lastVmCheckTime > pollInterval && !checkedAllVM) {
        lastVmCheckTime = now;
        size_t vmCount = vmIpConfigurationsCache.size();
        if (vmConnectedStatus.size() != vmCount)
            vmConnectedStatus.assign(vmCount, false);
        int connectedVMCount = 0;
        std::vector<std::string> connectedVMs;
        
        try {
            auto* dispatchCenter = DispatchCenter::getInstance();
            if (!dispatchCenter) return;
            
            for (size_t i = 0; i < vmCount; ++i) {
                std::string vmName = vmIpConfigurationsCache[i].first;
                bool isConnected = false;
                
                try {
                    isConnected = dispatchCenter->isVMInitialized(vmName) ||
                                   dispatchCenter->isVMConnected(vmName);
                } catch (...) {
                    isConnected = false;
                }
                
                if (isConnected) {
                    connectedVMCount++;
                    connectedVMs.push_back(vmName);
                    
                    if (!vmConnectedStatus[i]) {
                        vmConnectedStatus[i] = true;
                        
                        try {
                            bool visionStarted = turnOnVisionThread(vmName);
                            if (visionStarted) {
                                AddLogInfo(LogLevel::Info, "[主程序] " + vmName + " 视觉线程启动成功");
                            }
                        } catch (...) {
                            // 忽略异常，继续处理
                        }
                    } else {
                        // 确保视觉线程正常运行
                        try {
                            VMVision* visionProcessor = dispatchCenter->getVisionProcessor(vmName, std::chrono::milliseconds(1000));
                            if (!visionProcessor) {
                                turnOnVisionThread(vmName);
                            }
                        } catch (...) {
                            // 忽略异常，继续处理
                        }
                    }
                } else {
                    if (vmConnectedStatus[i]) {
                        AddLogInfo(LogLevel::Warning, "[主程序] " + vmName + " 连接已断开");
                        vmConnectedStatus[i] = false;
                    }
                }
            }
            
            // 同步全局状态到VMStateManager
            try {
                if (!connectedVMs.empty()) {
                    VMStateManager::getInstance()->setAvailableVMs(connectedVMs);
                    VMStateManager::getInstance()->setVMsConnected(true);
                } else {
                    VMStateManager::getInstance()->setAvailableVMs({});
                    VMStateManager::getInstance()->setVMsConnected(false);
                }
            } catch (...) {
                // 忽略异常
            }
            
            if (connectedVMCount == vmCount) {
                checkedAllVM = true;
            }
        } catch (...) {
            // 忽略异常，继续处理
        }
    }
    
    // 然后显示简化的状态信息
    size_t connected = std::count(vmConnectedStatus.begin(), vmConnectedStatus.end(), true);
    size_t total = vmConnectedStatus.size();
    
    if (total == 0) {
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "虚拟机: 配置加载中...");
    } else if (!checkedAllVM) {
        ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "虚拟机: %d/%d 连接中...", (int)connected, (int)total);
    } else {
        ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "虚拟机: %d/%d 已连接", (int)connected, (int)total);
    }
}

// VNC主入口函数
void vncMain() {
    try {

        // 如果正在连接，不进行操作
        if (VMStateManager::getInstance()->isConnecting()) {
            AddLogInfo(LogLevel::Warning, "[主程序] 正在连接中，请等待...");
            return;
        }
        
        // 如果已经连接成功，不进行操作
        if (VMStateManager::getInstance()->isConnected()) {
            AddLogInfo(LogLevel::Info, "[主程序] 已经连接成功，不需要重新连接");
            return;
        }
        
        // 清理之前的资源
        cleanupVnc();

        // 设置正在连接标志
        VMStateManager::getInstance()->setConnecting(true);

        // 加载虚拟机IP配置
        std::vector<std::pair<std::string, int>> vmIpConfigurations = loadIpConfigFromJson("./assets/ip_config.json");
        if (vmIpConfigurations.empty()) {
            AddLogInfo(LogLevel::Error, "[主程序] 加载虚拟机IP配置文件失败或配置为空！");
            VMStateManager::getInstance()->setConnecting(false);
            return;
        }

        // 初始化DispatchCenter
        if (!DispatchCenter::getInstance()->initialize(vmIpConfigurations)) {
            AddLogInfo(LogLevel::Warning, "[主程序] 部分虚拟机初始化失败，将继续处理已成功连接的虚拟机");
        }
        
        // 缓存VM配置供轮询使用
        vmIpConfigurationsCache = vmIpConfigurations;
        vmConnectedStatus.assign(vmIpConfigurations.size(), false);
        checkedAllVM = false;
        lastVmCheckTime = ImGui::GetTime();

		//加载视觉模板配置
		if (!TemplatePathManager::getInstance().loadFromJson("./assets/vision_config.json")) {
			AddLogInfo(LogLevel::Error, "[主程序] 加载视觉模板配置文件失败！");
			VMStateManager::getInstance()->setConnecting(false);
			return; // 终止连接
		}

        // 自动加载匹配设置界面配置（如果还没有加载过）
        if (VisionConfigEditor::GetCurrentStatus() == VisionConfigStatus::NotLoaded) {
            if (!VisionConfigEditor::LoadVisionConfig("./assets/vision_config.json")) {
                AddLogInfo(LogLevel::Warning, "[主程序] 自动加载匹配设置界面配置失败，但不影响主要功能");
            }
        }

        // 等待一段时间让连接完成，然后检查连接状态
        std::this_thread::sleep_for(std::chrono::seconds(3));
        
        // 检查实际连接状态并设置全局标志
        try {
            auto* dispatchCenter = DispatchCenter::getInstance();
            if (dispatchCenter) {
                std::vector<std::string> connectedVMs = dispatchCenter->getConnectedVMNames();
                int connectedCount = connectedVMs.size();
                int totalCount = vmIpConfigurations.size();
                
                if (connectedCount > 0) {
                    // 至少有一个虚拟机连接成功，设置连接成功标志
                    VMStateManager::getInstance()->setConnected(true);
                    VMStateManager::getInstance()->setConnecting(false);
                    
                    // 更新全局VM列表
                    VMStateManager::getInstance()->setAvailableVMs(connectedVMs);
                    VMStateManager::getInstance()->setVMsConnected(true);

                    // 启动每个连接成功的虚拟机的视觉线程
                    for (const auto& vmName : connectedVMs) {
                        try {
                            bool visionStarted = turnOnVisionThread(vmName);
                            if (!visionStarted) {
                                AddLogInfo(LogLevel::Warning, "[主程序] " + vmName + " 视觉线程启动失败");
                            }
                        } catch (const std::exception& e) {
                            AddLogInfo(LogLevel::Error, "[主程序] 启动" + vmName + "视觉线程时发生异常: " + std::string(e.what()));
                        } catch (...) {
                            AddLogInfo(LogLevel::Error, "[主程序] 启动" + vmName + "视觉线程时发生未知异常");
                        }
                    }

                } else {
                    // 没有虚拟机连接成功
                    VMStateManager::getInstance()->setConnected(false);
                    VMStateManager::getInstance()->setConnecting(false);
                    VMStateManager::getInstance()->setAvailableVMs({});  // 清空可用虚拟机列表
                    VMStateManager::getInstance()->setVMsConnected(false);
                    AddLogInfo(LogLevel::Error, "[主程序] 最终状态：没有虚拟机连接成功");
                }
            } else {
                AddLogInfo(LogLevel::Error, "[主程序] DispatchCenter实例无效，无法检查连接状态");
                VMStateManager::getInstance()->setConnected(false);
                VMStateManager::getInstance()->setConnecting(false);
            }
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "[主程序] 检查连接状态时发生异常: " + std::string(e.what()));
            VMStateManager::getInstance()->setConnected(false);
            VMStateManager::getInstance()->setConnecting(false);
        } catch (...) {
            AddLogInfo(LogLevel::Error, "[主程序] 检查连接状态时发生未知异常");
            VMStateManager::getInstance()->setConnected(false);
            VMStateManager::getInstance()->setConnecting(false);
        }

        // 移除延时状态检查线程，避免重复打印警告日志
        // 状态检查已在主程序中完成，不需要额外的延时检查
        
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[主程序] VNC初始化过程中发生异常: " + std::string(e.what()));
        VMStateManager::getInstance()->setConnecting(false);
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[主程序] VNC初始化过程中发生未知异常");
        VMStateManager::getInstance()->setConnecting(false);
    }
}
