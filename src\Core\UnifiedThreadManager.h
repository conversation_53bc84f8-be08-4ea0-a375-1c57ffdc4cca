#pragma once

#include <thread>
#include <memory>
#include <string>
#include <unordered_map>
#include <mutex>
#include <atomic>
#include <functional>
#include <chrono>
#include <vector>
#include <future>
#include <iostream>
#include <GUI/consolelog/consolelog.h>
#include "ThreadPool.h"
#include "ConfigManager.h"

// 线程池类型枚举
enum class ThreadPoolType {
    GENERAL,    // 通用线程池
    OCR,        // OCR专用线程池
    VISION      // 视觉处理专用线程池
};

// 线程状态信息
struct ThreadStatus {
    std::string name;
    bool isRunning;
    std::chrono::steady_clock::time_point startTime;
    std::chrono::milliseconds runningTime;
};

// 线程信息结构体
struct ThreadInfo {
    std::thread thread;
    std::shared_ptr<std::atomic<bool>> shouldStop;
    std::string name;
    std::chrono::steady_clock::time_point startTime;
    std::atomic<bool> isRunning{true};
};

/**
 * 统一线程管理器 - 增强版
 * 管理所有线程的生命周期，包括命名线程和线程池
 */
class UnifiedThreadManager {
public:
    // 单例模式
    static UnifiedThreadManager& getInstance() {
        static UnifiedThreadManager instance;
        return instance;
    }
    
    // 禁止拷贝和移动
    UnifiedThreadManager(const UnifiedThreadManager&) = delete;
    UnifiedThreadManager& operator=(const UnifiedThreadManager&) = delete;
    UnifiedThreadManager(UnifiedThreadManager&&) = delete;
    UnifiedThreadManager& operator=(UnifiedThreadManager&&) = delete;
    
    /**
     * 初始化线程管理器
     */
    bool initialize() {
        std::lock_guard<std::mutex> lock(mutex_);
        if (initialized_) {
            return true;
        }
        
        try {
            // 从配置获取线程池大小
            auto& configManager = ConfigManager::getInstance();
            int generalPoolSize = configManager.getConfig<int>("performance.thread_pool_size", 4);
            int maxWorkerThreads = configManager.getConfig<int>("performance.max_worker_threads", 16);
            
            // 创建线程池（构造函数中设置名称）
            generalThreadPool_ = std::make_unique<ThreadPool>(generalPoolSize, "GeneralPool");
            
            ocrThreadPool_ = std::make_unique<ThreadPool>(std::min(generalPoolSize, maxWorkerThreads / 4), "OCRPool");
            
            visionThreadPool_ = std::make_unique<ThreadPool>(std::min(generalPoolSize, maxWorkerThreads / 4), "VisionPool");
            
            initialized_ = true;
            startTime_ = std::chrono::steady_clock::now();
            
            return true;
        } catch (const std::exception& e) {
            // 静默处理异常
            return false;
        } catch (...) {
            return false;
        }
    }
    
    /**
     * 关闭线程管理器
     */
    void shutdown() {
        std::lock_guard<std::mutex> lock(mutex_);
        if (!initialized_) {
            return;
        }
        
        // 执行关闭回调
        for (auto& callback : shutdownCallbacks_) {
            try {
                callback();
            } catch (const std::exception& e) {
                AddLogInfo(LogLevel::Warning, "关闭回调异常: " + std::string(e.what()));
            }
        }
        
        // 停止所有命名线程
        for (auto& [name, threadInfo] : threads_) {
            if (threadInfo && threadInfo->shouldStop) {
                threadInfo->shouldStop->store(true);
                threadInfo->isRunning.store(false);
            }
        }
        
        // 等待线程结束
        for (auto& [name, threadInfo] : threads_) {
            if (threadInfo && threadInfo->thread.joinable()) {
                try {
                    threadInfo->thread.join();
                    AddLogInfo(LogLevel::Debug, "线程已停止: " + name);
                } catch (const std::exception& e) {
                    AddLogInfo(LogLevel::Warning, "停止线程异常: " + name + " - " + e.what());
                }
            }
        }
        
        // 关闭线程池
        if (generalThreadPool_) {
            generalThreadPool_->stop(true, std::chrono::seconds(5));
            generalThreadPool_.reset();
        }
        if (ocrThreadPool_) {
            ocrThreadPool_->stop(true, std::chrono::seconds(5));
            ocrThreadPool_.reset();
        }
        if (visionThreadPool_) {
            visionThreadPool_->stop(true, std::chrono::seconds(5));
            visionThreadPool_.reset();
        }
        
        threads_.clear();
        shutdownCallbacks_.clear();
        initialized_ = false;
        AddLogInfo(LogLevel::Info, "关闭完成");
    }
    
    /**
     * 创建命名线程 - 循环执行版本（用于持续运行的任务）
     */
    template<typename F>
    std::string createNamedThread(const std::string& name, F&& func) {
        return createNamedThreadInternal(name, std::forward<F>(func), true);
    }
    
    /**
     * 创建命名线程 - 一次性执行版本（用于单次任务）
     */
    template<typename F>
    std::string createNamedThreadOnce(const std::string& name, F&& func) {
        return createNamedThreadInternal(name, std::forward<F>(func), false);
    }

private:
    /**
     * 创建命名线程的内部实现
     */
    template<typename F>
    std::string createNamedThreadInternal(const std::string& name, F&& func, bool looping) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (!initialized_) {
            // 静默处理未初始化错误
            return "";
        }
        
        if (threads_.find(name) != threads_.end()) {
            // 静默处理线程已存在错误
            return "";
        }
        
        auto threadInfo = std::make_unique<ThreadInfo>();
        threadInfo->name = name;
        threadInfo->shouldStop = std::make_shared<std::atomic<bool>>(false);
        threadInfo->startTime = std::chrono::steady_clock::now();
        
        // 创建线程
        if (looping) {
            // 循环执行版本（用于持续运行的任务）
            threadInfo->thread = std::thread([func = std::forward<F>(func), shouldStop = threadInfo->shouldStop, name]() {
                try {
                    // 避免在线程启动时立即调用日志，可能导致静态初始化问题
                    while (!shouldStop->load()) {
                        func();
                        std::this_thread::sleep_for(std::chrono::milliseconds(100));
                    }
                } catch (const std::exception& e) {
                    // 静默处理异常
                }
            });
        } else {
            // 一次性执行版本（用于单次任务）
            auto isRunningPtr = &threadInfo->isRunning; // 获取指针以便在lambda中使用
            threadInfo->thread = std::thread([func = std::forward<F>(func), shouldStop = threadInfo->shouldStop, name, isRunningPtr]() {
                try {
                    func(); // 直接执行一次
                } catch (const std::exception& e) {
                    // 静默处理异常
                }
                
                // 标记线程为已停止
                isRunningPtr->store(false);
            });
        }
        
        threads_[name] = std::move(threadInfo);
        return name;
    }

public:
    
    /**
     * 停止命名线程
     */
    bool stopNamedThread(const std::string& name) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = threads_.find(name);
        if (it == threads_.end()) {
            return false;
        }
        
        auto& threadInfo = it->second;
        if (threadInfo && threadInfo->shouldStop) {
            threadInfo->shouldStop->store(true);
            threadInfo->isRunning.store(false);
        }
        
        if (threadInfo && threadInfo->thread.joinable()) {
            try {
                threadInfo->thread.join();
            } catch (const std::exception& e) {
                AddLogInfo(LogLevel::Warning, "  停止线程异常: " + name + " - " + e.what());
            }
        }
        
        threads_.erase(it);
        // 删除：停止线程日志
        return true;
    }
    
    /**
     * 提交任务到线程池
     */
    template<typename F, typename... Args>
    auto submitTask(ThreadPoolType type, F&& f, Args&&... args) 
        -> std::future<typename std::result_of<F(Args...)>::type> {
        
        ThreadPool* pool = getThreadPool(type);
        if (!pool) {
            // 静默处理线程池不可用错误，返回一个无效的future
            std::promise<typename std::result_of<F(Args...)>::type> promise;
            auto future = promise.get_future();
            // 不设置promise的值，future将保持无效状态
            return future;
        }
        
        return pool->enqueue(std::forward<F>(f), std::forward<Args>(args)...);
    }
    
    /**
     * 获取线程池指针
     */
    ThreadPool* getThreadPool(ThreadPoolType type) {
        std::lock_guard<std::mutex> lock(mutex_);
        switch (type) {
            case ThreadPoolType::GENERAL: return generalThreadPool_.get();
            case ThreadPoolType::OCR: return ocrThreadPool_.get();
            case ThreadPoolType::VISION: return visionThreadPool_.get();
            default: return nullptr;
        }
    }
    
    /**
     * 注册关闭回调
     */
    void registerShutdownCallback(std::function<void()> callback) {
        std::lock_guard<std::mutex> lock(mutex_);
        shutdownCallbacks_.push_back(std::move(callback));
    }
    
    /**
     * 获取线程数量
     */
    size_t getThreadCount() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return threads_.size();
    }
    
    /**
     * 获取线程状态信息
     */
    ThreadStatus getThreadStatus(const std::string& name) const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = threads_.find(name);
        if (it == threads_.end()) {
            return {"", false, {}, {}};
        }
        
        auto now = std::chrono::steady_clock::now();
        auto runningTime = std::chrono::duration_cast<std::chrono::milliseconds>(
            now - it->second->startTime);
        
        return {
            it->second->name,
            it->second->isRunning.load(),
            it->second->startTime,
            runningTime
        };
    }
    
    /**
     * 获取所有线程信息
     */
    std::vector<ThreadStatus> getAllThreadsInfo() const {
        std::lock_guard<std::mutex> lock(mutex_);
        std::vector<ThreadStatus> result;
        
        auto now = std::chrono::steady_clock::now();
        for (const auto& [name, threadInfo] : threads_) {
            auto runningTime = std::chrono::duration_cast<std::chrono::milliseconds>(
                now - threadInfo->startTime);
            
            result.push_back({
                threadInfo->name,
                threadInfo->isRunning.load(),
                threadInfo->startTime,
                runningTime
            });
        }
        
        return result;
    }
    
    /**
     * 获取运行时间
     */
    std::chrono::milliseconds getUptime() const {
        std::lock_guard<std::mutex> lock(mutex_);
        if (!initialized_) {
            return std::chrono::milliseconds::zero();
        }
        
        auto now = std::chrono::steady_clock::now();
        return std::chrono::duration_cast<std::chrono::milliseconds>(now - startTime_);
    }

private:
    // 私有构造函数
    UnifiedThreadManager() = default;
    
    ~UnifiedThreadManager() {
        if (initialized_) {
            shutdown();
        }
    }
    
    // 线程池管理
    std::unique_ptr<ThreadPool> generalThreadPool_;
    std::unique_ptr<ThreadPool> ocrThreadPool_;
    std::unique_ptr<ThreadPool> visionThreadPool_;
    
    // 命名线程管理
    std::unordered_map<std::string, std::unique_ptr<ThreadInfo>> threads_;
    
    // 生命周期管理
    std::vector<std::function<void()>> shutdownCallbacks_;
    
    // 同步和状态
    mutable std::mutex mutex_;
    std::atomic<bool> initialized_{false};
    std::chrono::steady_clock::time_point startTime_;
}; 