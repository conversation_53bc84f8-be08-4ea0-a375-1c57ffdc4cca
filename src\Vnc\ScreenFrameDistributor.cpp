#include "ScreenFrameDistributor.h"
#include <GUI/consolelog/consolelog.h>
#include "VNCControl.h" // For InputTask enum and struct
#ifdef _WIN32
    #include <windows.h>
#endif
#include <algorithm>

ScreenFrameDistributor::ScreenFrameDistributor(int rows, int cols) : rows_(rows), cols_(cols) {
    // Ensure valid grid size
    if (rows_ <= 0 || cols_ <= 0) {
        throw std::invalid_argument("Grid size must be greater than 0");
    }
}

void ScreenFrameDistributor::setGridSize(int rows, int cols) {
    if (rows <= 0 || cols <= 0) {
        throw std::invalid_argument("Grid size must be greater than 0");
    }
    rows_ = rows;
    cols_ = cols;
}

int ScreenFrameDistributor::getTotalSlices() const {
    return rows_ * cols_;
}



// 计算指定编号的切片在原图中的位置
// 编号规则：从1开始，从左到右，从上到下
// 例如：3x3网格，编号为：
// 1 2 3
// 4 5 6
// 7 8 9
cv::Rect ScreenFrameDistributor::calculateSliceRect(int sliceNumber) const {
    if (sliceNumber < 1 || sliceNumber > getTotalSlices()) {
        throw std::out_of_range("Slice number out of range");
    }

    int row = (sliceNumber - 1) / cols_;
    int col = (sliceNumber - 1) % cols_;
    
    return cv::Rect(col, row, 1, 1);  // 返回逻辑坐标，实际像素坐标在getSliceImage中计算
}



// 获取指定编号的切片图像
cv::Mat ScreenFrameDistributor::getSliceImage(const cv::Mat& frame, int sliceNumber) const {
    if (frame.empty()) {
        throw std::invalid_argument("Input frame is empty");
    }
    
    cv::Rect rect = calculateSliceRect(sliceNumber);
    
    // 计算实际切片大小
    int sliceWidth = frame.cols / cols_;
    int sliceHeight = frame.rows / rows_;
    
    // 调整rect到实际像素位置
    rect.x *= sliceWidth;
    rect.y *= sliceHeight;
    rect.width = sliceWidth;
    rect.height = sliceHeight;
    
    // 边界检查，确保切片区域在图像范围内
    if (rect.x < 0 || rect.y < 0 || 
        rect.x + rect.width > frame.cols || 
        rect.y + rect.height > frame.rows) {
        AddLogInfo(LogLevel::Error, "切片区域超出图像边界: rect=(" + 
                   std::to_string(rect.x) + "," + std::to_string(rect.y) + "," + 
                   std::to_string(rect.width) + "," + std::to_string(rect.height) + 
                   "), frame=(" + std::to_string(frame.cols) + "," + std::to_string(frame.rows) + ")");
        throw std::out_of_range("Slice region out of frame bounds");
    }
    
    // 返回切片图像
    return frame(rect);
}


// 恢复切片内坐标为全局坐标
cv::Point ScreenFrameDistributor::toOriginal(const cv::Point& localPoint, int sliceNumber, const cv::Mat& sliceImage) const {
    // 参数验证
    if (sliceImage.empty()) {
        AddLogInfo(LogLevel::Error, "切片图片为空");
    }
    if (sliceNumber < 1 || sliceNumber > getTotalSlices()) {
        AddLogInfo(LogLevel::Error, "切片编号超出范围");
    }

    // 计算切片的行列位置
    int row = (sliceNumber - 1) / cols_;
    int col = (sliceNumber - 1) % cols_;

    // 使用切片图像的尺寸
    int sliceWidth = sliceImage.cols;  // 直接使用切片宽度
    int sliceHeight = sliceImage.rows; // 直接使用切片高度

    // 计算该切片在全局中的左上角坐标
    int globalX = col * sliceWidth + localPoint.x;
    int globalY = row * sliceHeight + localPoint.y;

    return cv::Point(globalX, globalY);
}

// ==========================================================================================
// ================================ 切片配置管理工具函数 ===================================
// ==========================================================================================

// 验证切片配置是否有效
bool ScreenFrameDistributor::isValidSliceConfiguration(int rows, int cols) {
    return (rows > 0 && cols > 0 && rows <= 4 && cols <= 4);
}



// 获取切片配置的描述信息
std::string ScreenFrameDistributor::getSliceConfigurationDescription(int rows, int cols) {
    if (!isValidSliceConfiguration(rows, cols)) {
        return "无效配置";
    }
    
    int totalSlices = rows * cols;
    return std::to_string(rows) + "行" + std::to_string(cols) + "列，总共" + std::to_string(totalSlices) + "个切片";
}

// 验证切片编号是否有效
bool ScreenFrameDistributor::isValidSliceNumber(int sliceNumber) const {
    return (sliceNumber >= 1 && sliceNumber <= getTotalSlices());
}

// 获取当前配置的详细信息
std::string ScreenFrameDistributor::getConfigurationInfo() const {
    std::string info = "切片配置: " + std::to_string(rows_) + "行" + std::to_string(cols_) + "列\n";
    info += "总切片数: " + std::to_string(getTotalSlices()) + "\n";
    info += "切片编号: ";
    
    for (int row = 0; row < rows_; ++row) {
        if (row > 0) info += "          ";
        for (int col = 0; col < cols_; ++col) {
            int sliceNumber = row * cols_ + col + 1;
            if (col > 0) info += " ";
            info += "[" + std::to_string(sliceNumber) + "]";
        }
        if (row < rows_ - 1) info += "\n";
    }
    
    return info;
}

// 将切片内坐标转换为全局屏幕坐标（不需要切片图像）
cv::Point ScreenFrameDistributor::sliceToGlobalCoordinate(const cv::Point& slicePoint, int sliceNumber) const {
    // 参数验证
    if (sliceNumber < 1 || sliceNumber > getTotalSlices()) {
        AddLogInfo(LogLevel::Error, "[ScreenFrameDistributor] 切片编号超出范围: " + std::to_string(sliceNumber));
        return slicePoint; // 返回原坐标作为fallback
    }

    // 🔧 优先使用VNC分辨率（虚拟机内部分辨率）
    int screenWidth, screenHeight;
    if (hasVNCResolution()) {
        auto [vncWidth, vncHeight] = getVNCResolution();
        screenWidth = vncWidth;
        screenHeight = vncHeight;
    } else {
        // 如果没有设置VNC分辨率，回退到系统分辨率
        auto [sysWidth, sysHeight] = getScreenSize();
        screenWidth = sysWidth;
        screenHeight = sysHeight;
    }
    
    // 计算切片大小
    int sliceWidth = screenWidth / cols_;
    int sliceHeight = screenHeight / rows_;
    
    // 计算切片的行列位置（切片编号从1开始）
    int row = (sliceNumber - 1) / cols_;
    int col = (sliceNumber - 1) % cols_;
    
    // 计算该切片在全局屏幕中的左上角坐标
    int sliceOffsetX = col * sliceWidth;
    int sliceOffsetY = row * sliceHeight;
    
    // 边界检查，确保坐标在切片范围内
    int clampedX = std::min(slicePoint.x, sliceWidth - 1);
    int clampedY = std::min(slicePoint.y, sliceHeight - 1);
    
    if (clampedX != slicePoint.x || clampedY != slicePoint.y) {
        AddLogInfo(LogLevel::Warning, "[ScreenFrameDistributor] 坐标被裁剪: (" + 
                   std::to_string(slicePoint.x) + "," + std::to_string(slicePoint.y) + ") -> (" + 
                   std::to_string(clampedX) + "," + std::to_string(clampedY) + ")");
    }
    
    // 转换为全局坐标
    int globalX = sliceOffsetX + clampedX;
    int globalY = sliceOffsetY + clampedY;
    
    // 简化坐标转换结果日志 - 仅在Debug级别输出
    AddLogInfo(LogLevel::Debug, "[ScreenFrameDistributor] 切片" + std::to_string(sliceNumber) + 
               " (" + std::to_string(slicePoint.x) + "," + std::to_string(slicePoint.y) + 
               ") -> (" + std::to_string(globalX) + "," + std::to_string(globalY) + ")");
    
    return cv::Point(globalX, globalY);
}

// 将切片内ROI转换为全局ROI
cv::Rect ScreenFrameDistributor::sliceToGlobalROI(const cv::Rect& sliceROI, int sliceNumber) const {
    cv::Point topLeft = sliceToGlobalCoordinate(cv::Point(sliceROI.x, sliceROI.y), sliceNumber);
    // ROI的宽度和高度不变，只需要转换左上角坐标
    cv::Rect globalROI(topLeft.x, topLeft.y, sliceROI.width, sliceROI.height);
    
    // 简化ROI转换日志
    AddLogInfo(LogLevel::Debug, "[ScreenFrameDistributor] ROI转换: 切片" + std::to_string(sliceNumber) + 
              " (" + std::to_string(sliceROI.x) + "," + std::to_string(sliceROI.y) + "," + 
              std::to_string(sliceROI.width) + "x" + std::to_string(sliceROI.height) + 
              ") -> (" + std::to_string(globalROI.x) + "," + std::to_string(globalROI.y) + "," + 
              std::to_string(globalROI.width) + "x" + std::to_string(globalROI.height) + ")");
    
    return globalROI;
}

// 将全局屏幕坐标转换为切片内坐标
cv::Point ScreenFrameDistributor::globalToSliceCoordinate(const cv::Point& globalPoint, int sliceNumber) const {
    // 参数验证
    if (sliceNumber < 1 || sliceNumber > getTotalSlices()) {
        AddLogInfo(LogLevel::Error, "[ScreenFrameDistributor] 切片编号超出范围: " + std::to_string(sliceNumber));
        return globalPoint; // 返回原坐标作为fallback
    }

    // 🔧 优先使用VNC分辨率（虚拟机内部分辨率）
    int screenWidth, screenHeight;
    if (hasVNCResolution()) {
        auto [vncWidth, vncHeight] = getVNCResolution();
        screenWidth = vncWidth;
        screenHeight = vncHeight;
    } else {
        // 如果没有设置VNC分辨率，回退到系统分辨率
        auto [sysWidth, sysHeight] = getScreenSize();
        screenWidth = sysWidth;
        screenHeight = sysHeight;
    }
    
    // 计算每个切片的尺寸
    int sliceWidth = screenWidth / cols_;
    int sliceHeight = screenHeight / rows_;
    
    // 计算当前切片的行列位置
    int sliceRow = (sliceNumber - 1) / cols_;
    int sliceCol = (sliceNumber - 1) % cols_;
    
    // 计算切片左上角在全局坐标系中的位置
    int sliceOffsetX = sliceCol * sliceWidth;
    int sliceOffsetY = sliceRow * sliceHeight;
    
    // 转换为切片内坐标
    cv::Point slicePoint(globalPoint.x - sliceOffsetX, globalPoint.y - sliceOffsetY);
    
    // 简化反向坐标转换日志
    AddLogInfo(LogLevel::Debug, "[ScreenFrameDistributor] 全局(" + 
              std::to_string(globalPoint.x) + "," + std::to_string(globalPoint.y) + 
              ") -> 切片" + std::to_string(sliceNumber) + "(" + 
              std::to_string(slicePoint.x) + "," + std::to_string(slicePoint.y) + ")");
    
    return slicePoint;
}

// 将切片输入任务转换为全局输入任务
std::vector<InputTask> ScreenFrameDistributor::convertSliceInputTasksToGlobal(int sliceNumber, const std::vector<InputTask>& sliceTasks) const {
    std::vector<InputTask> globalTasks;
    
    try {
        for (const auto& task : sliceTasks) {
            InputTask globalTask = task; // 复制原任务
            
            // 检查任务类型，只对鼠标操作任务进行坐标转换
            if (task.type == InputType::MOUSE_MOVE || task.type == InputType::MOUSE_LEFT_CLICK || 
                task.type == InputType::MOUSE_RIGHT_CLICK || task.type == InputType::DRAGMOUSE) {
                cv::Point slicePoint(task.x, task.y);
                cv::Point globalPoint = sliceToGlobalCoordinate(slicePoint, sliceNumber);
                
                globalTask.x = globalPoint.x;
                globalTask.y = globalPoint.y;
                
                // 移除冗余的输入任务转换日志 - 由sliceToGlobalCoordinate统一处理
            }
            
            globalTasks.push_back(globalTask);
        }
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScreenFrameDistributor] 输入任务坐标转换异常: " + std::string(e.what()));
        return sliceTasks; // 异常时返回原任务列表
    }
    
    return globalTasks;
}

// 获取系统屏幕尺寸
std::pair<int, int> ScreenFrameDistributor::getScreenSize() {
#ifdef _WIN32
    // Windows API获取主显示器尺寸
    int screenWidth = GetSystemMetrics(SM_CXSCREEN);
    int screenHeight = GetSystemMetrics(SM_CYSCREEN);
    
    if (screenWidth > 0 && screenHeight > 0) {
        // 仅在Debug级别输出系统分辨率信息
        AddLogInfo(LogLevel::Debug, "[ScreenFrameDistributor] 系统分辨率: " + 
                   std::to_string(screenWidth) + "x" + std::to_string(screenHeight));
        return {screenWidth, screenHeight};
    }
#endif
    
    // 如果API调用失败或不在Windows平台，使用默认值
    AddLogInfo(LogLevel::Warning, "[ScreenFrameDistributor] 无法获取屏幕尺寸，使用默认值 1920x1080");
    return {1920, 1080};
}

// 生成切片顺序
std::vector<int> ScreenFrameDistributor::generateSliceOrder(int rows, int cols) {
    std::vector<int> order;
    int totalSlices = rows * cols;
    
    // 生成从1到totalSlices的顺序
    for (int i = 1; i <= totalSlices; ++i) {
        order.push_back(i);
    }
    
    return order;
}

// 🔧 VNC分辨率管理方法实现
void ScreenFrameDistributor::setVNCResolution(int width, int height) {
    vncWidth_ = width;
    vncHeight_ = height;
    hasVNCResolution_ = true;
}

std::pair<int, int> ScreenFrameDistributor::getVNCResolution() const {
    return {vncWidth_, vncHeight_};
}

bool ScreenFrameDistributor::hasVNCResolution() const {
    return hasVNCResolution_;
}


