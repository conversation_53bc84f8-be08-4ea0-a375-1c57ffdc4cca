#include "visionconfig.h"
#include <imgui.h>
#include <nlohmann/json.hpp>
#include <fstream>
#include <set>
#include <algorithm>  // 添加algorithm头文件用于std::transform
#include <GUI/consolelog/consolelog.h>
#include <config/TemplatePathManager.h>
#include <windows.h>
#include <commdlg.h>
#include <filesystem>
#include <shellapi.h>  // 用于ShellExecute

// 全局变量
namespace VisionConfigEditor {
    std::vector<VisionConfigItem> g_configItems;
    std::vector<VisionConfigItem> g_filteredItems;  // 搜索过滤后的项目
    VisionConfigStatus g_currentStatus = VisionConfigStatus::NotLoaded;
    std::string g_statusMessage;
    bool g_isLoaded = false;
    bool g_isEditing = false;
    int g_editingIndex = -1;
    char g_editNameBuffer[256] = "";
    char g_editFileBuffer[512] = "";
    int g_editCacheValue = 0;
    char g_searchBuffer[256] = "";  // 搜索框缓冲区
    bool g_hasSearchFilter = false;  // 是否有搜索过滤
    bool g_showDeleteConfirmDialog = false;  // 是否显示删除确认对话框
    int g_itemToDelete = -1;  // 要删除的项目索引
}

// 获取exe文件所在目录的辅助函数
std::string GetExeDirectory() {
    #ifdef _WIN32
    wchar_t exePath[MAX_PATH];
    DWORD length = GetModuleFileNameW(NULL, exePath, MAX_PATH);
    if (length > 0) {
        std::filesystem::path path(exePath);
        return path.parent_path().string();
    }
    #endif
    // 如果获取失败，使用当前工作目录作为备选
    return std::filesystem::current_path().string();
}

// 获取项目根目录的辅助函数（以exe文件所在目录为根目录）
std::string GetProjectRootPath() {
    return GetExeDirectory();
}

// 检查文件是否存在（支持相对路径，以exe目录为根目录）
bool CheckFileExists(const std::string& filePath) {
    if (filePath.empty() || filePath == "./screen/") {
        return false; // 空路径或默认路径视为不存在
    }
    
    std::filesystem::path path(filePath);
    
    // 如果是相对路径，转换为以exe目录为根的绝对路径
    if (path.is_relative()) {
        std::filesystem::path exeDir = GetExeDirectory();
        path = exeDir / path;
    }
    
    return std::filesystem::exists(path);
}

// 转换为绝对路径
std::string ToAbsolutePath(const std::string& filePath) {
    std::filesystem::path path(filePath);
    
    if (path.is_relative()) {
        // 对于相对路径，使用应用程序所在目录作为基准，而不是当前工作目录
        std::filesystem::path appPath = GetExeDirectory();
        path = appPath / path;
    }
    
    // 标准化路径（解析 .. 和 . 等）
    std::error_code ec;
    std::filesystem::path canonicalPath = std::filesystem::canonical(path, ec);
    
    if (!ec) {
        return canonicalPath.string();
    } else {
        // 如果标准化失败，返回原始的绝对路径
        return path.string();
    }
}

// 将绝对路径转换为相对路径（相对于exe目录）
std::string ToRelativePath(const std::string& absolutePath) {
    std::filesystem::path absPath(absolutePath);
    std::filesystem::path exeDir = GetExeDirectory();
    
    try {
        // 尝试创建相对路径
        std::filesystem::path relativePath = std::filesystem::relative(absPath, exeDir);
        
        // 如果相对路径的第一个部分不是".."，说明文件在exe目录内或子目录内
        if (!relativePath.empty() && relativePath.begin()->string() != "..") {
            // 转换为./xxx格式
            std::string relativeStr = "./" + relativePath.string();
            // 统一使用正斜杠
            std::replace(relativeStr.begin(), relativeStr.end(), '\\', '/');
            return relativeStr;
        }
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Warning, "[VisionConfig] 转换相对路径失败: " + std::string(e.what()));
    }
    
    // 如果转换失败或文件不在exe目录内，返回原始绝对路径
    return absolutePath;
}

// 在文件管理器中显示文件
void ShowFileInExplorer(const std::string& filePath) {
    AddLogInfo(LogLevel::Info, "[VisionConfig] 尝试打开文件: " + filePath);
    
    std::string absolutePath = ToAbsolutePath(filePath);
    AddLogInfo(LogLevel::Info, "[VisionConfig] 转换后的绝对路径: " + absolutePath);
    
    if (!CheckFileExists(filePath)) {
        VisionConfigEditor::g_currentStatus = VisionConfigStatus::LoadFailed;
        VisionConfigEditor::g_statusMessage = "文件不存在: " + absolutePath;
        AddLogInfo(LogLevel::Error, "[VisionConfig] " + VisionConfigEditor::g_statusMessage);
        return;
    }
    
    // 将路径转换为Windows格式（使用反斜杠）
    std::filesystem::path winPath(absolutePath);
    std::string windowsPath = winPath.make_preferred().string();
    AddLogInfo(LogLevel::Info, "[VisionConfig] Windows格式路径: " + windowsPath);
    
    #ifdef _WIN32
    // 方法1: 使用 ShellExecute API 
    std::wstring wideWindowsPath = std::filesystem::path(windowsPath).wstring();
    HINSTANCE result = ShellExecuteW(NULL, L"open", L"explorer.exe", 
                                     (L"/select,\"" + wideWindowsPath + L"\"").c_str(), 
                                     NULL, SW_SHOWNORMAL);
    
    if ((intptr_t)result > 32) {
        AddLogInfo(LogLevel::Info, "[VisionConfig] ShellExecute 成功打开文件管理器");
        return;
    }
    
    AddLogInfo(LogLevel::Warning, "[VisionConfig] ShellExecute 失败，错误代码: " + std::to_string((intptr_t)result));
    
    // 方法2: 使用 system() 作为备选方案
    std::string fallbackCommand = "explorer.exe /select,\"" + windowsPath + "\"";
    AddLogInfo(LogLevel::Info, "[VisionConfig] 尝试备选命令: " + fallbackCommand);
    int systemResult = system(fallbackCommand.c_str());
    
    if (systemResult == 0) {
        AddLogInfo(LogLevel::Info, "[VisionConfig] system() 成功打开文件管理器");
        return;
    }
    
    AddLogInfo(LogLevel::Warning, "[VisionConfig] system() 失败，返回值: " + std::to_string(systemResult));
    
    // 方法3: 直接打开文件夹（不选中文件）
    std::filesystem::path parentDir = std::filesystem::path(windowsPath).parent_path();
    std::string parentDirStr = parentDir.string();
    std::string dirCommand = "explorer.exe \"" + parentDirStr + "\"";
    AddLogInfo(LogLevel::Info, "[VisionConfig] 尝试打开父目录: " + dirCommand);
    int dirResult = system(dirCommand.c_str());
    
    if (dirResult == 0) {
        AddLogInfo(LogLevel::Info, "[VisionConfig] 成功打开父目录");
    } else {
        VisionConfigEditor::g_currentStatus = VisionConfigStatus::LoadFailed;
        VisionConfigEditor::g_statusMessage = "无法打开文件管理器";
        AddLogInfo(LogLevel::Error, "[VisionConfig] 所有方法都失败了");
    }
    
    #else
    // 非Windows系统的处理
    std::string command = "explorer.exe /select,\"" + windowsPath + "\"";
    system(command.c_str());
    #endif
}

// 过滤配置项
void FilterConfigItems() {
    VisionConfigEditor::g_filteredItems.clear();
    
    if (strlen(VisionConfigEditor::g_searchBuffer) == 0) {
        VisionConfigEditor::g_filteredItems = VisionConfigEditor::g_configItems;
        VisionConfigEditor::g_hasSearchFilter = false;
    } else {
        std::string searchTerm = VisionConfigEditor::g_searchBuffer;
        std::transform(searchTerm.begin(), searchTerm.end(), searchTerm.begin(), ::tolower);
        
        for (const auto& item : VisionConfigEditor::g_configItems) {
            std::string itemName = item.name;
            std::transform(itemName.begin(), itemName.end(), itemName.begin(), ::tolower);
            
            if (itemName.find(searchTerm) != std::string::npos) {
                VisionConfigEditor::g_filteredItems.push_back(item);
            }
        }
        VisionConfigEditor::g_hasSearchFilter = true;
    }
}

// 检查重复名称和文件路径
void VisionConfigEditor::CheckDuplicateNames() {
    std::set<std::string> nameSet;
    std::set<std::string> fileSet;
    std::set<std::string> duplicateNames;
    std::set<std::string> duplicateFiles;
    
    // 首先重置所有重复标记
    for (auto& item : g_configItems) {
        item.isDuplicate = false;
    }
    
    // 找出所有重复的名称
    for (const auto& item : g_configItems) {
        if (nameSet.find(item.name) != nameSet.end()) {
            duplicateNames.insert(item.name);
        } else {
            nameSet.insert(item.name);
        }
    }
    
    // 找出所有重复的文件路径
    for (const auto& item : g_configItems) {
        if (!item.file.empty() && item.file != "./screen/") { // 忽略空路径和默认路径
            if (fileSet.find(item.file) != fileSet.end()) {
                duplicateFiles.insert(item.file);
            } else {
                fileSet.insert(item.file);
            }
        }
    }
    
    // 标记重复的项（Name或File重复都标记为重复）
    for (auto& item : g_configItems) {
        if (duplicateNames.find(item.name) != duplicateNames.end() || 
            duplicateFiles.find(item.file) != duplicateFiles.end()) {
            item.isDuplicate = true;
        }
    }
    
    // 更新状态
    if (!duplicateNames.empty() || !duplicateFiles.empty()) {
        g_currentStatus = VisionConfigStatus::DuplicateNames;
        g_statusMessage = "发现重复项: ";
        
        if (!duplicateNames.empty()) {
            g_statusMessage += "Name(";
            for (const auto& name : duplicateNames) {
                g_statusMessage += name + " ";
            }
            g_statusMessage += ") ";
        }
        
        if (!duplicateFiles.empty()) {
            g_statusMessage += "File(";
            for (const auto& file : duplicateFiles) {
                std::filesystem::path filePath(file);
                g_statusMessage += filePath.filename().string() + " ";
            }
            g_statusMessage += ")";
        }
        
        AddLogInfo(LogLevel::Error, "[VisionConfig] " + g_statusMessage);
    }
}

// 加载vision_config.json
bool VisionConfigEditor::LoadVisionConfig(const std::string& configPath) {
    g_currentStatus = VisionConfigStatus::Loading;
    g_configItems.clear();
    g_filteredItems.clear();
    
    // 清空搜索框
    g_searchBuffer[0] = '\0';
    g_hasSearchFilter = false;
    
    // 构建正确的配置文件路径（以exe目录为根）
    std::string actualConfigPath;
    if (configPath.find("./") == 0) {
        // 相对路径，转换为以exe目录为根的绝对路径
        std::filesystem::path exeDir = GetProjectRootPath();
        std::filesystem::path relPath = configPath.substr(2);
        actualConfigPath = (exeDir / relPath).string();
    } else {
        actualConfigPath = configPath;
    }
    

    try {
        std::ifstream file(actualConfigPath);
        if (!file.is_open()) {
            g_currentStatus = VisionConfigStatus::LoadFailed;
            g_statusMessage = "无法打开配置文件: " + actualConfigPath;
            AddLogInfo(LogLevel::Error, "[VisionConfig] " + g_statusMessage);
            return false;
        }
        
        nlohmann::json jsonConfig;
        file >> jsonConfig;
        
        if (!jsonConfig.contains("imgTemp") || !jsonConfig["imgTemp"].is_array()) {
            g_currentStatus = VisionConfigStatus::LoadFailed;
            g_statusMessage = "配置文件格式错误：缺少imgTemp数组";
            AddLogInfo(LogLevel::Error, "[VisionConfig] " + g_statusMessage);
            return false;
        }
        
        int missingFileCount = 0;
        for (const auto& item : jsonConfig["imgTemp"]) {
            if (item.contains("name") && item.contains("file")) {
                VisionConfigItem configItem;
                configItem.name = item["name"].get<std::string>();
                configItem.file = item["file"].get<std::string>();
                configItem.isCache = item.contains("isCache") ? item["isCache"].get<int>() : 0;
                
                // 检查文件是否存在
                if (!CheckFileExists(configItem.file)) {
                    missingFileCount++;
                    AddLogInfo(LogLevel::Warning, "[VisionConfig] 文件不存在: " + configItem.file);
                }
                
                g_configItems.push_back(configItem);
            }
        }
        
        // 先检查重复项，设置 isDuplicate 标记
        CheckDuplicateNames();
        
        // 然后初始化过滤列表，此时 isDuplicate 标记已正确设置
        FilterConfigItems();
        
        if (g_currentStatus != VisionConfigStatus::DuplicateNames) {
            g_currentStatus = VisionConfigStatus::LoadSuccess;
            g_statusMessage = "成功加载Vison图片 " + std::to_string(g_configItems.size()) + " 项";
            if (missingFileCount > 0) {
                g_statusMessage += " (其中 " + std::to_string(missingFileCount) + " 个文件不存在)";
            }
        }
        
        g_isLoaded = true;
        AddLogInfo(LogLevel::Info, "[VisionConfig] " + g_statusMessage);
        return true;
        
    } catch (const std::exception& e) {
        g_currentStatus = VisionConfigStatus::LoadFailed;
        g_statusMessage = "解析配置文件失败: " + std::string(e.what());
        AddLogInfo(LogLevel::Error, "[VisionConfig] " + g_statusMessage);
        return false;
    }
}

// 保存vision_config.json
bool VisionConfigEditor::SaveVisionConfig(const std::string& configPath) {
    // 构建正确的配置文件路径（以exe目录为根）
    std::string actualConfigPath;
    if (configPath.find("./") == 0) {
        // 相对路径，转换为以exe目录为根的绝对路径
        std::filesystem::path exeDir = GetProjectRootPath();
        std::filesystem::path relPath = configPath.substr(2);
        actualConfigPath = (exeDir / relPath).string();
    } else {
        actualConfigPath = configPath;
    }
    
    AddLogInfo(LogLevel::Info, "[VisionConfig] 尝试保存配置文件: " + actualConfigPath);
    
    try {
        nlohmann::json jsonConfig;
        nlohmann::json imgTempArray = nlohmann::json::array();
        
        for (const auto& item : g_configItems) {
            nlohmann::json jsonItem;
            jsonItem["name"] = item.name;
            jsonItem["file"] = item.file;
            jsonItem["isCache"] = item.isCache;
            imgTempArray.push_back(jsonItem);
        }
        
        jsonConfig["imgTemp"] = imgTempArray;
        
        // 确保目录存在
        std::filesystem::path configFilePath(actualConfigPath);
        std::filesystem::path configDir = configFilePath.parent_path();
        if (!std::filesystem::exists(configDir)) {
            std::filesystem::create_directories(configDir);
            AddLogInfo(LogLevel::Info, "[VisionConfig] 已创建配置目录: " + configDir.string());
        }
        
        std::ofstream file(actualConfigPath);
        if (!file.is_open()) {
            g_currentStatus = VisionConfigStatus::SaveFailed;
            g_statusMessage = "无法写入配置文件: " + actualConfigPath;
            AddLogInfo(LogLevel::Error, "[VisionConfig] " + g_statusMessage);
            return false;
        }
        
        file << jsonConfig.dump(4); // 格式化输出
        file.close();
        
        g_currentStatus = VisionConfigStatus::SaveSuccess;
        g_statusMessage = "配置已保存成功";
        AddLogInfo(LogLevel::Info, "[VisionConfig] " + g_statusMessage);
        
        // 重新加载到TemplatePathManager
        ReloadToTemplateManager();
        
        return true;
        
    } catch (const std::exception& e) {
        g_currentStatus = VisionConfigStatus::SaveFailed;
        g_statusMessage = "保存配置文件失败: " + std::string(e.what());
        AddLogInfo(LogLevel::Error, "[VisionConfig] " + g_statusMessage);
        return false;
    }
}

// 获取当前状态
VisionConfigStatus VisionConfigEditor::GetCurrentStatus() {
    return g_currentStatus;
}

// 获取状态显示
std::pair<std::string, ImVec4> VisionConfigEditor::GetStatusDisplay() {
    switch (g_currentStatus) {
        case VisionConfigStatus::NotLoaded:
            return {"未加载", ImVec4(0.7f, 0.7f, 0.7f, 1.0f)};
        case VisionConfigStatus::Loading:
            return {"加载中...", ImVec4(1.0f, 1.0f, 0.0f, 1.0f)};
        case VisionConfigStatus::LoadSuccess:
            return {g_statusMessage, ImVec4(0.4f, 1.0f, 0.4f, 1.0f)};
        case VisionConfigStatus::LoadFailed:
            return {g_statusMessage, ImVec4(1.0f, 0.0f, 0.0f, 1.0f)};
        case VisionConfigStatus::SaveSuccess:
            return {g_statusMessage, ImVec4(0.4f, 1.0f, 0.4f, 1.0f)};
        case VisionConfigStatus::SaveFailed:
            return {g_statusMessage, ImVec4(1.0f, 0.0f, 0.0f, 1.0f)};
        case VisionConfigStatus::DuplicateNames:
            return {"当前name属性重复", ImVec4(1.0f, 0.0f, 0.0f, 1.0f)};
        default:
            return {"未知状态", ImVec4(0.7f, 0.7f, 0.7f, 1.0f)};
    }
}

// 重新加载到TemplatePathManager
void VisionConfigEditor::ReloadToTemplateManager() {
    std::filesystem::path exeDir = GetProjectRootPath();
    std::filesystem::path configPath = exeDir / "assets" / "vision_config.json";
    TemplatePathManager::getInstance().loadFromJson(configPath.string());
}

// 检查配置是否可以用于任务执行
bool VisionConfigEditor::IsConfigReadyForTask(std::string* errorMessage) {
    // 检查是否已加载
    if (!g_isLoaded) {
        if (errorMessage) {
            *errorMessage = "匹配设置尚未加载，请先加载vision_config.json";
        }
        return false;
    }
    
    // 检查是否有配置项
    if (g_configItems.empty()) {
        if (errorMessage) {
            *errorMessage = "匹配设置为空，请先添加图片配置";
        }
        return false;
    }
    
    // 检查当前状态
    VisionConfigStatus currentStatus = GetCurrentStatus();
    
    // 如果有重复名称，不允许执行任务
    if (currentStatus == VisionConfigStatus::DuplicateNames) {
        if (errorMessage) {
            *errorMessage = "匹配设置中存在重复的name或file，请先修复匹配设置中的图片问题";
        }
        return false;
    }
    
    // 检查是否有加载失败的状态
    if (currentStatus == VisionConfigStatus::LoadFailed) {
        if (errorMessage) {
            *errorMessage = "匹配设置加载失败，请检查配置文件";
        }
        return false;
    }
    
    // 检查是否有缺失的文件
    int missingFileCount = 0;
    for (const auto& item : g_configItems) {
        if (!CheckFileExists(item.file)) {
            missingFileCount++;
        }
    }
    
    if (missingFileCount > 0) {
        if (errorMessage) {
            *errorMessage = "匹配设置中有 " + std::to_string(missingFileCount) + " 个文件不存在，请先修复匹配设置中的图片问题";
        }
        return false;
    }
    
    // 所有检查通过
    return true;
}

// 文件选择对话框
std::string OpenJpgFileDialog(const std::string& currentFilePath = "") {
    OPENFILENAMEA ofn;
    char szFile[260] = {0};
    char szInitialDir[260] = {0};
    
    // 如果提供了当前文件路径，设置初始目录为该文件所在的目录
    if (!currentFilePath.empty()) {
        std::string absolutePath = ToAbsolutePath(currentFilePath);
        std::filesystem::path filePath(absolutePath);
        
        // 检查是否包含./screen/路径，如果是，直接使用./screen/作为初始目录
        if (currentFilePath.find("./screen/") != std::string::npos || currentFilePath.find(".\\screen\\") != std::string::npos) {
            // 使用exe文件所在目录作为根目录
            std::filesystem::path exeDir = GetExeDirectory();
            std::filesystem::path screenDir = exeDir / "screen";
            if (std::filesystem::exists(screenDir)) {
                std::string screenDirStr = screenDir.string();
                strcpy_s(szInitialDir, screenDirStr.c_str());
                AddLogInfo(LogLevel::Info, "[VisionConfig] 文件对话框初始目录（screen目录）: " + screenDirStr);
            } else {
                // screen目录不存在，尝试创建
                try {
                    std::filesystem::create_directories(screenDir);
                    std::string screenDirStr = screenDir.string();
                    strcpy_s(szInitialDir, screenDirStr.c_str());
                    AddLogInfo(LogLevel::Info, "[VisionConfig] 已创建screen目录: " + screenDirStr);
                } catch (const std::exception& e) {
                    AddLogInfo(LogLevel::Warning, "[VisionConfig] 无法创建screen目录: " + std::string(e.what()));
                }
            }
        }
        else if (std::filesystem::exists(filePath)) {
            // 文件存在，使用其父目录
            std::filesystem::path parentDir = filePath.parent_path();
            std::string parentDirStr = parentDir.string();
            strcpy_s(szInitialDir, parentDirStr.c_str());
            
            // 同时在szFile中设置当前文件名，这样对话框会预选当前文件
            strcpy_s(szFile, filePath.filename().string().c_str());
            
            AddLogInfo(LogLevel::Info, "[VisionConfig] 文件对话框初始目录: " + parentDirStr);
        } else {
            // 文件不存在，尝试使用路径中的目录部分
            std::filesystem::path parentDir = filePath.parent_path();
            if (std::filesystem::exists(parentDir)) {
                std::string parentDirStr = parentDir.string();
                strcpy_s(szInitialDir, parentDirStr.c_str());
                AddLogInfo(LogLevel::Info, "[VisionConfig] 文件对话框初始目录（文件不存在）: " + parentDirStr);
            } else {
                AddLogInfo(LogLevel::Warning, "[VisionConfig] 无法确定初始目录，使用默认路径");
            }
        }
    }
    
    ZeroMemory(&ofn, sizeof(ofn));
    ofn.lStructSize = sizeof(ofn);
    ofn.lpstrFile = szFile;
    ofn.nMaxFile = sizeof(szFile);
    ofn.lpstrFilter = "JPEG Files\0*.jpg;*.jpeg\0All Files\0*.*\0";
    ofn.nFilterIndex = 1;
    ofn.lpstrFileTitle = NULL;
    ofn.nMaxFileTitle = 0;
    ofn.lpstrInitialDir = strlen(szInitialDir) > 0 ? szInitialDir : NULL;
    ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST;
    
    if (GetOpenFileNameA(&ofn)) {
        std::string selectedAbsolutePath = std::string(szFile);
        AddLogInfo(LogLevel::Info, "[VisionConfig] 用户选择的文件（绝对路径）: " + selectedAbsolutePath);
        
        // 转换为相对路径
        std::string selectedRelativePath = ToRelativePath(selectedAbsolutePath);
        AddLogInfo(LogLevel::Info, "[VisionConfig] 转换后的相对路径: " + selectedRelativePath);
        
        return selectedRelativePath;
    }
    return "";
}

// 主显示函数
void ShowVisionConfigEditor() {

    // 状态显示和按钮区域
    ImGui::BeginChild("StatusAndButtons", ImVec2(0, 80), true);
    
    // 状态显示
    auto [statusText, statusColor] = VisionConfigEditor::GetStatusDisplay();
    ImGui::TextColored(statusColor, "状态: %s", statusText.c_str());
    
    ImGui::Spacing();
    
    // 按钮和搜索区域
    const char* loadButtonText = VisionConfigEditor::g_isLoaded ? "重新加载" : "立即加载";
    if (ImGui::Button(loadButtonText, ImVec2(100, 30))) {
        VisionConfigEditor::LoadVisionConfig();
    }
    
    // 添加间距
    ImGui::SameLine();
    ImGui::Dummy(ImVec2(20, 0));
    
    // 搜索框 - 调整高度和垂直居中
    ImGui::SameLine();
    ImGui::AlignTextToFramePadding();  // 确保文本垂直居中
    ImGui::Text("搜索:");
    ImGui::SameLine();
    ImGui::SetNextItemWidth(200);
    ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(ImGui::GetStyle().FramePadding.x, 6.0f));  // 调整垂直padding
    if (ImGui::InputTextWithHint("##search", "输入name搜索...", VisionConfigEditor::g_searchBuffer, sizeof(VisionConfigEditor::g_searchBuffer))) {
        FilterConfigItems();
    }
    ImGui::PopStyleVar();
    
    // 清除搜索按钮（移到前面）
    ImGui::SameLine();
    ImGui::Dummy(ImVec2(10, 0));
    ImGui::SameLine();
    if (ImGui::Button("清除", ImVec2(60, 30))) {
        VisionConfigEditor::g_searchBuffer[0] = '\0';
        FilterConfigItems();
    }
    
    // 增加图片按钮 - 浅绿色（移到后面）
    ImGui::SameLine();
    ImGui::Dummy(ImVec2(10, 0));
    ImGui::SameLine();
    ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.4f, 0.8f, 0.4f, 1.0f));         // 浅绿色
    ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.5f, 0.9f, 0.5f, 1.0f));  // 悬停更亮
    ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.3f, 0.7f, 0.3f, 1.0f));   // 按下更暗
    if (ImGui::Button("增加图片", ImVec2(80, 30))) {
        // 创建新的配置项
        VisionConfigItem newItem;
        newItem.name = "matchName";
        newItem.file = "./screen/";
        newItem.isCache = 0;
        newItem.isDuplicate = false;
        
        // 插入到第一行
        VisionConfigEditor::g_configItems.insert(VisionConfigEditor::g_configItems.begin(), newItem);
        
        // 设置为编辑状态
        VisionConfigEditor::g_isEditing = true;
        VisionConfigEditor::g_editingIndex = 0;  // 新插入的项在第一行，索引为0
        strcpy_s(VisionConfigEditor::g_editNameBuffer, newItem.name.c_str());
        strcpy_s(VisionConfigEditor::g_editFileBuffer, newItem.file.c_str());
        VisionConfigEditor::g_editCacheValue = newItem.isCache;
        
        // 重新过滤和检查重复
        VisionConfigEditor::CheckDuplicateNames();
        FilterConfigItems();
        
        AddLogInfo(LogLevel::Info, "[VisionConfig] 已添加新的图片配置项，等待编辑");
    }
    ImGui::PopStyleColor(3);
    ImGui::SameLine();
    ImGui::Text("提示:只支持jpg图片");
    ImGui::EndChild();
    
    // 配置项列表
    if (VisionConfigEditor::g_isLoaded) {
        ImGui::BeginChild("ConfigList", ImVec2(0, 0), true);
        
        // 显示搜索结果信息
        if (VisionConfigEditor::g_hasSearchFilter) {
            ImGui::TextColored(ImVec4(0.0f, 0.8f, 1.0f, 1.0f), "搜索结果: %d 项 (共 %d 项)", 
                              (int)VisionConfigEditor::g_filteredItems.size(), 
                              (int)VisionConfigEditor::g_configItems.size());
            ImGui::Separator();
        }
        
        // 表格 - 设置固定宽度
        ImGui::SetNextItemWidth(800.0f);  // 设置表格总宽度
        if (ImGui::BeginTable("VisionConfigTable", 4, ImGuiTableFlags_Borders | ImGuiTableFlags_RowBg | ImGuiTableFlags_Resizable | ImGuiTableFlags_SizingFixedFit)) {
            ImGui::TableSetupColumn("Name", ImGuiTableColumnFlags_WidthFixed, 150.0f);
            ImGui::TableSetupColumn("File", ImGuiTableColumnFlags_WidthStretch);  // 自适应宽度
            ImGui::TableSetupColumn("Cache", ImGuiTableColumnFlags_WidthFixed, 60.0f);
            ImGui::TableSetupColumn("操作", ImGuiTableColumnFlags_WidthFixed, 200.0f);  
            ImGui::TableHeadersRow();
            
            // 数据行
            for (int i = 0; i < VisionConfigEditor::g_filteredItems.size(); ++i) {
                const auto& item = VisionConfigEditor::g_filteredItems[i];
                
                // 找到原始索引 - 通过比较name和file来确保唯一性
                int originalIndex = -1;
                for (int j = 0; j < VisionConfigEditor::g_configItems.size(); ++j) {
                    if (VisionConfigEditor::g_configItems[j].name == item.name && 
                        VisionConfigEditor::g_configItems[j].file == item.file) {
                        originalIndex = j;
                        break;
                    }
                }
                
                if (originalIndex == -1) continue;  // 安全检查
                
                ImGui::TableNextRow();
                
                // 移除这个过滤逻辑，让所有项都显示，重复项用红色标记即可
                
                // Name列
                ImGui::TableNextColumn();
                if (item.isDuplicate) {
                    ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.0f, 0.0f, 1.0f));
                }
                
                if (VisionConfigEditor::g_isEditing && VisionConfigEditor::g_editingIndex == originalIndex) {
                    ImGui::SetNextItemWidth(-1);
                    ImGui::InputText(("##name" + std::to_string(originalIndex)).c_str(), VisionConfigEditor::g_editNameBuffer, sizeof(VisionConfigEditor::g_editNameBuffer));
                } else {
                    ImGui::Text("%s", item.name.c_str());
                }
                
                if (item.isDuplicate) {
                    ImGui::PopStyleColor();
                }
                
                // File列
                ImGui::TableNextColumn();
                if (VisionConfigEditor::g_isEditing && VisionConfigEditor::g_editingIndex == originalIndex) {
                    ImGui::SetNextItemWidth(-FLT_MIN);
                    if (ImGui::Button(("选择文件##" + std::to_string(originalIndex)).c_str())) {
                        // 传递当前正在编辑的文件路径，这样对话框会默认到该文件所在的目录
                        std::string currentFilePath = VisionConfigEditor::g_editFileBuffer;
                        std::string selectedFile = OpenJpgFileDialog(currentFilePath);
                        if (!selectedFile.empty()) {
                            strcpy_s(VisionConfigEditor::g_editFileBuffer, selectedFile.c_str());
                        }
                    }
                    ImGui::SameLine();
                    ImGui::Text("%s", VisionConfigEditor::g_editFileBuffer);
                } else {
                    // 只显示文件名，不显示完整路径（太长了）
                    std::filesystem::path filePath(item.file);
                    
                    // 检查文件是否存在或是否重复，都显示为红色
                    bool shouldDisplayRed = !CheckFileExists(item.file) || item.isDuplicate;
                    
                    if (shouldDisplayRed) {
                        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.0f, 0.0f, 1.0f));
                        if (!CheckFileExists(item.file)) {
                            ImGui::Text("[缺失] %s", filePath.filename().string().c_str());
                        } else {
                            ImGui::Text("%s", filePath.filename().string().c_str());
                        }
                        ImGui::PopStyleColor();
                    } else {
                        ImGui::Text("%s", filePath.filename().string().c_str());
                    }
                    
                    if (ImGui::IsItemHovered()) {
                        ImGui::SetTooltip("%s", item.file.c_str());
                    }
                    
                    // 右键菜单
                    if (ImGui::BeginPopupContextItem(("context" + std::to_string(originalIndex)).c_str())) {
                        if (ImGui::MenuItem("查看图片")) {
                            ShowFileInExplorer(item.file);
                        }
                        ImGui::EndPopup();
                    }
                }
                
                // Cache列
                ImGui::TableNextColumn();
                if (VisionConfigEditor::g_isEditing && VisionConfigEditor::g_editingIndex == originalIndex) {
                    ImGui::SetNextItemWidth(-1);
                    if (ImGui::Combo(("##cache" + std::to_string(originalIndex)).c_str(), &VisionConfigEditor::g_editCacheValue, "0\0001\000")) {
                        // Combo changed
                    }
                } else {
                    if (item.isDuplicate) {
                        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.0f, 0.0f, 1.0f));
                        ImGui::Text("%d", item.isCache);
                        ImGui::PopStyleColor();
                    } else {
                        ImGui::Text("%d", item.isCache);
                    }
                }
                
                // 操作列
                ImGui::TableNextColumn();
                if (VisionConfigEditor::g_isEditing && VisionConfigEditor::g_editingIndex == originalIndex) {
                    if (ImGui::Button(("保存##" + std::to_string(originalIndex)).c_str(), ImVec2(60, 0))) {
                        // 保存编辑到原始数组
                        VisionConfigEditor::g_configItems[originalIndex].name = VisionConfigEditor::g_editNameBuffer;
                        VisionConfigEditor::g_configItems[originalIndex].file = VisionConfigEditor::g_editFileBuffer;
                        VisionConfigEditor::g_configItems[originalIndex].isCache = VisionConfigEditor::g_editCacheValue;
                        
                        VisionConfigEditor::g_isEditing = false;
                        VisionConfigEditor::g_editingIndex = -1;
                        
                        // 重新检查重复名称和过滤
                        VisionConfigEditor::CheckDuplicateNames();
                        FilterConfigItems();
                        
                        // 保存到文件
                        VisionConfigEditor::SaveVisionConfig();
                    }
                    ImGui::SameLine();
                    ImGui::Dummy(ImVec2(8, 0));  // 调整间距
                    ImGui::SameLine();
                    if (ImGui::Button(("取消##" + std::to_string(originalIndex)).c_str(), ImVec2(60, 0))) {
                        VisionConfigEditor::g_isEditing = false;
                        VisionConfigEditor::g_editingIndex = -1;
                    }
                } else {
                    if (ImGui::Button(("编辑##" + std::to_string(originalIndex)).c_str(), ImVec2(60, 0))) {
                        if (!VisionConfigEditor::g_isEditing) {
                            VisionConfigEditor::g_isEditing = true;
                            VisionConfigEditor::g_editingIndex = originalIndex;
                            strcpy_s(VisionConfigEditor::g_editNameBuffer, item.name.c_str());
                            strcpy_s(VisionConfigEditor::g_editFileBuffer, item.file.c_str());
                            VisionConfigEditor::g_editCacheValue = item.isCache;
                        }
                    }
                    ImGui::SameLine();
                    ImGui::Dummy(ImVec2(8, 0));  // 调整间距
                    ImGui::SameLine();
                    // --- 删除按钮红色美化 ---
                    ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.8f, 0.2f, 0.2f, 1.0f));        // 红色
                    ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.9f, 0.3f, 0.3f, 1.0f)); // 悬停更亮
                    ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.7f, 0.1f, 0.1f, 1.0f));  // 按下更暗
                    if (ImGui::Button(("删除##" + std::to_string(originalIndex)).c_str(), ImVec2(60, 0))) {
                        VisionConfigEditor::g_showDeleteConfirmDialog = true;
                        VisionConfigEditor::g_itemToDelete = originalIndex;
                    }
                    ImGui::PopStyleColor(3);
                }
            }
            
            ImGui::EndTable();
        }
        
        ImGui::EndChild();
    } else {
        ImGui::Text("请点击'立即加载'按钮来加载配置文件");
    }
    
    // 删除确认对话框
    if (VisionConfigEditor::g_showDeleteConfirmDialog && VisionConfigEditor::g_itemToDelete >= 0) {
        ImGui::OpenPopup("删除确认");
        ImGui::SetNextWindowPos(ImGui::GetMainViewport()->GetCenter(), ImGuiCond_Appearing, ImVec2(0.5f, 0.5f));
        if (ImGui::BeginPopupModal("删除确认", nullptr, ImGuiWindowFlags_AlwaysAutoResize)) {
            ImGui::Text("确定要删除以下配置项吗？");
            
            // 显示要删除的项目信息
            if (VisionConfigEditor::g_itemToDelete < VisionConfigEditor::g_configItems.size()) {
                const auto& itemToDelete = VisionConfigEditor::g_configItems[VisionConfigEditor::g_itemToDelete];
                ImGui::Text("名称: %s", itemToDelete.name.c_str());
                ImGui::Text("文件: %s", itemToDelete.file.c_str());
            }
            
            ImGui::Separator();
            
            if (ImGui::Button("确定", ImVec2(120, 0))) {
                // 执行删除操作
                if (VisionConfigEditor::g_itemToDelete < VisionConfigEditor::g_configItems.size()) {
                    std::string deletedName = VisionConfigEditor::g_configItems[VisionConfigEditor::g_itemToDelete].name;
                    VisionConfigEditor::g_configItems.erase(VisionConfigEditor::g_configItems.begin() + VisionConfigEditor::g_itemToDelete);
                    
                    // 如果当前正在编辑被删除的项目，退出编辑状态
                    if (VisionConfigEditor::g_isEditing && VisionConfigEditor::g_editingIndex == VisionConfigEditor::g_itemToDelete) {
                        VisionConfigEditor::g_isEditing = false;
                        VisionConfigEditor::g_editingIndex = -1;
                    } else if (VisionConfigEditor::g_isEditing && VisionConfigEditor::g_editingIndex > VisionConfigEditor::g_itemToDelete) {
                        // 如果编辑的是被删除项目后面的项目，需要调整索引
                        VisionConfigEditor::g_editingIndex--;
                    }
                    
                    // 重新检查重复名称和过滤
                    VisionConfigEditor::CheckDuplicateNames();
                    FilterConfigItems();
                    
                    // 保存到文件
                    VisionConfigEditor::SaveVisionConfig();
                    
                    AddLogInfo(LogLevel::Info, "[VisionConfig] 已删除配置项: " + deletedName);
                }
                
                ImGui::CloseCurrentPopup();
                VisionConfigEditor::g_showDeleteConfirmDialog = false;
                VisionConfigEditor::g_itemToDelete = -1;
            }
            ImGui::SameLine();
            if (ImGui::Button("取消", ImVec2(120, 0))) {
                ImGui::CloseCurrentPopup();
                VisionConfigEditor::g_showDeleteConfirmDialog = false;
                VisionConfigEditor::g_itemToDelete = -1;
            }
            ImGui::EndPopup();
        }
    }
} 