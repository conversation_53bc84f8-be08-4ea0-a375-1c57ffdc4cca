#ifndef INPUT_SIMULATOR_H
#define INPUT_SIMULATOR_H

#include <vector>
#include <string>
#include <windows.h> // 包含 Windows API 头文件
#include <random>

class InputSimulator {
public:
    enum OperationType {
        MouseMove<PERSON>,
        <PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON>,
        Ty<PERSON>,
        Hotkey
    };

    InputSimulator();
    ~InputSimulator();

    void moveMouse(int x, int y, bool human_like = true);
    void moveMouseRelative(int dx, int dy, bool human_like = true);
    void mouseLeftClick(bool human_like = true);
    void mouseRightClick(bool human_like = true);
    void mouseDoubleClick(bool human_like = true);
    void mouseDrag(int start_x, int start_y, int end_x, int end_y, bool human_like = true);
    void mouseScroll(int amount);
    void keyPress(WORD key_code);
    void keyDown(WORD key_code);
    void keyUp(WORD key_code);
    void typeText(const std::string& text, bool human_like = true);
    void hotkey(const std::vector<WORD>& keys);
    POINT getCurrentMousePos() const;
    void setDelaySettings(int min_delay, int max_delay);

private:
    int min_delay;
    int max_delay;
    std::mt19937 rng;
    std::uniform_int_distribution<> delay_distribution;
    double error_probability;

    int generateGaussianDelay(int mean, int stddev);
    void humanDelay(OperationType type);
    std::vector<POINT> generateHumanLikePath(int start_x, int start_y, int end_x, int end_y);
};

#endif // INPUT_SIMULATOR_H