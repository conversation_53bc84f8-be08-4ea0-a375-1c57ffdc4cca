#pragma once
#include <string>
#include <functional>
#include <vector>
#include <chrono>
#include <atomic>

// 加载步骤结构
struct LoadingStep {
    std::string name;
    std::string description;
    float progress;
    std::function<void()> action;
    bool completed;
    
    LoadingStep(const std::string& n, const std::string& desc, float prog, std::function<void()> act = nullptr)
        : name(n), description(desc), progress(prog), action(act), completed(false) {}
};

// 加载管理器类
class LoadingManager {
public:
    static LoadingManager& getInstance();
    
    // 初始化加载管理器
    void initialize();
    
    // 添加加载步骤
    void addStep(const std::string& name, const std::string& description, float progress, std::function<void()> action = nullptr);
    
    // 执行加载步骤
    void executeStep(int stepIndex);
    
    // 执行所有加载步骤
    void executeAllSteps();
    
    // 更新当前加载状态
    void updateProgress(const std::string& description, float progress);
    
    // 标记UI准备完成
    void markUIReady();
    
    // 检查UI是否准备完成
    bool isUIReady() const { return m_uiReady.load(); }
    
    // 获取当前状态
    bool isLoading() const { return m_isLoading; }
    const std::string& getCurrentText() const { return m_currentText; }
    float getCurrentProgress() const { return m_currentProgress; }
    int getCurrentStep() const { return m_currentStep; }
    const std::vector<LoadingStep>& getSteps() const { return m_steps; }
    int getStepCount() const { return static_cast<int>(m_steps.size()); }
    
    // 重置加载状态
    void reset();
    
    // 获取加载时间
    std::chrono::milliseconds getLoadingTime() const;
    
    // 渲染加载界面
    void renderLoadingScreen();
    
    // 设置最小显示时间（毫秒）- 仅用于视觉效果
    void setMinDisplayTime(int milliseconds) { m_minDisplayTime = milliseconds; }
    
    // 获取最小显示时间
    int getMinDisplayTime() const { return m_minDisplayTime; }
    
    // 设置UI准备完成回调函数（可选）
    void setUIReadyCallback(std::function<bool()> callback) { m_uiReadyCallback = callback; }
    
    // 检查UI是否真正准备好
    bool checkUIReady() const;

private:
    LoadingManager() = default;
    ~LoadingManager() = default;
    LoadingManager(const LoadingManager&) = delete;
    LoadingManager& operator=(const LoadingManager&) = delete;
    
    bool m_isLoading;
    std::string m_currentText;
    float m_currentProgress;
    int m_currentStep;
    std::vector<LoadingStep> m_steps;
    std::chrono::steady_clock::time_point m_startTime;
    int m_minDisplayTime; // 最小显示时间（毫秒）
    std::atomic<bool> m_uiReady; // UI准备状态
    std::function<bool()> m_uiReadyCallback; // UI准备完成回调函数
};

// 全局函数声明
void RenderLoadingScreen();
void UpdateLoadingProgress(const std::string& text, float progress); 