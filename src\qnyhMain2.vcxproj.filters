﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Core\TaskManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Core\VersionChecker.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Core\fwindows\find_window.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\LoadingManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\centerpanel\centerconsole.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\centerpanel\rolekills.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\centerpanel\tasksettings.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\consolelog\consolelog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\hardwareinfo\hardwareinfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\mainui.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\scripteditor\scripteditor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\systeminfo\systeminfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\GUI\tasklist\tasklist.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Logic\common.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Vnc\DispatchCenter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Vnc\MouseBoardCode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Vnc\ScreenFrameDistributor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Vnc\VMTasks.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Vnc\VMVision.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Vnc\VNCControl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\Vnc\VncMain.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\config\ConfigLoader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\config\TemplatePathManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\visualStudio\project\qnyhMain2\src\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Core\VersionChecker.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Core\fwindows\find_window.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Core\thread_queue\threadsafequeue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\LoadingManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\centerpanel\centerconsole.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\centerpanel\rolekills.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\centerpanel\tasksettings.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\consolelog\consolelog.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\hardwareinfo\hardwareinfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\mainui.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\scripteditor\scripteditor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\systeminfo\systeminfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\GUI\tasklist\tasklist.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Logic\common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Vnc\DispatchCenter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Vnc\MouseBoardCode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Vnc\ScreenFrameDistributor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Vnc\VMTasks.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Vnc\VMVision.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Vnc\VNCControl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Vnc\VncMain.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\Vnc\threadsafequeue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\config\ConfigLoader.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\config\LRUCache.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\visualStudio\project\qnyhMain2\src\config\TemplatePathManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\visualStudio\project\qnyhMain2\src\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{E00F4E21-6C27-3030-A51A-909332AB6C1F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{7D91B791-846B-3DC5-A192-2BE726A8D079}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
