// DispatchCenter.cpp

#include "DispatchCenter.h"
#include "VNCControl.h"
#include "VMVision.h"
#include "VMStateManager.h"
#include "VMTasks.h"
#include "VncMain.h" // 添加VncMain.h头文件
#include "MouseBoardCode.h" // 添加MouseBoardCode.h头文件，用于createKeyboardTask等函数
// #include "AppConfig.h" // 文件未找到，导致 C1083 错误
#include "GUI/consolelog/consolelog.h" // 修正日志路径
#include <config/TemplatePathManager.h> // 添加模板路径管理器
#include "Logic/common.h" // 添加common.h以使用新的脚本解析函数
// #include <iostream> // 用于 std::cerr - 已移除
#include <thread> // 用于 std::thread
#include <future> // 用于 std::future
#include <mutex> // 用于 std::mutex
#include <condition_variable> // 用于 std::condition_variable
#include <queue> // 用于 std::queue
#include <vector> // 用于 std::vector
#include <memory> // 用于 std::shared_ptr 和 std::unique_ptr
#include <chrono> // 用于 std::chrono
#include <algorithm> // 用于 std::remove_if
#include <sstream> // 新增
#include <regex>   // 新增

// 全局静态指针，用于确保类的单一实例
DispatchCenter* DispatchCenter::instance_ = nullptr;
std::mutex DispatchCenter::instanceMutex_;

DispatchCenter* DispatchCenter::getInstance() { // 返回类型改为 DispatchCenter*
    std::lock_guard<std::mutex> lock(instanceMutex_);
    if (instance_ == nullptr) {
        instance_ = new DispatchCenter();
    }
    return instance_; // 返回指针
}

DispatchCenter::DispatchCenter()
    : running_(false)
    , threadManager_(UnifiedThreadManager::getInstance()) {
    
    // 不在构造函数中初始化线程管理器，而是在需要时才初始化
    // 这避免了静态初始化顺序问题
    // 不在构造函数中调用日志函数，避免静态初始化顺序问题
}

// 确保线程管理器已初始化
bool DispatchCenter::ensureThreadManagerInitialized() {
    static std::once_flag initialized;
    static bool initializationSuccess = false;
    
    std::call_once(initialized, [this]() {
        try {
            if (threadManager_.initialize()) {
                initializationSuccess = true;
                // 延迟记录日志，避免静态初始化顺序问题
            } else {
                initializationSuccess = false;
            }
        } catch (const std::exception& e) {
            initializationSuccess = false;
        }
    });
    
    return initializationSuccess;
}

DispatchCenter::~DispatchCenter() {
    try {
        // 调用安全清理函数
        cleanup();
        
        // 关闭统一线程管理器
        threadManager_.shutdown();
        
    } catch (...) {
        // 析构函数中忽略所有异常
    }
}

// 现在初始化新的VM控制器
void DispatchCenter::addVMController_unlocked(const std::string& vmName, const std::string& ip, int port, const std::string& password) {
    //如果控制器已存在，直接返回，避免重复创建
    if (vmControllers_.count(vmName)) { // 检查是否已存在控制器
		AddLogInfo(LogLevel::Error, "[DispatchCenter] 虚拟机控制器已存在: " + vmName);
        return; // 不返回布尔值
    }

    VMControllerInfo controllerInfo; // 创建了一个 VMControllerInfo 类型的对象 controllerInfo
    controllerInfo.vmName = vmName;
    controllerInfo.ip = ip;
    controllerInfo.port = port;
    controllerInfo.password = password;
    
    //使用 std::make_unique 创建了一个 VNCControl 对象的智能指针，并将其赋值给 controllerInfo.vncControl
    controllerInfo.vncControl = std::make_unique<VNCControl>(ip, port, password);
    //successfullyConnected 这个状态（如果存在的话）应该由 VNCControl 内部的连接方法来设置
    // 初始化 Promise/Future 对
    controllerInfo.initialized_promise = {}; // 为新控制器重置 promise
    controllerInfo.initialized_future = controllerInfo.initialized_promise.get_future().share();

    // 将控制器信息移入vmControllers_映射
    auto [it, success] = vmControllers_.emplace(vmName, std::move(controllerInfo));
    if (!success) {
		AddLogInfo(LogLevel::Error, "[DispatchCenter] 无法添加虚拟机控制器: " + vmName);
        return; 
    }
    
    // 初始化VMStateManager中的VM状态
    auto* stateManager = VMStateManager::getInstance();
    if (stateManager) {
        stateManager->initializeVM(vmName);
        stateManager->setVMConnectionState(vmName, VMConnectionState::DISCONNECTED_STATE);
    }

    // 通过 it->second 获取映射中实际存储的 VMControllerInfo 对象的引用，并获取其地址
    VMControllerInfo* pControllerInMap = &it->second;

    // 确保线程管理器已初始化
    if (!ensureThreadManagerInitialized()) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] 统一线程管理器初始化失败，无法创建VM控制器: " + vmName);
        // 移除VM控制器，因为线程管理器初始化失败
        vmControllers_.erase(vmName);
        return;
    }
    
    // 使用统一线程管理器创建核心控制器线程
    std::string controllerThreadName = "VMController_" + vmName;
    std::string pollerThreadName = "TaskPoller_" + vmName;
    
    // 创建VM控制器线程
    std::string controllerResult = threadManager_.createNamedThread(controllerThreadName, 
        [this, pControllerInMap]() {
            vmControllerThreadWithStopToken(pControllerInMap);
        });
    
    if (controllerResult.empty()) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] VM控制器线程创建失败: " + controllerThreadName);
        vmControllers_.erase(vmName);
        return;
    }
    
    // 创建任务轮询器线程
    std::string pollerResult = threadManager_.createNamedThread(pollerThreadName,
        [this, pControllerInMap]() {
            taskPollerThreadWithStopToken(pControllerInMap);
        });
    
    // 检查线程创建是否成功
    if (pollerResult.empty()) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] 任务轮询器线程创建失败: " + pollerThreadName);
        vmControllers_.erase(vmName);
        return;
    }
    
}

// 移除一个虚拟机控制器
bool DispatchCenter::removeVMController(const std::string& vmName) {
    std::lock_guard<std::mutex> lock(controllersMutex_);
    auto it = vmControllers_.find(vmName);
    if (it != vmControllers_.end()) {
        // 通知控制器线程停止
        it->second.stopRequested.store(true);
        it->second.pollerStopRequested.store(true);
        
        // 通知其条件变量
        it->second.taskQueueCV_.notify_all();
        it->second.pollerCV_.notify_all();
        it->second.executionCV_.notify_all();

        // 使用统一线程管理器停止线程
        std::string controllerThreadName = "VMController_" + vmName;
        std::string pollerThreadName = "TaskPoller_" + vmName;
        
        try {
            threadManager_.stopNamedThread(controllerThreadName);
            threadManager_.stopNamedThread(pollerThreadName);
            // 删除：已通过统一线程管理器停止VM线程日志
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Warning, "[DispatchCenter] 停止VM线程失败: " + vmName + ", 错误: " + e.what());
            // 统一线程管理器处理失败，继续清理其他资源
        }

        vmControllers_.erase(it);
        controllersCV_.notify_all(); // 通知等待者虚拟机已被移除
        return true;
    }
    AddLogInfo(LogLevel::Warning, "[DispatchCenter] 尝试移除不存在的虚拟机控制器: " + vmName);
    return false;
}

void DispatchCenter::processDirectKeyboardMouseTask(const std::string& vmName, VMControllerInfo* controllerInfo, DirectKeyboardMouseActionTask* task) {
    if (!controllerInfo || !controllerInfo->vncControl || !task) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 处理直接键盘鼠标任务时参数无效。");
        if (task) {
            auto promise = task->getPromise();
            if (promise) {
                KeyboardMouseResult res;
                res.success = false;
                res.errorMessage = "控制器或VNCControl不可用于任务执行";
                try { promise->set_value(res); } catch (const std::future_error& e) { AddLogInfo(LogLevel::Warning, "[Promise] set_value 多次调用: " + std::string(e.what())); }
            }
        }
        return;
    }
    
    auto client = controllerInfo->vncControl->getRfbClient(); 
    if (!client) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 直接键盘鼠标任务执行时rfbClient不可用。");
        auto promise = task->getPromise();
        if (promise) {
            KeyboardMouseResult res;
            res.success = false;
            res.errorMessage = "任务执行时rfbClient不可用";
            try { promise->set_value(res); } catch (const std::future_error& e) { AddLogInfo(LogLevel::Warning, "[Promise] set_value 多次调用: " + std::string(e.what())); }
        }
        return;
    }
    
    // 🔧 坐标转换：将切片输入任务转换为全局输入任务
    auto* visionProcessor = getVisionProcessor(vmName);
    if (visionProcessor && visionProcessor->getScreenFrameDistributor()) {
        auto* frameDistributor = visionProcessor->getScreenFrameDistributor();
        std::vector<InputTask> globalActions = frameDistributor->convertSliceInputTasksToGlobal(task->getSliceNumber(), task->getActions());
        
        // 用转换后的全局坐标更新任务
        task->setActions(globalActions);
    }
    
    // 直接调用execute方法执行任务
    task->execute(client.get(), nullptr);
}



VMControllerInfo* DispatchCenter::getVMControllerInfo(const std::string& vmName) {
    try {
        std::lock_guard<std::mutex> lock(controllersMutex_);
        
        // 首先检查vmName是否为空
        if (vmName.empty()) {
            AddLogInfo(LogLevel::Warning, "[DispatchCenter] getVMControllerInfo: vmName为空");
            return nullptr;
        }
        
        auto it = vmControllers_.find(vmName);
        if (it != vmControllers_.end()) {
            // 额外的安全检查，确保返回的控制器信息是有效的
            VMControllerInfo* controllerInfo = &(it->second);
            if (controllerInfo && !controllerInfo->vmName.empty()) {
                // 进一步检查关键成员是否有效
                try {
                    // 测试访问一些关键成员，确保内存有效
                    volatile bool test1 = controllerInfo->successfullyConnected.load();
                    volatile int test2 = controllerInfo->currentSliceForScript.load();
                    volatile size_t test3 = controllerInfo->sliceQueues.size();
                    
                    // 如果能访问到这里，说明内存是有效的
                    (void)test1; (void)test2; (void)test3; // 避免未使用变量警告
                    
                    return controllerInfo;
                } catch (const std::exception& e) {
                    AddLogInfo(LogLevel::Error, "[DispatchCenter] 访问虚拟机 [" + vmName + "] 控制器信息内存时发生异常: " + std::string(e.what()));
                    return nullptr;
                } catch (...) {
                    AddLogInfo(LogLevel::Error, "[DispatchCenter] 访问虚拟机 [" + vmName + "] 控制器信息内存时发生未知异常");
                    return nullptr;
                }
            } else {
                AddLogInfo(LogLevel::Warning, "[DispatchCenter] 找到虚拟机 [" + vmName + "] 但控制器信息无效");
            }
        } else {
            AddLogInfo(LogLevel::Debug, "[DispatchCenter] 未找到虚拟机 [" + vmName + "] 的控制器信息");
        }
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] 获取虚拟机 [" + vmName + "] 控制器信息时发生异常: " + std::string(e.what()));
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] 获取虚拟机 [" + vmName + "] 控制器信息时发生未知异常");
    }
    return nullptr;
}

VMThreadState DispatchCenter::getVMState(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(controllersMutex_)); // const method, but mutex protects shared state
    auto it = vmControllers_.find(vmName);
    if (it != vmControllers_.end()) {
        // Assuming VMControllerInfo has a way to get state, e.g., an atomic member or a getter
        return it->second.currentState.load(); 
    }
    return VMThreadState::STOPPED; // Default to STOPPED as error state
}

bool DispatchCenter::isVMInitialized(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(controllersMutex_));
    return isVMInitialized_unlocked(vmName);
}

bool DispatchCenter::isVMInitialized_unlocked(const std::string& vmName) const {
    auto it = vmControllers_.find(vmName);
    if (it != vmControllers_.end()) {
        // 检查 future 是否已经就绪
        if (it->second.initialized_future.valid() && 
            it->second.initialized_future.wait_for(std::chrono::seconds(0)) == std::future_status::ready) {
            return it->second.initialized_future.get(); // 获取 promise 的结果
        }
        return false; // Future 尚未就绪或无效
    }
    return false; // VM不存在
}

// 检查虚拟机是否已连接（成功建立VNC连接）
bool DispatchCenter::isVMConnected(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(controllersMutex_));
    auto it = vmControllers_.find(vmName);
    if (it != vmControllers_.end()) {
        return it->second.successfullyConnected.load(); // 假设这个标志表示活动连接
    }
    return false;
}

// 检查虚拟机是否已暂停
bool DispatchCenter::isVMPaused(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(controllersMutex_));
    auto it = vmControllers_.find(vmName);
    if (it != vmControllers_.end()) {
        return it->second.currentState.load() == VMThreadState::PAUSED;
    }

    return false; // 或者抛出异常，取决于所需的错误处理方式
}

// 暂停虚拟机的任务处理
void DispatchCenter::pauseVM(const std::string& vmName) {
    std::lock_guard<std::mutex> lock(controllersMutex_); // 保护vmControllers_
    VMControllerInfo* controllerInfo = findVMController_unlocked(vmName);
    if (controllerInfo) {
        if (controllerInfo->currentState.load() != VMThreadState::PAUSED) {
            AddLogInfo(LogLevel::Info, "[DispatchCenter] 暂停虚拟机: " + vmName);
            controllerInfo->currentState.store(VMThreadState::PAUSED);
            // 通知任务处理线程，以防它在等待空队列
            // 这样它可以重新评估其条件并进入暂停等待状态
            // controllerInfo->taskProcessingCV_.notify_one(); // taskProcessingCV_已移除
            // vmControllerThread的主循环的CV等待谓词应该检查PAUSED状态
            // 目前，这个通知移除是第一步。完整的暂停实现需要更改vmControllerThread
            controllerInfo->taskQueueCV_.notify_one(); // 通知主任务循环重新评估其等待条件，如果它包含PAUSED状态
        }
        else {
            AddLogInfo(LogLevel::Info, "[DispatchCenter] 虚拟机 " + vmName + " 已经处于暂停状态。");
        }
    }
    else {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] 尝试暂停不存在的虚拟机: " + vmName);
    }
}

// 恢复虚拟机的任务处理
void DispatchCenter::resumeVM(const std::string& vmName) {
    std::lock_guard<std::mutex> lock(controllersMutex_); // 保护vmControllers_
    VMControllerInfo* controllerInfo = findVMController_unlocked(vmName);
    if (controllerInfo) {
        if (controllerInfo->currentState.load() == VMThreadState::PAUSED) {
            AddLogInfo(LogLevel::Info, "[DispatchCenter] 恢复虚拟机: " + vmName);
            // 确定要返回的正确状态（例如，如果已连接则返回RUNNING，或者如果在初始化期间暂停则返回INITIALIZING）
            // 为简单起见，假设它应该返回到RUNNING状态
            // 如果PAUSED可能发生在其他状态，可能需要更复杂的逻辑
            if (controllerInfo->successfullyConnected.load()) {
                 controllerInfo->currentState.store(VMThreadState::RUNNING);
            } else {
                // 如果未成功连接，可能是在初始化或连接尝试期间暂停的
                // 恢复到INITIALIZING或重试连接的状态
                // 目前，假设RUNNING是可以的，或者它将重新评估其连接状态
                controllerInfo->currentState.store(VMThreadState::INITIALIZING); // 或者RUNNING并让循环处理它
            }
            controllerInfo->taskQueueCV_.notify_one();
        } else {
            AddLogInfo(LogLevel::Info, "[DispatchCenter] 虚拟机 " + vmName + " 未暂停，恢复操作未执行。");
        }
    } else {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] 尝试恢复不存在的虚拟机: " + vmName);
    }
}

// 暂停指定切片 - 使用新的状态管理器
void DispatchCenter::pauseSlice(const std::string& vmName, int sliceNumber) {
    try {
        // 检查参数有效性
        if (vmName.empty()) {
            AddLogInfo(LogLevel::Warning, "[DispatchCenter] pauseSlice: vmName为空");
            return;
        }
        
        // 使用状态管理器更新切片状态
        auto* stateManager = VMStateManager::getInstance();
        if (!stateManager) {
            AddLogInfo(LogLevel::Error, "[DispatchCenter] pauseSlice: VMStateManager实例获取失败");
            return;
        }

        // ✅ 检查切片是否已经暂停，避免重复暂停
        if (stateManager->isSlicePaused(vmName, sliceNumber)) {
            AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 切片 " + std::to_string(sliceNumber) + " 已经处于暂停状态，跳过重复暂停");
            return;
        }

        stateManager->setSliceState(vmName, sliceNumber, SliceState::PAUSED_STATE);
        
        AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 切片 " + std::to_string(sliceNumber) + " 已暂停");
        
        // 更新队列大小信息
        std::lock_guard<std::mutex> lock(controllersMutex_);
        VMControllerInfo* controllerInfo = findVMController_unlocked(vmName);
        if (controllerInfo) {
            try {
                auto sliceIt = controllerInfo->sliceQueues.find(sliceNumber);
                if (sliceIt != controllerInfo->sliceQueues.end() && sliceIt->second) {
                    // 状态由VMStateManager统一管理，不再需要设置SliceInfo的标志
                    
                    int queueSize = sliceIt->second->taskQueue ? sliceIt->second->taskQueue->size() : 0;
                    stateManager->setSliceTaskQueueSize(vmName, sliceNumber, queueSize);
                    
                    AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 切片 " + std::to_string(sliceNumber) + 
                              " 已暂停（匹配失败），队列中还有 " + std::to_string(queueSize) + " 个待执行任务");
                }
                
                // 检查是否应该暂停整个虚拟机的脚本执行
                checkAndPauseVMIfNeeded(controllerInfo, vmName);
            } catch (const std::exception& e) {
                AddLogInfo(LogLevel::Error, "[DispatchCenter] pauseSlice: 处理切片队列信息时发生异常: " + std::string(e.what()));
            } catch (...) {
                AddLogInfo(LogLevel::Error, "[DispatchCenter] pauseSlice: 处理切片队列信息时发生未知异常");
            }
        } else {
            AddLogInfo(LogLevel::Warning, "[DispatchCenter] pauseSlice: 找不到虚拟机 " + vmName + " 的控制器信息");
        }
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] pauseSlice: 发生异常: " + std::string(e.what()));
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] pauseSlice: 发生未知异常");
    }
}

// 恢复指定切片 - 使用新的状态管理器
void DispatchCenter::resumeSlice(const std::string& vmName, int sliceNumber) {
    // 使用状态管理器更新切片状态
    auto* stateManager = VMStateManager::getInstance();
    
    // 检查切片当前是否暂停
    if (!stateManager->isSlicePaused(vmName, sliceNumber)) {
        AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 切片 " + std::to_string(sliceNumber) + " 未处于暂停状态");
        return;
    }
    
    // 恢复切片状态
    stateManager->setSliceState(vmName, sliceNumber, SliceState::IDLE_STATE);
    AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 切片 " + std::to_string(sliceNumber) + " 已恢复");
    
    std::lock_guard<std::mutex> lock(controllersMutex_);
    VMControllerInfo* controllerInfo = findVMController_unlocked(vmName);
    if (controllerInfo) {
        // 状态由VMStateManager统一管理，不再需要清除SliceInfo的标志
        // 检查是否应该恢复虚拟机脚本执行
        checkAndResumeVMIfNeeded(controllerInfo, vmName);
        
        // 如果当前脚本在这个切片上被暂停，继续执行脚本
        if (stateManager->getCurrentSliceForScript(vmName) == sliceNumber) {
            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 切片 " + std::to_string(sliceNumber) + " 恢复，继续执行脚本");
            // 轮询器会自动继续执行后续任务，无需手动触发
            AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 切片恢复后，轮询器将自动继续执行任务");
        }
        
        controllerInfo->taskQueueCV_.notify_one(); // 通知任务处理线程
    }
}

// 检查并暂停虚拟机脚本执行（当所有切片暂停或有切片暂停且其他切片无任务时）
void DispatchCenter::checkAndPauseVMIfNeeded(VMControllerInfo* controllerInfo, const std::string& vmName) {
    if (!controllerInfo) return;
    
    bool allSlicesPaused = true;
    bool hasActiveTasks = false;
    int totalSlices = 0;
    int pausedSlices = 0;
    
    auto* stateManager = VMStateManager::getInstance();
    
    // 检查所有切片的状态
    for (const auto& slice : controllerInfo->sliceQueues) {
        if (slice.second) {
            totalSlices++;
            int sliceNumber = slice.first;
            
            bool isSlicePaused = stateManager ? stateManager->isSlicePaused(vmName, sliceNumber) : false;
            if (isSlicePaused) {
                pausedSlices++;
            } else {
                allSlicesPaused = false;
                
                // 检查非暂停切片是否有待执行任务
                if (slice.second->taskQueue && slice.second->taskQueue->size() > 0) {
                    hasActiveTasks = true;
                }
            }
        }
    }
    
    // 条件1：所有切片都暂停了
    if (allSlicesPaused && totalSlices > 0) {
        if (!controllerInfo->pollerPaused.load()) {
            controllerInfo->pollerPaused.store(true);
            AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 所有切片已暂停，自动暂停虚拟机脚本执行");
        }
        return;
    }
    
    // 条件2：有切片暂停且其他切片没有任务执行
    // ✅ 修复：只有在所有切片都解析完成后才考虑暂停VM
    // 检查是否还有切片正在解析（通过检查脚本解析状态）
    bool isScriptRunning = stateManager ? stateManager->isScriptRunning(vmName) : false;

    if (pausedSlices > 0 && !hasActiveTasks && !isScriptRunning) {
        if (!controllerInfo->pollerPaused.load()) {
            controllerInfo->pollerPaused.store(true);
            AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 有切片暂停且其他切片无任务，自动暂停虚拟机脚本执行 (暂停切片: " +
                      std::to_string(pausedSlices) + "/" + std::to_string(totalSlices) + ")");
        }
        return;
    }
    
    // 如果条件不满足，确保轮询器正在运行
    if (controllerInfo->pollerPaused.load() && hasActiveTasks) {
        controllerInfo->pollerPaused.store(false);
        controllerInfo->pollerCV_.notify_one();
        AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 检测到有可执行任务，自动恢复虚拟机脚本执行");
    }
}

// 检查并恢复虚拟机脚本执行（当有切片恢复且有可执行任务时）
void DispatchCenter::checkAndResumeVMIfNeeded(VMControllerInfo* controllerInfo, const std::string& vmName) {
    if (!controllerInfo) return;
    
    bool hasActiveSlices = false;
    bool hasActiveTasks = false;
    int totalSlices = 0;
    int activeSlices = 0;
    
    auto* stateManager = VMStateManager::getInstance();
    
    // 检查所有切片的状态
    for (const auto& slice : controllerInfo->sliceQueues) {
        if (slice.second) {
            totalSlices++;
            int sliceNumber = slice.first;
            
            bool isSlicePaused = stateManager ? stateManager->isSlicePaused(vmName, sliceNumber) : false;
            if (!isSlicePaused) {
                activeSlices++;
                hasActiveSlices = true;
                
                // 检查活跃切片是否有待执行任务
                if (slice.second->taskQueue && slice.second->taskQueue->size() > 0) {
                    hasActiveTasks = true;
                }
            }
        }
    }
    
    // 如果有活跃切片且有任务，确保轮询器正在运行
    if (hasActiveSlices && hasActiveTasks) {
        if (controllerInfo->pollerPaused.load()) {
            controllerInfo->pollerPaused.store(false);
            controllerInfo->pollerCV_.notify_one();
            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 检测到活跃切片有任务，自动恢复虚拟机脚本执行 (活跃切片: " + 
                      std::to_string(activeSlices) + "/" + std::to_string(totalSlices) + ")");
        }
    }
    // 如果没有活跃任务，保持暂停状态
    else if (!hasActiveTasks && controllerInfo->pollerPaused.load()) {
        AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 虚拟机脚本保持暂停状态 (活跃切片: " + 
                  std::to_string(activeSlices) + "/" + std::to_string(totalSlices) + ", 有任务: " + 
                  (hasActiveTasks ? "是" : "否") + ")");
    }
}

// 恢复所有暂停的切片
void DispatchCenter::resumeAllPausedSlices(const std::string& vmName) {
    std::lock_guard<std::mutex> lock(controllersMutex_);
    VMControllerInfo* controllerInfo = findVMController_unlocked(vmName);
    if (controllerInfo) {
        int resumedCount = 0;
        auto* stateManager = VMStateManager::getInstance();
        
        for (auto& slicePair : controllerInfo->sliceQueues) {
            int sliceNumber = slicePair.first;
            if (stateManager && stateManager->isSlicePaused(vmName, sliceNumber)) {
                stateManager->setSliceState(vmName, sliceNumber, SliceState::IDLE_STATE);
                resumedCount++;
                AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 切片 " + std::to_string(sliceNumber) + " 已恢复");
            }
        }
        
        if (resumedCount > 0) {
            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 总共恢复了 " + std::to_string(resumedCount) + " 个暂停的切片");
            controllerInfo->taskQueueCV_.notify_one(); // 通知任务处理线程
            // 通知轮询器恢复工作
            controllerInfo->pollerCV_.notify_one();
        } else {
            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 没有暂停的切片需要恢复");
        }
    } else {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] 尝试恢复不存在的虚拟机的切片: " + vmName);
    }
}

// 暂停任务轮询器
void DispatchCenter::pauseTaskPoller(const std::string& vmName) {
    std::lock_guard<std::mutex> lock(controllersMutex_);
    VMControllerInfo* controllerInfo = findVMController_unlocked(vmName);
    if (controllerInfo) {
        controllerInfo->pollerPaused.store(true);
        AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 任务轮询器已暂停");
    } else {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] 尝试暂停不存在的虚拟机的轮询器: " + vmName);
    }
}

// 恢复任务轮询器
void DispatchCenter::resumeTaskPoller(const std::string& vmName) {
    std::lock_guard<std::mutex> lock(controllersMutex_);
    VMControllerInfo* controllerInfo = findVMController_unlocked(vmName);
    if (controllerInfo) {
        controllerInfo->pollerPaused.store(false);
        controllerInfo->pollerCV_.notify_one();
        AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 任务轮询器已恢复");
    } else {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] 尝试恢复不存在的虚拟机的轮询器: " + vmName);
    }
}







// 清空待处理任务
void DispatchCenter::clearPendingTasks(const std::string& vmName) {
    std::lock_guard<std::mutex> lock(controllersMutex_);
    VMControllerInfo* controllerInfo = findVMController_unlocked(vmName);
    if (controllerInfo) {
        // 清空主任务队列
        if (controllerInfo->taskQueue) {
            int mainQueueTasks = controllerInfo->taskQueue->size();
            controllerInfo->taskQueue->clear();
            if (mainQueueTasks > 0) {
                AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 已清空主任务队列中的 " + std::to_string(mainQueueTasks) + " 个任务");
            }
        }
        
        // 清空所有切片队列中的任务
        int totalSliceTasks = 0;
        for (auto& slicePair : controllerInfo->sliceQueues) {
            if (slicePair.second && slicePair.second->taskQueue) {
                int sliceTasks = slicePair.second->taskQueue->size();
                slicePair.second->taskQueue->clear();
                totalSliceTasks += sliceTasks;
            }
        }
        
        if (totalSliceTasks > 0) {
            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 已清空所有切片队列中的 " + std::to_string(totalSliceTasks) + " 个任务");
        }
        
        AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 任务清空完成");
    } else {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] clearPendingTasks: 找不到虚拟机 " + vmName);
    }
}

// 清除所有任务并重置切片状态
void DispatchCenter::clearAllTasksAndResetSlices(const std::string& vmName) {
    std::lock_guard<std::mutex> lock(controllersMutex_);
    VMControllerInfo* controllerInfo = findVMController_unlocked(vmName);
    if (controllerInfo) {
        // 1. 清空所有任务
        int totalTasks = 0;
        
        // 清空主任务队列
        if (controllerInfo->taskQueue) {
            totalTasks += controllerInfo->taskQueue->size();
            controllerInfo->taskQueue->clear();
        }
        
        // 清空中间队列（新架构中的生产者-消费者队列）
        if (controllerInfo->intermediateQueue) {
            int intermediateTasks = controllerInfo->intermediateQueue->size();
            controllerInfo->intermediateQueue->clear();
            totalTasks += intermediateTasks;
            if (intermediateTasks > 0) {
                AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 清空中间队列中的 " + std::to_string(intermediateTasks) + " 个任务");
            }
        }
        
        // 清空所有切片队列中的任务并重置切片状态
        int totalSlices = 0;
        int pausedSlices = 0;
        auto* stateManager = VMStateManager::getInstance();
        
        for (auto& slicePair : controllerInfo->sliceQueues) {
            if (slicePair.second && slicePair.second->taskQueue) {
                totalTasks += slicePair.second->taskQueue->size();
                slicePair.second->taskQueue->clear();
                
                // 重置切片状态（检查是否暂停用于统计）
                if (stateManager && stateManager->isSlicePaused(vmName, slicePair.first)) {
                    pausedSlices++;
                }
                
                // 同步重置VMStateManager中的切片状态
                if (stateManager) {
                    stateManager->setSliceState(vmName, slicePair.first, SliceState::IDLE_STATE);
                }
                
                totalSlices++;
            }
        }
        
        // 重置切片等待状态
        // 注释掉已移除的成员变量引用
        // for (auto& waitPair : controllerInfo->sliceWaitingStatus) {
        //     waitPair.second = false;
        // }
        
        // 重置当前切片索引
        controllerInfo->currentSliceIndex = 0;
        
        // 恢复轮询器（如果之前被暂停）
        if (controllerInfo->pollerPaused.load()) {
            controllerInfo->pollerPaused.store(false);
            controllerInfo->pollerCV_.notify_one();
            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 轮询器已恢复");
        }
        
        // 删除：任务清除和状态重置完成日志
        // 删除：清除了X个任务日志
        // 删除：重置了X个切片状态日志
        if (pausedSlices > 0) {
            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 恢复了 " + std::to_string(pausedSlices) + " 个暂停的切片");
        }
        
        // 通知任务处理线程
        controllerInfo->taskQueueCV_.notify_one();
        
    } else {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] clearAllTasksAndResetSlices: 找不到虚拟机 " + vmName);
    }
}

void DispatchCenter::disconnectAllVMs() {
    stopAllVMControllers(); 
    AddLogInfo(LogLevel::Info, "[DispatchCenter] 所有虚拟机断开连接。");
}

// Check if there are any pending tasks in any VM controller
bool DispatchCenter::hasTasks() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(controllersMutex_));
    for (const auto& pair : vmControllers_) {
        if (pair.second.taskQueue && !pair.second.taskQueue->empty()) {
            return true;
        }
    }
    return false;
}

// Get all VM names
std::vector<std::string> DispatchCenter::getAllVMNames() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(controllersMutex_));
    std::vector<std::string> vmNames;
    for (const auto& pair : vmControllers_) {
        vmNames.push_back(pair.first);
    }
    return vmNames;
}

// 获取已连接的虚拟机名称列表
std::vector<std::string> DispatchCenter::getConnectedVMNames() const {
    std::vector<std::string> connectedVMs;
    try {
        std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(controllersMutex_));
        for (const auto& pair : vmControllers_) {
            try {
                // 检查虚拟机是否已连接，并且加强安全检查
                if (pair.second.successfullyConnected.load() && 
                    pair.second.vncControl && 
                    pair.second.vncControl->isConnected()) {
                    connectedVMs.push_back(pair.first);
                }
            } catch (const std::exception& e) {
                AddLogInfo(LogLevel::Error, "[DispatchCenter] 检查虚拟机 [" + pair.first + "] 连接状态时发生异常: " + std::string(e.what()));
            } catch (...) {
                AddLogInfo(LogLevel::Error, "[DispatchCenter] 检查虚拟机 [" + pair.first + "] 连接状态时发生未知异常");
            }
        }
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] 获取已连接虚拟机列表时发生异常: " + std::string(e.what()));
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] 获取已连接虚拟机列表时发生未知异常");
    }
    return connectedVMs;
}

// Get count of active VM controllers
size_t DispatchCenter::getActiveVMCount() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(controllersMutex_));
    size_t count = 0;
    for (const auto& pair : vmControllers_) {
        if (pair.second.currentState.load() == VMThreadState::RUNNING) {
            count++;
        }
    }
    return count;
}

// Reconnect a VM controller
bool DispatchCenter::reconnectVMController(const std::string& vmName) {
    VMControllerInfo* controllerInfo = findVMController(vmName);
    if (!controllerInfo || !controllerInfo->vncControl) {
         return false;
    }
    
    // Disconnect first if already connected
    if (controllerInfo->successfullyConnected.load()) {
        controllerInfo->vncControl->disconnect();
    }
    
    // Try to reconnect
    controllerInfo->currentState.store(VMThreadState::INITIALIZING);
    if (!controllerInfo->vncControl->connectToServer()) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": VNC reconnection failed.");
        controllerInfo->currentState.store(VMThreadState::ERROR_CONNECTION_FAILED);
        controllerInfo->successfullyConnected.store(false);
        try { controllerInfo->initialized_promise.set_value(false); } catch (const std::future_error& e) { AddLogInfo(LogLevel::Warning, "[Promise] initialized_promise set_value 多次调用: " + std::string(e.what())); } // Signal initialization failure
        controllersCV_.notify_all(); // Notify waiters about connection failure

        // 确保vncClient指针为nullptr，防止后续代码尝试使用它
        controllerInfo->vncClient.reset();
        return false;
    }
    
    controllerInfo->successfullyConnected.store(true);
    controllerInfo->currentState.store(VMThreadState::RUNNING);
    
    // 更新VMStateManager中的连接状态
    auto* stateManager = VMStateManager::getInstance();
    if (stateManager) {
        stateManager->setVMConnectionState(vmName, VMConnectionState::CONNECTED_STATE);
    }
    
    AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": VNC reconnected successfully.");
    try { controllerInfo->initialized_promise.set_value(true); } catch (const std::future_error& e) { AddLogInfo(LogLevel::Warning, "[Promise] initialized_promise set_value 多次调用: " + std::string(e.what())); } // Signal initialization success
    return true;
}

// 初始化连接
bool DispatchCenter::initialize(const std::vector<std::pair<std::string, int>>& vm_configs) {
    std::lock_guard<std::mutex> lock(controllersMutex_);
    bool allInitialized = true;
    
    for (const auto& config : vm_configs) {
        const std::string vmName = config.first;
        int port = config.second;
        std::string ip = "127.0.0.1"; // 默认ip地址
        std::string password = ""; // 默认空密码
        
        // 创建一个VM控制器
        if (vmControllers_.find(vmName) == vmControllers_.end()) {
            // 调用函数创建
            addVMController_unlocked(vmName, ip, port, password);
            // 不再立即检查初始化状态，因为VNC连接是异步的
            // 主程序会在稍后检查VM状态
        } else {
            AddLogInfo(LogLevel::Warning, "[DispatchCenter] VM控制器已存在，跳过创建: " + vmName);
        }
    }
    
    //running_.store(true);
    return allInitialized;
}

// 查找视觉处理器
VMVision* DispatchCenter::getVisionProcessor(const std::string& vmName, std::chrono::milliseconds timeout) {
    std::lock_guard<std::mutex> lock(controllersMutex_); // getVisionProcessor manages its own lock
    VMControllerInfo* controllerInfo = findVMController_unlocked(vmName, timeout); // Use unlocked version as lock is already held

    // 加强调试日志，输出所有关键状态
    if (!controllerInfo) {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": Controller not found or not ready for vision processing. (controllerInfo=nullptr)");
        return nullptr;
    }

    if (!controllerInfo->successfullyConnected.load()) {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": VNC not connected, cannot get vision processor. (successfullyConnected=false)");
        return nullptr;
    }

    // Ensure VMVision instance exists
    if (!controllerInfo->visionProcessor) {
        // Assuming vncClient is valid and set up within controllerInfo by this point
            auto clientPtr = controllerInfo->vncClient.lock();
    if (!clientPtr) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": vncClient is null, cannot create VMVision. (vncClient=nullptr)");
        return nullptr;
    }
    controllerInfo->visionProcessor = std::make_unique<VMVision>(vmName, clientPtr);

        // 设置线程池（使用统一管理器的线程池）
        if (controllerInfo->visionProcessor) {
            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 开始为视觉处理器设置线程池");
            
            // VMVision现在自己管理线程池，不需要外部设置
            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": VMVision使用自己的线程池，无需外部设置");
        }
    }
    return controllerInfo->visionProcessor.get();
}

//在之前的视觉循环中调用当前函数取处理匹配任务
void DispatchCenter::processImageMatchTask(const std::string& vmName, VMControllerInfo* controllerInfo, ImageMatchVisionTask* task) {
    //检查任务、控制器信息、视觉处理器和VNC控制是否都已初始化
    if (!task || !controllerInfo || !controllerInfo->visionProcessor || !controllerInfo->vncControl) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": Invalid arguments for processImageMatchTask.");
        if(task && task->getPromise()) {
            VisionMatchResult res; 
            res.success = false; 
            res.errorMessage = "Invalid arguments or uninitialized controller components for ImageMatchVisionTask.";
            try { task->getPromise()->set_value(res); } catch (const std::future_error& e) { AddLogInfo(LogLevel::Warning, "[Promise] task promise set_value 多次调用: " + std::string(e.what())); }
        }
        return;
    }

    // ✅ 添加切片状态检查，防止在暂停状态下执行任务
    int sliceNumber = task->getSliceNumber();
    auto* stateManager = VMStateManager::getInstance();
    if (stateManager && stateManager->isSlicePaused(vmName, sliceNumber)) {
        AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 切片 " + std::to_string(sliceNumber) + " 已暂停，跳过VISUAL_MATCH任务");
        if(task && task->getPromise()) {
            VisionMatchResult res;
            res.success = false;
            res.errorMessage = "切片已暂停，任务跳过";
            try { task->getPromise()->set_value(res); } catch (const std::future_error& e) { AddLogInfo(LogLevel::Warning, "[Promise] task promise set_value 多次调用: " + std::string(e.what())); }
        }
        return;
    }

    // 删除冗余的接收图片匹配任务日志

    // 使用TemplatePathManager获取正确的模板路径
    std::string actualTemplatePath = TemplatePathManager::getInstance().getFilePath(task->getTemplateName());
    
    // 如果TemplatePathManager中没有配置，尝试使用传入的路径
    if (actualTemplatePath.empty() && !task->getImagePath().empty()) {
        actualTemplatePath = task->getImagePath();
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 模板 '" + task->getTemplateName() + "' 未在配置中找到，使用传入路径: " + actualTemplatePath);
    }
    
    // 如果提供了模板路径，加载模板
    if (!actualTemplatePath.empty()) {
        AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 使用模板路径: " + actualTemplatePath);
    } else {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 没有提供图像路径，使用模板名称作为直接路径尝试: " + task->getTemplateName());
        actualTemplatePath = task->getTemplateName(); // 回退到直接使用模板名称
    }

    auto promiseForVision = std::make_shared<std::promise<std::pair<bool, cv::Point>>>();
    std::future<std::pair<bool, cv::Point>> futureFromVision = promiseForVision->get_future();

    // 🔧 修复坐标转换逻辑：传入的ROI是基于切片的相对坐标，无需转换
    cv::Rect sliceROI = task->getRoi(); // 基于切片的ROI坐标
    AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 使用切片内ROI坐标: (" + 
               std::to_string(sliceROI.x) + "," + std::to_string(sliceROI.y) + "," + 
               std::to_string(sliceROI.width) + "," + std::to_string(sliceROI.height) + ")");
    
    // 调用视觉处理器的异步匹配函数（使用切片内ROI）
    controllerInfo->visionProcessor->matchTemplateAsync(
        actualTemplatePath,
        task->getThreshold(),
        sliceROI,  // 直接使用切片内的ROI坐标
        task->getSliceNumber(),
        promiseForVision
    );

    VisionMatchResult finalResult;
    try {
        // Add a timeout to future.get()
        if (futureFromVision.wait_for(std::chrono::seconds(10)) == std::future_status::ready) {
            std::pair<bool, cv::Point> visionResultPair = futureFromVision.get();
            finalResult.success = visionResultPair.first;
            finalResult.matchLocation = visionResultPair.second;
            finalResult.confidence = visionResultPair.first ? task->getThreshold() : 0.0;  //获取置信度
            //处理匹配成功或失败的情况
            if (visionResultPair.first) {
                finalResult.errorMessage = "Match found.";
                
                // 图片匹配成功后，根据鼠标点击类型执行相应操作
                try {
                    if (controllerInfo->vncControl) {
                        // 🔧 修复坐标转换逻辑：VMVision返回的是基于切片的坐标
                        cv::Point sliceMatchLocation = visionResultPair.second;
                        
                        // 将切片坐标转换为全局坐标用于鼠标操作
                        cv::Point globalMatchLocation = sliceMatchLocation; // 默认值
                        if (controllerInfo->visionProcessor && controllerInfo->visionProcessor->getScreenFrameDistributor()) {
                            globalMatchLocation = controllerInfo->visionProcessor->getScreenFrameDistributor()->sliceToGlobalCoordinate(sliceMatchLocation, task->getSliceNumber());
                        }
                        
                        // 移动鼠标到匹配位置（使用全局坐标）
                        controllerInfo->vncControl->sendInput(mouseMoveCoord(globalMatchLocation.x, globalMatchLocation.y));
                        
                        // 根据鼠标点击类型执行相应操作
                        std::string clickType = task->getMouseClickType();
                        std::string actionName = "移动";
                        if (clickType == "right") {
                            controllerInfo->vncControl->sendInput(mouseRightClick());
                            actionName = "右键点击";
                        } else if (clickType == "double") {
                            // 双击需要发送两个左键点击任务
                            auto doubleClickTasks = mouseDoubleClick();
                            for (const auto& clickTask : doubleClickTasks) {
                                controllerInfo->vncControl->sendInput(clickTask);
                            }
                            actionName = "双击";
                        } else if (clickType != "none") { // 默认为左键点击，除非明确指定为none
                            controllerInfo->vncControl->sendInput(mouseLeftClick());
                            actionName = "左键点击";
                        }
                        
                        // 统一的成功日志 - 只打印一条
                        AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 图像\"" + task->getTemplateName() + "\"匹配成功，" + actionName + " (" + 
                                   std::to_string(globalMatchLocation.x) + "," + std::to_string(globalMatchLocation.y) + ")");
                        
                        // 🔧 更新结果：返回切片内坐标给脚本使用
                        finalResult.matchLocation = sliceMatchLocation;
                        
                    } else {
                        AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": VNC控制器不可用，无法移动鼠标和点击");
                    }
                } catch (const std::exception& e) {
                    AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 移动鼠标和点击时发生异常: " + std::string(e.what()));
                } catch (...) {
                    AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 移动鼠标和点击时发生未知异常");
                }
            } else {
                finalResult.errorMessage = "Match not found.";
                // VISUAL_MATCH 匹配失败时暂停当前切片，并将任务重新入队
                AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": VISUAL_MATCH匹配失败，暂停切片 " + std::to_string(task->getSliceNumber()));

                // ✅ 将失败的任务重新加入到队列头部，方便后续手动重新执行
                reEnqueueFailedTask(vmName, task);

                // 暂停切片
                pauseSlice(vmName, task->getSliceNumber());
                
                // 为原任务设置暂停结果，让ScriptExecutor知道任务已暂停
                finalResult.success = false;
                finalResult.errorMessage = "图像匹配失败，切片已暂停等待恢复";
                if(task->getPromise()) {
                    try {
                        task->getPromise()->set_value(finalResult);
                    } catch (const std::future_error& e) {
                        AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": Promise设置失败: " + std::string(e.what()));
                    }
                }
                return;
            }
        } else {
            finalResult.success = false;
            finalResult.errorMessage = "模板异步匹配等待超时···.";
            AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 异步匹配超时 '" + task->getTemplateName() + "'，暂停切片 " + std::to_string(task->getSliceNumber()));

            // 直接暂停切片，不重新入队任务
            pauseSlice(vmName, task->getSliceNumber());
            
            // 为原任务设置超时结果，让ScriptExecutor知道任务已暂停
            if(task->getPromise()) {
                try {
                    task->getPromise()->set_value(finalResult);
                } catch (const std::future_error& e) {
                    AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": Promise设置失败: " + std::string(e.what()));
                }
            }
            return;
        }
    } catch (const std::exception& e) {
        finalResult.success = false;
        finalResult.errorMessage = "Exception from vision future: " + std::string(e.what());
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 异步匹配异常 '" + task->getTemplateName() + "': " + e.what() + "，暂停切片 " + std::to_string(task->getSliceNumber()));

        // 直接暂停切片，不重新入队任务
        pauseSlice(vmName, task->getSliceNumber());
        
        // 为原任务设置异常结果，让ScriptExecutor知道任务已暂停
        if(task->getPromise()) {
            try {
                task->getPromise()->set_value(finalResult);
            } catch (const std::future_error& fe) {
                AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": Promise设置失败: " + std::string(fe.what()));
            }
        }
        return;
    }
    
    if(task->getPromise()) task->getPromise()->set_value(finalResult);
}

//视觉文字匹配1
void DispatchCenter::processWordsMatchTask(const std::string& vmName, VMControllerInfo* controllerInfo, WordsMatchVisionTask* task) {
    // 使用指定的切片编号
    int sliceNumber = task->getSliceNumber();
    //检查传入的参数是否有效task 
    //(文字匹配任务) 是否为空。
    //controllerInfo(VM 控制器信息) 是否为空。
    //controllerInfo->visionProcessor(视觉处理器) 是否已初始化。
    //controllerInfo->vncControl(VNC 控制器) 是否已初始化。
    if (!task || !controllerInfo || !controllerInfo->visionProcessor || !controllerInfo->vncControl) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 文字匹配的某些参数传递无效");
        if(task && task->getPromise()) {
            WordsMatchResult res; 
            res.success = false; 
            res.errorMessage = "没有正确的文字匹配初始化";
            task->getPromise()->set_value(res);
        }
        return;
    }
    
    // ✅ 添加切片状态检查，防止在暂停状态下执行任务
    auto* stateManager = VMStateManager::getInstance();
    if (stateManager) {
        SliceState currentState = stateManager->getSliceState(vmName, sliceNumber);
        AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 切片 " + std::to_string(sliceNumber) + " 当前状态: " + std::to_string(static_cast<int>(currentState)));

        if (stateManager->isSlicePaused(vmName, sliceNumber)) {
            AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 切片 " + std::to_string(sliceNumber) + " 已暂停，跳过TEXT_MATCH任务");
            if(task && task->getPromise()) {
                WordsMatchResult res;
                res.success = false;
                res.errorMessage = "切片已暂停，任务跳过";
                task->getPromise()->set_value(res);
            }
            return;
        }
    } else {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": VMStateManager实例为空");
    }

    if (task->getWordsToMatch().empty()) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 没有文字匹配的任务");
        WordsMatchResult res;
        res.success = false;
        res.errorMessage = "没有文字匹配的任务";
        if(task->getPromise()) task->getPromise()->set_value(res);
        return;
    }
    //VMVision::findTextAsync 当前只接受一个字符串作为输入。因此，它只取 task->getWordsToMatch() 向量中的第一个文字进行匹配。
    //它也明确指出，WordsMatchVisionTask 中定义的区域（ROI）和阈值参数当前并没有被 findTextAsync 函数使用，这是一个"当前 API 的局限性"。
    std::string wordToMatch = task->getWordsToMatch().front(); 

    //创建一个 std::promise 对象 promiseForVision，用于接收 findTextAsync 的异步结果。
    auto promiseForVision = std::make_shared<std::promise<std::pair<bool, cv::Point>>>();
    std::future<std::pair<bool, cv::Point>> futureFromVision = promiseForVision->get_future();

    // 🔧 修复坐标转换逻辑：传入的ROI是基于切片的相对坐标，无需转换
    cv::Rect sliceROI = task->getRoi(); // 基于切片的ROI坐标
    AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 使用切片内ROI坐标: (" + 
               std::to_string(sliceROI.x) + "," + std::to_string(sliceROI.y) + "," + 
               std::to_string(sliceROI.width) + "," + std::to_string(sliceROI.height) + ")");
    
    //待匹配的文字和 promise 传递给视觉处理器，异步地开始查找文字（使用切片内ROI）
    controllerInfo->visionProcessor->findTextAsync(
        wordToMatch,
        task->getThreshold(),
        sliceROI,  // 直接使用切片内的ROI坐标
        task->getSliceNumber(),
        promiseForVision
    );

    WordsMatchResult finalResult;
    try {
        // Add a timeout to future.get()
        if (futureFromVision.wait_for(std::chrono::seconds(10)) == std::future_status::ready) {
            std::pair<bool, cv::Point> visionResultPair = futureFromVision.get();
            finalResult.success = visionResultPair.first;
            if (visionResultPair.first) {
                // 🔧 修复坐标转换逻辑：VMVision返回的是基于切片的坐标
                cv::Point sliceMatchLocation = visionResultPair.second;
                
                // 将切片坐标转换为全局坐标用于鼠标操作
                cv::Point globalMatchLocation = sliceMatchLocation; // 默认值
                if (controllerInfo->visionProcessor && controllerInfo->visionProcessor->getScreenFrameDistributor()) {
                    globalMatchLocation = controllerInfo->visionProcessor->getScreenFrameDistributor()->sliceToGlobalCoordinate(sliceMatchLocation, task->getSliceNumber());
                }
                
                finalResult.matchLocation = sliceMatchLocation; // 返回切片内坐标给脚本
                finalResult.recognizedTexts.push_back(wordToMatch);
                finalResult.allMatchLocations.push_back(cv::Rect(sliceMatchLocation.x, sliceMatchLocation.y, 0, 0));
                finalResult.errorMessage = "文字已在匹配中找到.";
                
                // 文字匹配成功后，根据鼠标点击类型执行相应操作
                try {
                    if (controllerInfo->vncControl) {
                        // 移动鼠标到匹配位置（使用全局坐标）
                        controllerInfo->vncControl->sendInput(mouseMoveCoord(globalMatchLocation.x, globalMatchLocation.y));
                        
                        // 根据鼠标点击类型执行相应操作
                        std::string clickType = task->getMouseClickType();
                        std::string actionName = "移动";
                        if (clickType == "right") {
                            controllerInfo->vncControl->sendInput(mouseRightClick());
                            actionName = "右键点击";
                        } else if (clickType == "double") {
                            // 双击需要发送两个左键点击任务
                            auto doubleClickTasks = mouseDoubleClick();
                            for (const auto& clickTask : doubleClickTasks) {
                                controllerInfo->vncControl->sendInput(clickTask);
                            }
                            actionName = "双击";
                        } else if (clickType != "none") { // 默认为左键点击，除非明确指定为none
                            controllerInfo->vncControl->sendInput(mouseLeftClick());
                            actionName = "左键点击";
                        }
                        
                        // 统一的成功日志 - 只打印一条
                        AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 文字\"" + wordToMatch + "\"匹配成功，" + actionName + " (" + 
                                   std::to_string(globalMatchLocation.x) + "," + std::to_string(globalMatchLocation.y) + ")");
                        
                        // 🔧 更新结果：返回切片内坐标给脚本使用
                        finalResult.matchLocation = sliceMatchLocation;
                        
                    } else {
                        AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": VNC控制器不可用，无法移动鼠标和点击");
                    }
                } catch (const std::exception& e) {
                    AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 移动鼠标和点击时发生异常: " + std::string(e.what()));
                } catch (...) {
                    AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 移动鼠标和点击时发生未知异常");
                }
            } else {
                finalResult.errorMessage = "文字没有在匹配中找到.";
                // TEXT_MATCH 匹配失败时暂停当前切片，并将任务重新入队
                AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": TEXT_MATCH匹配失败，暂停切片 " + std::to_string(sliceNumber));

                // ✅ 将失败的任务重新加入到队列头部，方便后续手动重新执行
                reEnqueueFailedTask(vmName, task);

                // 暂停切片
                pauseSlice(vmName, sliceNumber);
                
                // 为原任务设置失败结果
                finalResult.success = false;
                finalResult.errorMessage = "任务匹配失败，切片已暂停等待恢复";
                if(task->getPromise()) {
                    try {
                        task->getPromise()->set_value(finalResult);
                    } catch (const std::future_error& e) {
                        AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": Promise设置失败: " + std::string(e.what()));
                    }
                }
                return;
            }
        } else {
            finalResult.success = false;
            finalResult.errorMessage = "等待文字匹配结果超时···";
            AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 匹配结果超时'" + wordToMatch + "'，暂停切片 " + std::to_string(sliceNumber));

            // 直接暂停切片，不重新入队任务
            pauseSlice(vmName, sliceNumber);
            
            // 为原任务设置超时结果
            if(task->getPromise()) {
                try {
                    task->getPromise()->set_value(finalResult);
                } catch (const std::future_error& e) {
                    AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": Promise设置失败: " + std::string(e.what()));
                }
            }
            return;
        }
    } catch (const std::exception& e) {
        finalResult.success = false;
        finalResult.errorMessage = "Exception from vision future: " + std::string(e.what());
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": Exception for findTextAsync on '" + wordToMatch + "': " + e.what() + "，暂停切片 " + std::to_string(sliceNumber));

        // 直接暂停切片，不重新入队任务
        pauseSlice(vmName, sliceNumber);
        
        // 为原任务设置异常结果
        if(task->getPromise()) {
            try {
                task->getPromise()->set_value(finalResult);
            } catch (const std::future_error& fe) {
                AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": Promise设置失败: " + std::string(fe.what()));
            }
        }
        return;
    }

    if(task->getPromise()) task->getPromise()->set_value(finalResult);
}

// 文本识别任务处理
void DispatchCenter::processTextRecognizeTask(const std::string& vmName, VMControllerInfo* controllerInfo, TextRecognizeVisionTask* task) {
    // 检查任务、控制器信息、视觉处理器和VNC控制是否都已初始化
    if (!task || !controllerInfo || !controllerInfo->visionProcessor || !controllerInfo->vncControl) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": Invalid arguments for processTextRecognizeTask.");
        if(task && task->getPromise()) {
            TextRecognizeResult res; 
            res.success = false; 
            res.errorMessage = "Invalid arguments or uninitialized controller components for TextRecognizeVisionTask.";
            task->getPromise()->set_value(res);
        }
        return;
    }
    // 删除冗余的接收文本识别任务日志
    
    // 创建一个共享的promise用于异步操作
    auto promiseForVision = std::make_shared<std::promise<TextRecognizeResult>>();
    // 获取对应的future用于等待结果
    std::future<TextRecognizeResult> futureFromVision = promiseForVision->get_future();

    // 🔧 坐标转换：将切片内ROI转换为全局ROI
    cv::Rect globalROI = task->getRoi(); // 默认值
    if (controllerInfo->visionProcessor && controllerInfo->visionProcessor->getScreenFrameDistributor()) {
        globalROI = controllerInfo->visionProcessor->getScreenFrameDistributor()->sliceToGlobalROI(task->getRoi(), task->getSliceNumber());
    }
    
    // 调用视觉处理器的异步文本识别函数
    AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + "开始文本识别，切片" + std::to_string(task->getSliceNumber()) + 
               "内ROI: (" + std::to_string(task->getRoi().x) + ", " + std::to_string(task->getRoi().y) + ", " + 
               std::to_string(task->getRoi().width) + ", " + std::to_string(task->getRoi().height) + 
               ") -> 全局ROI: (" + std::to_string(globalROI.x) + ", " + std::to_string(globalROI.y) + ", " + 
               std::to_string(globalROI.width) + ", " + std::to_string(globalROI.height) + ")");
    
    controllerInfo->visionProcessor->recognizeTextAsync(
        globalROI,
        task->getSliceNumber(),
        promiseForVision
    );

    // 设置最终结果对象
    TextRecognizeResult finalResult;
    try { 
        // 异步等待最多10秒内获取识别结果
        if (futureFromVision.wait_for(std::chrono::seconds(10)) == std::future_status::ready) {
            finalResult = futureFromVision.get();
        } else {
            finalResult.success = false; 
            finalResult.errorMessage = "文本识别等待超时···.";
            AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 文本识别超时.");
        }
    } catch (const std::exception& e) {
        finalResult.success = false; 
        finalResult.errorMessage = "Exception from vision future: " + std::string(e.what());
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 文本识别异常: " + e.what());
    }
    
    if(task->getPromise()) task->getPromise()->set_value(finalResult);
}

// 等待画面静止任务处理
void DispatchCenter::processWaitForScreenStillTask(const std::string& vmName, VMControllerInfo* controllerInfo, WaitForScreenStillTask* task) {
    if (!task || !controllerInfo || !controllerInfo->visionProcessor || !controllerInfo->vncControl) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": Invalid arguments for processWaitForScreenStillTask.");
        if (task && task->getPromise()) {
            WaitForScreenStillResult res;
            res.success = false;
            res.errorMessage = "Invalid arguments or uninitialized controller components for WaitForScreenStillTask.";
            task->getPromise()->set_value(res);
        }
        return;
    }

    // 删除冗余的接收等待画面静止任务日志

    // 🔧 坐标转换：将切片内ROI转换为全局ROI
    cv::Rect sliceROI = task->getRoi();
    cv::Rect roi = sliceROI; // 默认值
    if (controllerInfo->visionProcessor && controllerInfo->visionProcessor->getScreenFrameDistributor()) {
        roi = controllerInfo->visionProcessor->getScreenFrameDistributor()->sliceToGlobalROI(sliceROI, task->getSliceNumber());
    }
    int sliceNumber = task->getSliceNumber();
    int checkIntervalSeconds = task->getCheckIntervalSeconds();
    int maxTimeoutSeconds = task->getMaxTimeoutSeconds();

    // OCR频率限制：最小5秒，最大10秒
    const int MIN_OCR_INTERVAL = 5;  // 最小5秒间隔
    const int MAX_OCR_INTERVAL = 10; // 最大10秒间隔
    int actualCheckInterval = checkIntervalSeconds;
    
    if (checkIntervalSeconds < MIN_OCR_INTERVAL) {
        actualCheckInterval = MIN_OCR_INTERVAL;
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": OCR检查间隔过短(" + 
                   std::to_string(checkIntervalSeconds) + "秒)，调整为最小值" + std::to_string(MIN_OCR_INTERVAL) + "秒");
    } else if (checkIntervalSeconds > MAX_OCR_INTERVAL) {
        actualCheckInterval = MAX_OCR_INTERVAL;
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": OCR检查间隔过长(" + 
                   std::to_string(checkIntervalSeconds) + "秒)，调整为最大值" + std::to_string(MAX_OCR_INTERVAL) + "秒");
    }

    AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 开始检测画面静止状态，切片" + std::to_string(sliceNumber) + 
               "内ROI=(" + std::to_string(sliceROI.x) + "," + std::to_string(sliceROI.y) + "," + 
               std::to_string(sliceROI.width) + "," + std::to_string(sliceROI.height) + 
               ") -> 全局ROI=(" + std::to_string(roi.x) + "," + std::to_string(roi.y) + "," + 
               std::to_string(roi.width) + "," + std::to_string(roi.height) + 
               "), 原始间隔=" + std::to_string(checkIntervalSeconds) + "秒" +
               ", 实际间隔=" + std::to_string(actualCheckInterval) + "秒");

    auto startTime = std::chrono::steady_clock::now();
    auto maxDuration = std::chrono::seconds(maxTimeoutSeconds);
    
    std::string previousText = "";
    bool firstCheck = true;
    int consecutiveMatchCount = 0;
    int totalChecks = 0;
    const int requiredConsecutiveMatches = 2; // 需要连续2次相同才认为静止

    WaitForScreenStillResult finalResult;
    finalResult.success = true; // 默认成功，除非出现错误

    while (true) {
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - startTime);
        
        if (elapsed > maxDuration) {
            AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 画面静止检测超时 (" + 
                       std::to_string(maxTimeoutSeconds) + "秒)");
            finalResult.success = false;
            finalResult.errorMessage = "画面静止检测超时";
            finalResult.isStill = false;
            break;
        }

        totalChecks++;

        // 新的流程：先等待固定时间间隔（OCR识别时间不计入等待时间）
        if (!firstCheck) {
            AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 等待 " + std::to_string(actualCheckInterval) + " 秒后进行OCR检测");
            std::this_thread::sleep_for(std::chrono::seconds(actualCheckInterval));
            
            // 检查是否在等待期间超时
            currentTime = std::chrono::steady_clock::now();
            elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - startTime);
            if (elapsed > maxDuration) {
                AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 在等待期间检测超时");
                finalResult.success = false;
                finalResult.errorMessage = "在等待期间检测超时";
                finalResult.isStill = false;
                break;
            }
        }

        try {
            AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 开始异步OCR识别（第 " + std::to_string(totalChecks) + " 次）");
            
            // 创建promise和future用于异步文字识别
            auto promise = std::make_shared<std::promise<TextRecognizeResult>>();
            auto future = promise->get_future();
            
            // 创建文字识别任务
            auto textRecognizeTask = std::make_shared<TextRecognizeVisionTask>(vmName, roi, sliceNumber, promise);

            // 使用线程池异步执行文字识别（不阻塞当前线程）
            auto ocrFuture = threadManager_.submitTask(ThreadPoolType::OCR, [this, vmName, controllerInfo, textRecognizeTask]() {
                try {
                    this->processTextRecognizeTask(vmName, controllerInfo, textRecognizeTask.get());
                } catch (const std::exception& e) {
                    AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": OCR任务异常: " + std::string(e.what()));
                } catch (...) {
                    AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": OCR任务未知异常");
                }
            });

            // 等待OCR识别结果（最多等待15秒）
            auto status = future.wait_for(std::chrono::seconds(15));
            
            // 等待线程池任务完成
            if (ocrFuture.valid()) {
                ocrFuture.wait();
            }
            
            if (status != std::future_status::ready) {
                AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": OCR识别超时（15秒），跳过本次检测");
                continue; // 直接继续下一轮，下一轮会先等待指定间隔
            }

            auto result = future.get();
            if (!result.success) {
                AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": OCR识别失败: " + 
                           result.errorMessage + "，跳过本次检测");
                continue; // 直接继续下一轮，下一轮会先等待指定间隔
            }

            // 将所有识别到的文本合并为一个字符串进行比较
            std::string currentText = "";
            for (const auto& text : result.recognizedTexts) {
                currentText += text;
            }
            
            // 清理文本，移除空白字符和换行符，便于比较
            currentText.erase(std::remove_if(currentText.begin(), currentText.end(), 
                                           [](char c) { return std::isspace(c); }), currentText.end());

            AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 识别到文字: \"" + currentText + "\"");

            if (firstCheck) {
                // 第一次检测，记录基准文本
                if (currentText.empty()) {
                    // 如果第一次识别就是空字符串，不设置为基准，继续等待下次检测
                    AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 第一次检测未识别到文字，继续等待下次检测");
                    // 保持 firstCheck = true，继续下一轮检测
                } else {
                previousText = currentText;
                firstCheck = false;
                consecutiveMatchCount = 1;
                AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 设置基准文字: \"" + previousText + "\"");
                }
            } else {
                // 比较当前文本与上次文本
                if (currentText == previousText) {
                    // 特殊情况：如果两次识别都是空字符串，不认为是静止状态，继续等待
                    if (currentText.empty() && previousText.empty()) {
                        AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 连续两次都未识别到文字，继续等待（不认为是静止状态）");
                        // 不增加 consecutiveMatchCount，保持当前状态继续检测
                    } else {
                    consecutiveMatchCount++;
                    AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 文字相同，连续匹配次数: " + 
                               std::to_string(consecutiveMatchCount) + "/" + std::to_string(requiredConsecutiveMatches));
                    
                    if (consecutiveMatchCount >= requiredConsecutiveMatches) {
                        AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 画面已静止，连续 " + 
                                   std::to_string(consecutiveMatchCount) + " 次检测结果相同");
                        finalResult.isStill = true;
                        break;
                        }
                    }
                } else {
                    // 文字发生变化，重置计数器，保存新的文本用于下次比较
                    AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 文字发生变化，从 \"" + 
                               previousText + "\" 变为 \"" + currentText + "\"，重置计数器");
                    previousText = currentText;
                    consecutiveMatchCount = 1;
                }
            }

        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 画面静止检测时发生异常: " + 
                       std::string(e.what()));
            finalResult.success = false;
            finalResult.errorMessage = "检测过程中发生异常: " + std::string(e.what());
            break;
        } catch (...) {
            AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 画面静止检测时发生未知异常");
            finalResult.success = false;
            finalResult.errorMessage = "检测过程中发生未知异常";
            break;
        }

        // 注意：下一轮循环开始时会先等待指定时间间隔，这样确保OCR识别时间不计入等待时间
    }

    // 设置最终结果
    auto endTime = std::chrono::steady_clock::now();
    auto totalElapsed = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    finalResult.totalChecks = totalChecks;
    finalResult.elapsedSeconds = totalElapsed.count() / 1000.0;

    AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 画面静止检测完成，总检测次数: " + 
               std::to_string(totalChecks) + ", 耗时: " + std::to_string(finalResult.elapsedSeconds) + "秒");

    if (task->getPromise()) task->getPromise()->set_value(finalResult);
}

// 等待视觉匹配任务处理
void DispatchCenter::processWaitForVisualMatchTask(const std::string& vmName, VMControllerInfo* controllerInfo, WaitForVisualMatchTask* task) {
    if (!task || !controllerInfo || !controllerInfo->visionProcessor || !controllerInfo->vncControl) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": Invalid arguments for processWaitForVisualMatchTask.");
        if (task && task->getPromise()) {
            WaitForVisualMatchResult res;
            res.success = false;
            res.errorMessage = "Invalid arguments or uninitialized controller components for WaitForVisualMatchTask.";
            task->getPromise()->set_value(res);
        }
        return;
    }

    // 删除冗余的接收等待视觉匹配任务日志

    std::string templateName = task->getTemplateName();
    int sliceNumber = task->getSliceNumber();
    int checkIntervalSeconds = task->getCheckIntervalSeconds();
    int maxTimeoutSeconds = task->getMaxTimeoutSeconds();
    double threshold = task->getThreshold();

    // 视觉匹配频率限制：最小3秒，最大15秒（比OCR频率更宽松）
    const int MIN_VISUAL_INTERVAL = 3;  // 最小3秒间隔
    const int MAX_VISUAL_INTERVAL = 15; // 最大15秒间隔
    int actualCheckInterval = checkIntervalSeconds;
    
    if (checkIntervalSeconds < MIN_VISUAL_INTERVAL) {
        actualCheckInterval = MIN_VISUAL_INTERVAL;
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 视觉匹配检查间隔过短(" + 
                   std::to_string(checkIntervalSeconds) + "秒)，调整为最小值" + std::to_string(MIN_VISUAL_INTERVAL) + "秒");
    } else if (checkIntervalSeconds > MAX_VISUAL_INTERVAL) {
        actualCheckInterval = MAX_VISUAL_INTERVAL;
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 视觉匹配检查间隔过长(" + 
                   std::to_string(checkIntervalSeconds) + "秒)，调整为最大值" + std::to_string(MAX_VISUAL_INTERVAL) + "秒");
    }

    AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 开始等待视觉匹配，模板=" + templateName + 
               ", 切片=" + std::to_string(sliceNumber) + 
               ", 阈值=" + std::to_string(threshold) +
               ", 原始间隔=" + std::to_string(checkIntervalSeconds) + "秒" +
               ", 实际间隔=" + std::to_string(actualCheckInterval) + "秒");

    auto startTime = std::chrono::steady_clock::now();
    auto maxDuration = std::chrono::seconds(maxTimeoutSeconds);
    
    bool firstCheck = true;
    int totalChecks = 0;

    WaitForVisualMatchResult finalResult;
    finalResult.success = true; // 默认成功，除非出现错误

    while (true) {
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - startTime);
        
        if (elapsed > maxDuration) {
            AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 视觉匹配检测超时 (" + 
                       std::to_string(maxTimeoutSeconds) + "秒)");
            finalResult.success = false;
            finalResult.errorMessage = "视觉匹配检测超时";
            finalResult.matchFound = false;
            break;
        }

        totalChecks++;

        // 新的流程：先等待固定时间间隔（视觉匹配时间不计入等待时间）
        if (!firstCheck) {
            AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 等待 " + std::to_string(actualCheckInterval) + " 秒后进行视觉匹配检测");
            std::this_thread::sleep_for(std::chrono::seconds(actualCheckInterval));
            
            // 检查是否在等待期间超时
            currentTime = std::chrono::steady_clock::now();
            elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - startTime);
            if (elapsed > maxDuration) {
                AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 在等待期间检测超时");
                finalResult.success = false;
                finalResult.errorMessage = "在等待期间检测超时";
                finalResult.matchFound = false;
                break;
            }
        }

        try {
            AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 开始异步视觉匹配检测（第 " + std::to_string(totalChecks) + " 次）");
            
            // 创建promise和future用于异步视觉匹配
            auto promise = std::make_shared<std::promise<VisionMatchResult>>();
            auto future = promise->get_future();
            
            // 创建视觉匹配任务 (使用全屏ROI)
            auto visualMatchTask = std::make_shared<ImageMatchVisionTask>(vmName, templateName, threshold, "", cv::Rect(0, 0, 0, 0), sliceNumber, "left", promise);

            // 使用线程池异步执行视觉匹配（不阻塞当前线程）
            auto visualFuture = threadManager_.submitTask(ThreadPoolType::VISION, [this, vmName, controllerInfo, visualMatchTask]() {
                try {
                    this->processImageMatchTask(vmName, controllerInfo, visualMatchTask.get());
                } catch (const std::exception& e) {
                    AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 视觉匹配任务异常: " + std::string(e.what()));
                } catch (...) {
                    AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 视觉匹配任务未知异常");
                }
            });

            // 等待视觉匹配结果（最多等待20秒）
            auto status = future.wait_for(std::chrono::seconds(20));
            
            // 等待线程池任务完成
            if (visualFuture.valid()) {
                visualFuture.wait();
            }
            
            if (status != std::future_status::ready) {
                AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 视觉匹配超时（20秒），跳过本次检测");
                continue; // 直接继续下一轮，下一轮会先等待指定间隔
            }

            auto result = future.get();
            if (!result.success) {
                AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 视觉匹配未找到目标: " + 
                           result.errorMessage + "，继续下一轮检测");
                firstCheck = false; // 确保下一轮会等待间隔
                continue; // 直接继续下一轮，下一轮会先等待指定间隔
            }

            // 匹配成功！
            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 视觉匹配成功找到目标！位置: (" + 
                       std::to_string(result.matchLocation.x) + ", " + std::to_string(result.matchLocation.y) + 
                       "), 置信度: " + std::to_string(result.confidence));
            
            finalResult.matchFound = true;
            finalResult.matchLocation = result.matchLocation;
            finalResult.confidence = result.confidence;
            break;

        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 视觉匹配检测时发生异常: " + 
                       std::string(e.what()));
            finalResult.success = false;
            finalResult.errorMessage = "检测过程中发生异常: " + std::string(e.what());
            break;
        } catch (...) {
            AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 视觉匹配检测时发生未知异常");
            finalResult.success = false;
            finalResult.errorMessage = "检测过程中发生未知异常";
            break;
        }

        // 设置firstCheck为false，确保下一轮会等待指定间隔
        firstCheck = false;
    }

    // 设置最终结果
    auto endTime = std::chrono::steady_clock::now();
    auto totalElapsed = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    finalResult.totalChecks = totalChecks;
    finalResult.elapsedSeconds = totalElapsed.count() / 1000.0;

    AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 视觉匹配检测完成，总检测次数: " + 
               std::to_string(totalChecks) + ", 耗时: " + std::to_string(finalResult.elapsedSeconds) + "秒" +
               ", 匹配结果: " + (finalResult.matchFound ? "成功" : "失败"));

    if (task->getPromise()) task->getPromise()->set_value(finalResult);
}

// 查找 VM controller instance
VMControllerInfo* DispatchCenter::findVMController(const std::string& vmName, std::chrono::milliseconds timeout) {
    std::unique_lock<std::mutex> lock(controllersMutex_);
    if (controllersCV_.wait_for(lock, timeout, [this, &vmName] {
        auto it_cv = vmControllers_.find(vmName); // Search under lock
        return it_cv != vmControllers_.end() && it_cv->second.successfullyConnected.load();
        })) {
        // Re-find after lock is reacquired, as predicate only guarantees state at time of check.
        // The CV wait ensures that when it wakes up due to notify (and predicate is true),
        // the condition holds. However, map iterators can be invalidated by other operations
        // if the lock was released. Here, the lock is held throughout, so it->second is safe.
        auto it = vmControllers_.find(vmName);
        if (it != vmControllers_.end() && it->second.successfullyConnected.load()) { // Double check after waking
            return &it->second;
        }
    }
    AddLogInfo(LogLevel::Warning, "[DispatchCenter] Timed out or failed to find active VM controller: " + vmName);
    return nullptr;
}

// Public const version : Locks and waits
const VMControllerInfo * DispatchCenter::findVMController(const std::string & vmName, std::chrono::milliseconds timeout) const {
    std::unique_lock<std::mutex> lock(const_cast<std::mutex&>(controllersMutex_));
    if (controllersCV_.wait_for(lock, timeout, [this, &vmName] {
        auto it_cv = vmControllers_.find(vmName); // Search under lock
        return it_cv != vmControllers_.end() && it_cv->second.successfullyConnected.load();
        })) {
        auto it = vmControllers_.find(vmName);
        if (it != vmControllers_.end() && it->second.successfullyConnected.load()) { // Double check after waking
            return &it->second;
        }
    }
    AddLogInfo(LogLevel::Warning, "[DispatchCenter] Timed out or failed to find active VM controller (const): " + vmName);
    return nullptr;
}

// Private unlocked non-const version: No lock, no wait
VMControllerInfo* DispatchCenter::findVMController_unlocked(const std::string& vmName, std::chrono::milliseconds /*timeout_ignored*/) {
    auto it = vmControllers_.find(vmName);
    if (it != vmControllers_.end() && it->second.successfullyConnected.load()) { // Check if connected or just exists based on needs
        return &it->second;
    }
    // AddLogInfo for unlocked version might be too verbose, consider if needed.
    return nullptr;
}

// Private unlocked const version: No lock, no wait
const VMControllerInfo* DispatchCenter::findVMController_unlocked(const std::string& vmName, std::chrono::milliseconds /*timeout_ignored*/) const {
    auto it = vmControllers_.find(vmName);
    if (it != vmControllers_.end() && it->second.successfullyConnected.load()) { // Check if connected or just exists based on needs
        return &it->second;
    }
    // AddLogInfo for unlocked version might be too verbose, consider if needed.
    return nullptr;
}



void DispatchCenter::stopAllVMControllers() {
    std::map<std::string, VMControllerInfo> controllersToStop;
    {
        std::lock_guard<std::mutex> lock(controllersMutex_);
        // Move all VMControllerInfo objects out of vmControllers_ into a local map.
        // This allows releasing controllersMutex_ while joining threads,
        // preventing deadlocks if a joining thread needs to interact with DispatchCenter (though unlikely here).
        controllersToStop.swap(vmControllers_);
    } // controllersMutex_ is released here


    for (auto& pair : controllersToStop) {
        try {
            AddLogInfo(LogLevel::Debug, "[DispatchCenter] Signaling stop for VM: " + pair.first);
            pair.second.stopRequested.store(true);
            // Notify its own condition variable for task processing, so it can wake up and see stopRequested
            {
                std::lock_guard<std::mutex> qLock(pair.second.taskQueueMutex_); // Lock its specific queue mutex
                pair.second.taskQueueCV_.notify_all();
            } // queue mutex released

            // 线程由统一线程管理器管理，无需手动处理
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "[DispatchCenter] 停止VM [" + pair.first + "] 时发生异常: " + std::string(e.what()));
        } catch (...) {
            AddLogInfo(LogLevel::Error, "[DispatchCenter] 停止VM [" + pair.first + "] 时发生未知异常");
        }
    }
    // controllersToStop map goes out of scope here, and its VMControllerInfo objects are destructed.
    // VMControllerInfo destructor should ideally not join threads again.

    controllersCV_.notify_all(); // Notify any external waiters that all VMs are now gone/stopped.
}

// 清理函数，用于程序退出时
void DispatchCenter::cleanup() {
    try {
        AddLogInfo(LogLevel::Info, "[DispatchCenter] 开始清理...");
        
        // 首先调用安全停止
        stopAllVMControllersSafe();
        
        // 等待一段时间让线程自然退出
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        
        // 清理容器
        {
            std::lock_guard<std::mutex> lock(controllersMutex_);
            vmControllers_.clear();
        }
        
        AddLogInfo(LogLevel::Info, "[DispatchCenter] 清理完成");
        
    } catch (...) {
        // 清理过程中忽略所有异常
    }
}

// 安全的停止函数，温和地停止所有VM控制器
void DispatchCenter::stopAllVMControllersSafe() {
    try {
        AddLogInfo(LogLevel::Info, "[DispatchCenter] 开始安全停止所有VM控制器...");
        
        // 第1步：获取当前所有VM名称（快照方式，避免长时间锁定）
        std::vector<std::string> vmNames;
        {
            std::lock_guard<std::mutex> lock(controllersMutex_);
            for (const auto& pair : vmControllers_) {
                vmNames.push_back(pair.first);
            }
            AddLogInfo(LogLevel::Info, "[DispatchCenter] 获取到 " + std::to_string(vmNames.size()) + " 个VM需要停止");
        }
        
        // 第2步：逐个设置停止标志（每个VM独立处理，失败不影响其他VM）
        for (const auto& vmName : vmNames) {
            try {
                std::lock_guard<std::mutex> lock(controllersMutex_);
                auto it = vmControllers_.find(vmName);
                if (it != vmControllers_.end()) {
                    // 设置停止标志
                    it->second.stopRequested.store(true);
                    it->second.pollerStopRequested.store(true);
                    
                    // 温和地通知所有等待的线程
                    try {
                        it->second.taskQueueCV_.notify_all();
                        it->second.pollerCV_.notify_all();
                        it->second.executionCV_.notify_all();
                    } catch (...) {
                        // 通知失败不影响停止流程
                    }
                    
                    AddLogInfo(LogLevel::Info, "[DispatchCenter] VM [" + vmName + "] 停止标志已设置");
                }
            } catch (...) {
                AddLogInfo(LogLevel::Warning, "[DispatchCenter] 设置VM [" + vmName + "] 停止标志时发生异常，继续处理其他VM");
            }
        }
        
        AddLogInfo(LogLevel::Info, "[DispatchCenter] 停止标志设置完成，等待线程自然退出...");
        
        // 第3步：等待线程有足够时间退出
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        
        // 第4步：安全清理控制器容器
        try {
            std::lock_guard<std::mutex> lock(controllersMutex_);
            int clearedCount = vmControllers_.size();
            vmControllers_.clear();
            AddLogInfo(LogLevel::Info, "[DispatchCenter] 已清理 " + std::to_string(clearedCount) + " 个VM控制器");
        } catch (...) {
            AddLogInfo(LogLevel::Warning, "[DispatchCenter] 清理控制器容器时发生异常，但停止操作已完成");
        }
        
        // 第5步：重置状态管理器
        try {
            VMStateManager::getInstance()->setAvailableVMs({});
            AddLogInfo(LogLevel::Info, "[DispatchCenter] VMStateManager状态已重置");
        } catch (...) {
            AddLogInfo(LogLevel::Warning, "[DispatchCenter] 重置VMStateManager状态时发生异常");
        }
        
        AddLogInfo(LogLevel::Info, "[DispatchCenter] 安全停止流程完成");
        
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] 安全停止过程中发生未知异常，但操作已尽可能完成");
    }
}



// Add DirectKeyboardMouseActionTask
std::future<KeyboardMouseResult> DispatchCenter::addDirectKeyboardMouseActionTask(
    const std::string& vmName,
    const std::vector<InputTask>& actions,
    int sliceNumber)
{
    auto promise = std::make_shared<std::promise<KeyboardMouseResult>>();
    std::future<KeyboardMouseResult> future = promise->get_future();

    VMControllerInfo* controllerInfo = findVMController(vmName);
    if (!controllerInfo || controllerInfo->sliceQueues.find(sliceNumber) == controllerInfo->sliceQueues.end()) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": Controller or slice queue invalid for addDirectKeyboardMouseActionTask.");
        KeyboardMouseResult res;
        res.success = false;
        res.errorMessage = "VM controller or slice queue not available.";
        promise->set_value(res);
        return future;
    }

    if (actions.empty()) {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": Empty actions list for addDirectKeyboardMouseActionTask.");
        KeyboardMouseResult res; 
        res.success = false;
        res.errorMessage = "No actions provided.";
        promise->set_value(res);
        return future;
    }

    auto task = std::make_shared<DirectKeyboardMouseActionTask>(vmName, actions, promise, sliceNumber);
    controllerInfo->sliceQueues[sliceNumber]->taskQueue->push(task);
    
    // 通知轮询器有新任务
    {
        std::lock_guard<std::mutex> pollerLock(controllerInfo->pollerMutex_);
        controllerInfo->pollerCV_.notify_one();
    }
    
    controllerInfo->taskQueueCV_.notify_one();
    
    return future;
}



// 增加图片匹配任务
std::future<VisionMatchResult> DispatchCenter::addImageMatchTask(
    const std::string& vmName,
    const std::string& templateName,
    double threshold,
    const std::string& imagePath, // 模板图像文件的路径
    cv::Rect roi, // cv::Rect 是 OpenCV 中的一个矩形结构体，表示屏幕上要进行搜索的兴趣区域 (Region of Interest)。如果设置了 ROI，匹配算法将只在这个区域内搜索，从而提高效率并减少误报。
    int sliceNumber,
    const std::string& mouseClickType // 新增：鼠标点击类型
    )
{
    auto promise = std::make_shared<std::promise<VisionMatchResult>>();
    std::future<VisionMatchResult> future = promise->get_future();

    VMControllerInfo* controllerInfo = findVMController(vmName);

    // 检查控制器和特定切片的队列
    if (!controllerInfo || controllerInfo->sliceQueues.find(sliceNumber) == controllerInfo->sliceQueues.end()) {
        VisionMatchResult res; 
        res.success = false;
        res.errorMessage = "VM controller or slice queue not available.";
        promise->set_value(res);
        return future;
    }
    auto visionProcessor = getVisionProcessor(vmName);
    if (!visionProcessor) {
        VisionMatchResult res; 
        res.success = false;
        res.errorMessage = "Vision processor not available after getVisionProcessor.";
        promise->set_value(res);
        return future;
    }
    AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + "模板文本为：" + templateName);
    //ImageMatchVisionTask 是一个自定义的任务类，它封装了执行图像匹配所需的所有信息
    auto task = std::make_shared<ImageMatchVisionTask>(
        vmName,
        templateName,
        threshold,
        imagePath,
        roi,
        sliceNumber,
        mouseClickType,
        std::move(promise)
    );
    //将创建好的 ImageMatchVisionTask 共享指针添加到特定虚拟机的任务队列中。这个队列通常由一个或多个工作线程监视，这些工作线程会从中取出任务并执行。
    controllerInfo->sliceQueues[sliceNumber]->taskQueue->push(task); // 放入指定切片队列
    
    // 通知轮询器有新任务
    {
        std::lock_guard<std::mutex> pollerLock(controllerInfo->pollerMutex_);
        controllerInfo->pollerCV_.notify_one();
    }
    
    //controllerInfo->taskQueueCV_.notify_one();：通知（唤醒）一个正在等待 taskQueueCV_ 的工作线程，告诉它任务队列中有新任务可以处理了。
    controllerInfo->taskQueueCV_.notify_one();
    
    return future;
}

// 创建并添加任务到队列
std::future<WordsMatchResult> DispatchCenter::addWordsMatchTask(
    const std::string& vmName,
    const std::vector<std::string>& wordsToMatch,
    double threshold,
    cv::Rect roi,
    int sliceNumber,
    const std::string& mouseClickType) // 新增：鼠标点击类型
{
    auto promise = std::make_shared<std::promise<WordsMatchResult>>();
    std::future<WordsMatchResult> future = promise->get_future();

    VMControllerInfo* controllerInfo = findVMController(vmName);
    // 检查控制器和特定切片的队列
    if (!controllerInfo || controllerInfo->sliceQueues.find(sliceNumber) == controllerInfo->sliceQueues.end()) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": Controller or slice queue invalid for addWordsMatchTask.");
        WordsMatchResult res; 
        res.success = false;
        res.errorMessage = "VM controller or slice queue not available.";
        promise->set_value(res);
        return future;
    }
    auto visionProcessor = getVisionProcessor(vmName);
    if (!visionProcessor) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": vision processor unavailable after getVisionProcessor for addWordsMatchTask.");
        WordsMatchResult res; 
        res.success = false;
        res.errorMessage = "Vision processor not available after getVisionProcessor.";
        promise->set_value(res);
        return future;
    }

    auto task = std::make_shared<WordsMatchVisionTask>(vmName, wordsToMatch, threshold, roi, sliceNumber, mouseClickType, promise);
    controllerInfo->sliceQueues[sliceNumber]->taskQueue->push(task); // 放入指定切片队列
    
    // 通知轮询器有新任务
    {
        std::lock_guard<std::mutex> pollerLock(controllerInfo->pollerMutex_);
        controllerInfo->pollerCV_.notify_one();
    }
    
    controllerInfo->taskQueueCV_.notify_one();
    
    return future;
}

// 创建并添加文本识别任务到队列
std::future<TextRecognizeResult> DispatchCenter::addTextRecognizeTask(
    const std::string& vmName,
    cv::Rect roi,
    int sliceNumber)
{
    auto promise = std::make_shared<std::promise<TextRecognizeResult>>();
    std::future<TextRecognizeResult> future = promise->get_future();

    VMControllerInfo* controllerInfo = this->findVMController(vmName);
    // 检查控制器和特定切片的队列
    if (!controllerInfo || controllerInfo->sliceQueues.find(sliceNumber) == controllerInfo->sliceQueues.end()) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": Controller or task queue invalid for addTextRecognizeTask.");
        TextRecognizeResult res; 
        res.success = false;
        res.errorMessage = "VM controller or slice queue not available.";
        promise->set_value(res);
        return future;
    }
    auto visionProcessor = this->getVisionProcessor(vmName);
    if (!visionProcessor) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": vision processor unavailable after getVisionProcessor for addTextRecognizeTask.");
        TextRecognizeResult res; 
        res.success = false;
        res.errorMessage = "Vision processor not available after getVisionProcessor.";
        promise->set_value(res);
        return future;
    }

    auto task = std::make_shared<TextRecognizeVisionTask>(vmName, roi, sliceNumber, promise);
    controllerInfo->sliceQueues[sliceNumber]->taskQueue->push(task); // 放入指定切片队列
    
    // 通知轮询器有新任务
    {
        std::lock_guard<std::mutex> pollerLock(controllerInfo->pollerMutex_);
        controllerInfo->pollerCV_.notify_one();
    }
    
    controllerInfo->taskQueueCV_.notify_one();
    
    return future;
}

// 创建并添加等待画面静止任务到队列
std::future<WaitForScreenStillResult> DispatchCenter::addWaitForScreenStillTask(
    const std::string& vmName,
    int sliceNumber,
    int x,
    int y,
    int width,
    int height,
    int checkIntervalSeconds,
    int maxTimeoutSeconds)
{
    auto promise = std::make_shared<std::promise<WaitForScreenStillResult>>();
    std::future<WaitForScreenStillResult> future = promise->get_future();

    // 创建cv::Rect对象
    cv::Rect roi(x, y, width, height);

    VMControllerInfo* controllerInfo = this->findVMController(vmName);
    // 检查控制器和特定切片的队列
    if (!controllerInfo || controllerInfo->sliceQueues.find(sliceNumber) == controllerInfo->sliceQueues.end()) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": Controller or task queue invalid for addWaitForScreenStillTask.");
        WaitForScreenStillResult res;
        res.success = false;
        res.errorMessage = "VM controller or slice queue not available.";
        promise->set_value(res);
        return future;
    }
    auto visionProcessor = this->getVisionProcessor(vmName);
    if (!visionProcessor) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": vision processor unavailable after getVisionProcessor for addWaitForScreenStillTask.");
        WaitForScreenStillResult res;
        res.success = false;
        res.errorMessage = "Vision processor not available after getVisionProcessor.";
        promise->set_value(res);
        return future;
    }

    auto task = std::make_shared<WaitForScreenStillTask>(vmName, sliceNumber, roi, checkIntervalSeconds, maxTimeoutSeconds, promise);
    controllerInfo->sliceQueues[sliceNumber]->taskQueue->push(task); // 放入指定切片队列
    
    // 通知轮询器有新任务
    {
        std::lock_guard<std::mutex> pollerLock(controllerInfo->pollerMutex_);
        controllerInfo->pollerCV_.notify_one();
    }
    
    controllerInfo->taskQueueCV_.notify_one();
    
    AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 等待画面静止任务已添加到队列");
    return future;
}

// 创建并添加等待视觉匹配任务到队列
std::future<WaitForVisualMatchResult> DispatchCenter::addWaitForVisualMatchTask(
    const std::string& vmName,
    int sliceNumber,
    const std::string& templateName,
    int checkIntervalSeconds,
    int maxTimeoutSeconds,
    double threshold)
{
    auto promise = std::make_shared<std::promise<WaitForVisualMatchResult>>();
    std::future<WaitForVisualMatchResult> future = promise->get_future();

    VMControllerInfo* controllerInfo = this->findVMController(vmName);
    // 检查控制器和特定切片的队列
    if (!controllerInfo || controllerInfo->sliceQueues.find(sliceNumber) == controllerInfo->sliceQueues.end()) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": Controller or task queue invalid for addWaitForVisualMatchTask.");
        WaitForVisualMatchResult res;
        res.success = false;
        res.errorMessage = "VM controller or slice queue not available.";
        promise->set_value(res);
        return future;
    }
    auto visionProcessor = this->getVisionProcessor(vmName);
    if (!visionProcessor) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": vision processor unavailable after getVisionProcessor for addWaitForVisualMatchTask.");
        WaitForVisualMatchResult res;
        res.success = false;
        res.errorMessage = "Vision processor not available after getVisionProcessor.";
        promise->set_value(res);
        return future;
    }

    auto task = std::make_shared<WaitForVisualMatchTask>(vmName, sliceNumber, templateName, checkIntervalSeconds, maxTimeoutSeconds, threshold, promise);
    controllerInfo->sliceQueues[sliceNumber]->taskQueue->push(task); // 放入指定切片队列
    
    // 通知轮询器有新任务
    {
        std::lock_guard<std::mutex> pollerLock(controllerInfo->pollerMutex_);
        controllerInfo->pollerCV_.notify_one();
    }
    
    controllerInfo->taskQueueCV_.notify_one();
    
    AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 等待视觉匹配任务已添加到队列，模板=" + templateName);
    return future;
}

// 标签定位任务 - 使用ROI识别
std::future<LabelLocateResult> DispatchCenter::addLabelLocateTask(
    const std::string& vmName,
    cv::Rect roi,
    int sliceNumber,
    const std::string& scriptContent)
{
    auto promise = std::make_shared<std::promise<LabelLocateResult>>();
    std::future<LabelLocateResult> future = promise->get_future();

    VMControllerInfo* controllerInfo = this->findVMController(vmName);
    // 检查控制器和特定切片的队列
    if (!controllerInfo || controllerInfo->sliceQueues.find(sliceNumber) == controllerInfo->sliceQueues.end()) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": Controller or task queue invalid for addLabelLocateTask.");
        LabelLocateResult res;
        res.success = false;
        res.errorMessage = "VM controller or slice queue not available.";
        promise->set_value(res);
        return future;
    }

    auto task = std::make_shared<LabelLocateTask>(vmName, roi, sliceNumber, scriptContent, promise);
    controllerInfo->sliceQueues[sliceNumber]->taskQueue->push(task); // 放入指定切片队列
    
    // 通知轮询器有新任务
    {
        std::lock_guard<std::mutex> pollerLock(controllerInfo->pollerMutex_);
        controllerInfo->pollerCV_.notify_one();
    }
    
    controllerInfo->taskQueueCV_.notify_one();
    
    AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 标签定位任务（ROI模式）已添加到队列");
    return future;
}

// 标签定位任务 - 直接文本匹配
std::future<LabelLocateResult> DispatchCenter::addLabelLocateTask(
    const std::string& vmName,
    const std::string& targetText,
    int sliceNumber,
    const std::string& scriptContent)
{
    auto promise = std::make_shared<std::promise<LabelLocateResult>>();
    std::future<LabelLocateResult> future = promise->get_future();

    VMControllerInfo* controllerInfo = this->findVMController(vmName);
    // 检查控制器和特定切片的队列
    if (!controllerInfo || controllerInfo->sliceQueues.find(sliceNumber) == controllerInfo->sliceQueues.end()) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": Controller or task queue invalid for addLabelLocateTask.");
        LabelLocateResult res;
        res.success = false;
        res.errorMessage = "VM controller or slice queue not available.";
        promise->set_value(res);
        return future;
    }

    auto task = std::make_shared<LabelLocateTask>(vmName, targetText, sliceNumber, scriptContent, promise);
    controllerInfo->sliceQueues[sliceNumber]->taskQueue->push(task); // 放入指定切片队列
    
    // 通知轮询器有新任务
    {
        std::lock_guard<std::mutex> pollerLock(controllerInfo->pollerMutex_);
        controllerInfo->pollerCV_.notify_one();
    }
    
    controllerInfo->taskQueueCV_.notify_one();
    
    AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 标签定位任务（文本模式）已添加到队列，目标文本=" + targetText);
    return future;
}




















// 支持停止信号的VM控制器线程函数（用于统一线程管理器）
void DispatchCenter::vmControllerThreadWithStopToken(VMControllerInfo* controllerInfo) {
    if (!controllerInfo) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] vmControllerThreadWithStopToken 开始空 controllerInfo.");
        return;
    }

    const std::string& vmName = controllerInfo->vmName;

    controllerInfo->currentState.store(VMThreadState::INITIALIZING);
    
    if (!controllerInfo->vncControl) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": VNCControl没有初始化");
        controllerInfo->currentState.store(VMThreadState::ERROR_CONNECTION_FAILED);
        controllerInfo->successfullyConnected.store(false);
        controllerInfo->initialized_promise.set_value(false);
        controllersCV_.notify_all();
        return;
    }

    // 尝试建立与虚拟机的 VNC 连接
    bool connectionSuccess = false;
    try {
        connectionSuccess = controllerInfo->vncControl->connectToServer();
        controllerInfo->vncClient = controllerInfo->vncControl->getRfbClient();
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": VNC连接异常: " + e.what());
        connectionSuccess = false;
    }

    auto clientPtr = controllerInfo->vncClient.lock();
    if (!connectionSuccess || !clientPtr) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": VNC连接失败");
        controllerInfo->currentState.store(VMThreadState::ERROR_CONNECTION_FAILED);
        controllerInfo->successfullyConnected.store(false);
        controllerInfo->initialized_promise.set_value(false);
        controllersCV_.notify_all();
        return;
    }

    // 连接成功，初始化视觉处理器
    controllerInfo->visionProcessor = std::make_unique<VMVision>(vmName, clientPtr);
    controllerInfo->successfullyConnected.store(true);
    controllerInfo->currentState.store(VMThreadState::RUNNING);
    

    // 🔧 设置VNC分辨率到ScreenFrameDistributor（使用虚拟机内部分辨率）
    if (controllerInfo->visionProcessor && controllerInfo->visionProcessor->getScreenFrameDistributor()) {
        controllerInfo->visionProcessor->getScreenFrameDistributor()->setVNCResolution(clientPtr->width, clientPtr->height);
    }
    
    // 更新VMStateManager中的连接状态
    auto* stateManager = VMStateManager::getInstance();
    if (stateManager) {
        stateManager->setVMConnectionState(vmName, VMConnectionState::CONNECTED_STATE);
    }
    
    controllerInfo->initialized_promise.set_value(true);
    controllersCV_.notify_all();

    AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 开始任务处理循环");

    // 主任务处理循环
    while (!controllerInfo->stopRequested.load()) {
        try {
            // 处理VNC消息
            auto vncClientPtr = controllerInfo->vncClient.lock();
            if (vncClientPtr) {
                try {
                    int timeout = 0;
                    int result = WaitForMessage(vncClientPtr.get(), timeout);
                    if (result > 0) {
                        if (!HandleRFBServerMessage(vncClientPtr.get())) {
                            AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": Error handling VNC server message.");
                        }
                    }
                } catch (const std::exception& e) {
                    AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": VNC消息处理异常: " + e.what());
                    break;
                }
            }

            // 等待轮询器推送任务
            std::shared_ptr<IVMTask> task = nullptr;
            int sliceNumber = -1;
            
            {
                std::unique_lock<std::mutex> execLock(controllerInfo->executionMutex_);
                controllerInfo->executionCV_.wait_for(execLock, std::chrono::milliseconds(100), 
                    [&]() { 
                        return controllerInfo->stopRequested.load() ||
                               controllerInfo->isExecutingTask.load(); 
                    });
                
                if (controllerInfo->stopRequested.load()) {
                    break;
                }
                
                if (controllerInfo->isExecutingTask.load()) {
                    task = controllerInfo->currentExecutingTask;
                    sliceNumber = controllerInfo->currentExecutingSlice;
                }
            }
            
            if (task) {
                // 执行任务
                try {
                    executeTask(task, sliceNumber, controllerInfo);
                    
                    // 如果是等待任务，清除等待状态
                    if (task->getType() == VMTaskType::WAIT_FOR_SCREEN_STILL || 
                        task->getType() == VMTaskType::WAIT_FOR_VISUAL_MATCH) {
                        
                        // 等待任务完成，更新状态到VMStateManager
                        auto* stateManager = VMStateManager::getInstance();
                        if (stateManager) {
                            if (task->getType() == VMTaskType::WAIT_FOR_SCREEN_STILL) {
                                stateManager->setSliceState(vmName, sliceNumber, SliceState::IDLE_STATE);
                            } else if (task->getType() == VMTaskType::WAIT_FOR_VISUAL_MATCH) {
                                stateManager->setSliceState(vmName, sliceNumber, SliceState::IDLE_STATE);
                            }
                        }
                    }
                    
                } catch (const std::exception& e) {
                    AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 任务执行异常: " + e.what());
                }
                
                // 任务执行完成，清除执行状态
                {
                    std::lock_guard<std::mutex> execLock(controllerInfo->executionMutex_);
                    controllerInfo->isExecutingTask = false;
                    controllerInfo->currentExecutingTask = nullptr;
                    controllerInfo->currentExecutingSlice = -1;
                }
                
                // 通知轮询器可以继续
                controllerInfo->executionCV_.notify_all();
            }
            
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 主循环异常: " + e.what());
        }
    }

    // 清理和断开连接
    controllerInfo->currentState.store(VMThreadState::STOPPED);
    if (controllerInfo->vncControl) {
        controllerInfo->vncControl->disconnect();
    }
    controllerInfo->successfullyConnected.store(false);

    // 删除：VM控制器线程结束日志
}

// 支持停止信号的任务轮询器线程函数（用于统一线程管理器）
void DispatchCenter::taskPollerThreadWithStopToken(VMControllerInfo* controllerInfo) {
    if (!controllerInfo) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] taskPollerThreadWithStopToken 开始空 controllerInfo.");
        return;
    }

    const std::string& vmName = controllerInfo->vmName;

    // 等待VM初始化完成
    auto future = controllerInfo->initialized_future;
    if (future.wait_for(std::chrono::seconds(30)) != std::future_status::ready || !future.get()) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": VM初始化失败，轮询器线程退出");
        return;
    }

    // 轮询器主循环
    while (!controllerInfo->pollerStopRequested.load()) {
        try {
            // 检查暂停状态
            if (controllerInfo->pollerPaused.load()) {
                std::unique_lock<std::mutex> pauseLock(controllerInfo->pollerMutex_);
                controllerInfo->pollerCV_.wait(pauseLock, [&]() {
                    return !controllerInfo->pollerPaused.load() || 
                           controllerInfo->pollerStopRequested.load();
                });
                continue;
            }

            // 检查是否有任务正在执行
            {
                std::unique_lock<std::mutex> execLock(controllerInfo->executionMutex_);
                if (controllerInfo->isExecutingTask.load()) {
                    // 等待任务执行完成
                    controllerInfo->executionCV_.wait_for(execLock, std::chrono::milliseconds(100), 
                        [&]() { 
                            return !controllerInfo->isExecutingTask.load() ||
                                   controllerInfo->pollerStopRequested.load(); 
                        });
                    continue;
                }
            }

            // 轮询所有切片，寻找可执行的任务
            bool foundTask = false;
            int totalSlices = controllerInfo->sliceOrder.size();
            
            for (int i = 0; i < totalSlices && !foundTask; ++i) {
                if (controllerInfo->pollerStopRequested.load()) {
                    break;
                }
                
                // 获取当前要检查的切片号
                int sliceNumber = controllerInfo->sliceOrder[controllerInfo->currentSliceIndex];
                
                // 检查切片是否存在且可执行
                auto sliceIt = controllerInfo->sliceQueues.find(sliceNumber);
                if (sliceIt == controllerInfo->sliceQueues.end() || !sliceIt->second) {
                    // 切片不存在或无效，跳到下一个
                    controllerInfo->currentSliceIndex = (controllerInfo->currentSliceIndex + 1) % totalSlices;
                    continue;
                }
                
                auto& sliceInfo = sliceIt->second;
                if (!sliceInfo->canExecuteTask(vmName, sliceNumber)) {
                    // 切片处于等待状态，跳过
                    controllerInfo->currentSliceIndex = (controllerInfo->currentSliceIndex + 1) % totalSlices;
                    continue;
                }
                
                // 尝试从切片队列获取任务
                auto optTask = sliceInfo->taskQueue->try_pop();
                if (optTask && *optTask) {
                    std::shared_ptr<IVMTask> task = *optTask;
                    
                    // 检查是否是等待任务，如果是则设置切片等待状态
                    VMTaskType taskType = task->getType();
                    auto* stateManager = VMStateManager::getInstance();
                    if (taskType == VMTaskType::WAIT_FOR_SCREEN_STILL) {
                        if (stateManager) {
                            stateManager->setSliceState(vmName, sliceNumber, SliceState::WAITING_SCREEN_STILL_STATE);
                        }
                    } else if (taskType == VMTaskType::WAIT_FOR_VISUAL_MATCH) {
                        if (stateManager) {
                            stateManager->setSliceState(vmName, sliceNumber, SliceState::WAITING_VISUAL_MATCH_STATE);
                        }
                    } else {
                        // 普通任务：设置为运行状态
                        if (stateManager) {
                            stateManager->setSliceState(vmName, sliceNumber, SliceState::RUNNING_STATE);
                        }
                    }
                    
                    // 将任务推送给执行器
                    {
                        std::lock_guard<std::mutex> execLock(controllerInfo->executionMutex_);
                        controllerInfo->currentExecutingTask = task;
                        controllerInfo->currentExecutingSlice = sliceNumber;
                        controllerInfo->isExecutingTask = true;
                    }
                    
                    // 通知执行器
                    controllerInfo->executionCV_.notify_one();
                    foundTask = true;
                }
                
                // 移动到下一个切片
                controllerInfo->currentSliceIndex = (controllerInfo->currentSliceIndex + 1) % totalSlices;
            }
            
            if (!foundTask) {
                // 没有找到任务，休眠更长时间以降低CPU占用
                // 性能优化：从50ms增加到300ms，降低轮询频率约85%，大幅减少CPU占用
                static const int POLLER_IDLE_SLEEP_MS = 300;  // 轮询器空闲时的休眠时间
                std::this_thread::sleep_for(std::chrono::milliseconds(POLLER_IDLE_SLEEP_MS));
            }
            
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 轮询器异常: " + e.what());
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    // 原子性检查，确保结束日志只打印一次
    bool expected = false;
    if (controllerInfo->pollerEndLogged.compare_exchange_strong(expected, true)) {
        // 删除：任务轮询器线程结束日志
    }
}

// 检测画面是否静止 - 通过比较固定区域的文字识别结果
void DispatchCenter::waitForScreenStill(
    const std::string& vmName,
    int sliceNumber,
    cv::Rect roi,
    int checkIntervalSeconds,
    int maxTimeoutSeconds
) {
    AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 开始检测画面静止状态，ROI=(" + 
               std::to_string(roi.x) + "," + std::to_string(roi.y) + "," + 
               std::to_string(roi.width) + "," + std::to_string(roi.height) + 
               "), 切片=" + std::to_string(sliceNumber) + 
               ", 检测间隔=" + std::to_string(checkIntervalSeconds) + "秒");

    auto startTime = std::chrono::steady_clock::now();
    auto maxDuration = std::chrono::seconds(maxTimeoutSeconds);
    
    std::string previousText = "";
    bool firstCheck = true;
    int consecutiveMatchCount = 0;
    const int requiredConsecutiveMatches = 2; // 需要连续2次相同才认为静止

    while (true) {
        auto currentTime = std::chrono::steady_clock::now();
        if (currentTime - startTime > maxDuration) {
            AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 画面静止检测超时 (" + 
                       std::to_string(maxTimeoutSeconds) + "秒)");
            break;
        }

        try {
            // 创建promise和future用于文字识别任务
            auto promise = std::make_shared<std::promise<TextRecognizeResult>>();
            auto future = promise->get_future();
            
            // 创建文字识别任务
            auto textRecognizeTask = std::make_shared<TextRecognizeVisionTask>(vmName, roi, sliceNumber, promise);

            // 获取VM控制器信息
            VMControllerInfo* controllerInfo = getVMControllerInfo(vmName);
            if (!controllerInfo) {
                AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 无法获取VM控制器信息");
                break;
            }

            // 确保视觉处理器已初始化
            auto visionClientPtr = controllerInfo->vncClient.lock();
            if (!controllerInfo->visionProcessor && visionClientPtr) {
                controllerInfo->visionProcessor = std::make_unique<VMVision>(vmName, visionClientPtr);
                // VMVision现在自己管理线程池，不需要外部设置
            }

            if (!controllerInfo->visionProcessor) {
                AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 视觉处理器初始化失败");
                break;
            }

            // 执行文字识别
            processTextRecognizeTask(vmName, controllerInfo, textRecognizeTask.get());

            // 等待识别结果
            auto status = future.wait_for(std::chrono::seconds(10));
            if (status != std::future_status::ready) {
                AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 文字识别超时，继续下次检测");
                std::this_thread::sleep_for(std::chrono::seconds(checkIntervalSeconds));
                continue;
            }

            auto result = future.get();
            if (!result.success) {
                AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 文字识别失败: " + 
                           result.errorMessage + "，继续下次检测");
                std::this_thread::sleep_for(std::chrono::seconds(checkIntervalSeconds));
                continue;
            }

            // 将所有识别到的文本合并为一个字符串进行比较
            std::string currentText = "";
            for (const auto& text : result.recognizedTexts) {
                currentText += text;
            }
            
            // 清理文本，移除空白字符和换行符，便于比较
            currentText.erase(std::remove_if(currentText.begin(), currentText.end(), 
                                           [](char c) { return std::isspace(c); }), currentText.end());

            AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 识别到文字: \"" + currentText + "\"");

            if (firstCheck) {
                // 第一次检测，记录基准文本
                previousText = currentText;
                firstCheck = false;
                consecutiveMatchCount = 1;
                AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 设置基准文字: \"" + previousText + "\"");
            } else {
                // 比较当前文本与上次文本
                if (currentText == previousText) {
                    consecutiveMatchCount++;
                    AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 文字相同，连续匹配次数: " + 
                               std::to_string(consecutiveMatchCount) + "/" + std::to_string(requiredConsecutiveMatches));
                    
                    if (consecutiveMatchCount >= requiredConsecutiveMatches) {
                        AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 画面已静止，连续 " + 
                                   std::to_string(consecutiveMatchCount) + " 次检测结果相同");
                        break;
                    }
                } else {
                    // 文字发生变化，重置计数器
                    AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 文字发生变化，从 \"" + 
                               previousText + "\" 变为 \"" + currentText + "\"，重置计数器");
                    previousText = currentText;
                    consecutiveMatchCount = 1;
                }
            }

        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 画面静止检测时发生异常: " + 
                       std::string(e.what()));
            // 异常时等待一段时间后继续
        } catch (...) {
            AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 画面静止检测时发生未知异常");
        }

        // 等待指定时间间隔后进行下次检测
        std::this_thread::sleep_for(std::chrono::seconds(checkIntervalSeconds));
    }

    AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 画面静止检测完成");
}

// ==========================================================================================
// ============================= 新增调度和脚本处理核心代码 =============================
// ==========================================================================================

// 新增公有函数：从字符串加载并运行脚本 - 旧实现已删除
void DispatchCenter::executeScript(const std::string& vmName, const std::string& scriptContent) {
    // 直接调用common.cpp中的实现
    ::loadAndRunScriptFromString(vmName, scriptContent);
}



// 新增辅助函数：执行具体的任务
void DispatchCenter::executeTask(std::shared_ptr<IVMTask> task, int sliceNumber, VMControllerInfo* controllerInfo) {
    const std::string& vmName = controllerInfo->vmName;
    
    if (!task) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 任务指针为空");
        return;
    }
    
    try {
        VMTaskType taskType = task->getType();
        
        if (taskType == VMTaskType::WAIT_FOR_SCREEN_STILL) {
            // 对于 "等待画面静止" 任务，进行同步阻塞处理
            auto* waitTask = dynamic_cast<WaitForScreenStillTask*>(task.get());
            if (!waitTask) {
                AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 等待画面静止任务转换失败");
                return;
            }
            processWaitForScreenStillTask(vmName, controllerInfo, waitTask);

        } else if (taskType == VMTaskType::WAIT_FOR_VISUAL_MATCH) {
            // 对于 "等待视觉匹配" 任务，进行同步阻塞处理
            auto* waitTask = dynamic_cast<WaitForVisualMatchTask*>(task.get());
            if (!waitTask) {
                AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 等待视觉匹配任务转换失败");
                return;
            }
            processWaitForVisualMatchTask(vmName, controllerInfo, waitTask);

        } else {
            // 对于所有其他普通任务，像以前一样同步执行
            // 确保视觉处理器已初始化
            auto taskClientPtr = controllerInfo->vncClient.lock();
            if (!controllerInfo->visionProcessor && taskClientPtr) {
                controllerInfo->visionProcessor = std::make_unique<VMVision>(vmName, taskClientPtr);
                // VMVision现在自己管理线程池，不需要外部设置
            }

            switch (taskType) {
                case VMTaskType::KEYBOARD_MOUSE_ACTION: {
                    auto* keyboardTask = dynamic_cast<DirectKeyboardMouseActionTask*>(task.get());
                    if (!keyboardTask) {
                        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 直接键盘鼠标任务转换失败");
                        return;
                    }
                    processDirectKeyboardMouseTask(vmName, controllerInfo, keyboardTask);
                    break;
                }

                case VMTaskType::IMAGE_MATCH_VISION: {
                    if (!controllerInfo->visionProcessor) {
                        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 视觉处理器不可用");
                        return;
                    }
                    auto* imageTask = dynamic_cast<ImageMatchVisionTask*>(task.get());
                    if (!imageTask) {
                        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 图像匹配任务转换失败");
                        return;
                    }
                    processImageMatchTask(vmName, controllerInfo, imageTask);
                    break;
                }
                case VMTaskType::WORDS_MATCH_VISION: {
                    if (!controllerInfo->visionProcessor) {
                        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 视觉处理器不可用");
                        return;
                    }
                    auto* wordsTask = dynamic_cast<WordsMatchVisionTask*>(task.get());
                    if (!wordsTask) {
                        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 文字匹配任务转换失败");
                        return;
                    }
                    processWordsMatchTask(vmName, controllerInfo, wordsTask);
                    break;
                }
                case VMTaskType::TEXT_RECOGNIZE_VISION: {
                    if (!controllerInfo->visionProcessor) {
                        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 视觉处理器不可用");
                        return;
                    }
                    auto* textTask = dynamic_cast<TextRecognizeVisionTask*>(task.get());
                    if (!textTask) {
                        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 文字识别任务转换失败");
                        return;
                    }
                    processTextRecognizeTask(vmName, controllerInfo, textTask);
                    break;
                }
                case VMTaskType::LABEL_LOCATE: {
                    auto* labelTask = dynamic_cast<LabelLocateTask*>(task.get());
                    if (!labelTask) {
                        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 标签定位任务转换失败");
                        return;
                    }
                    processLabelLocateTask(vmName, controllerInfo, labelTask);
                    break;
                }
                // 注意：WAIT_FOR_SCREEN_STILL 已在上面处理，这里不会进入
                default:
                    AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 未知任务类型: " + std::to_string(static_cast<int>(taskType)));
                    break;
            }
        }
        
        // 删除冗余的切片任务执行完成日志
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 处理切片 " + std::to_string(sliceNumber) + " 任务时发生异常: " + std::string(e.what()));
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 处理切片 " + std::to_string(sliceNumber) + " 任务时发生未知异常");
    }
}

// ==========================================================================================
// ================================ 切片配置管理 ======================================
// ==========================================================================================

// 设置虚拟机的切片配置
void DispatchCenter::setVMSliceConfiguration(const std::string& vmName, int rows, int cols) {
    // 使用 ScreenFrameDistributor 的验证函数
    if (!ScreenFrameDistributor::isValidSliceConfiguration(rows, cols)) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": " + 
                   ScreenFrameDistributor::getSliceConfigurationDescription(rows, cols));
        return;
    }
    
    std::lock_guard<std::mutex> lock(controllersMutex_);
    VMControllerInfo* controllerInfo = findVMController_unlocked(vmName);
    
    if (!controllerInfo) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 找不到虚拟机控制器，无法设置切片配置");
        return;
    }
    
    // 清空现有任务（避免冲突）
    for (auto& slicePair : controllerInfo->sliceQueues) {
        if (slicePair.second && slicePair.second->taskQueue) {
            slicePair.second->taskQueue->clear();
        }
    }
    
    // 重新初始化切片配置
    controllerInfo->initializeSlices(rows, cols);
    
    // 更新VMVision中的ScreenFrameDistributor
    if (controllerInfo->visionProcessor) {
        controllerInfo->visionProcessor->setSliceConfiguration(rows, cols);
        AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": VMVision切片配置已同步更新");
    }
    
    AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 切片配置已设置为 " + 
               ScreenFrameDistributor::getSliceConfigurationDescription(rows, cols));
}

// 获取虚拟机的切片配置
std::pair<int, int> DispatchCenter::getVMSliceConfiguration(const std::string& vmName) const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(controllersMutex_));
    const VMControllerInfo* controllerInfo = findVMController_unlocked(vmName);
    
    if (!controllerInfo) {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName + ": 找不到虚拟机控制器，返回默认切片配置");
        return std::make_pair(1, 2); // 返回默认配置
    }
    
    return std::make_pair(controllerInfo->sliceRows, controllerInfo->sliceCols);
}

// 标签定位任务处理
void DispatchCenter::processLabelLocateTask(const std::string& vmName, VMControllerInfo* controllerInfo, LabelLocateTask* task) {
    if (!task) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": Invalid LabelLocateTask.");
        return;
    }

    auto promise = task->getPromise();
    if (!promise) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": LabelLocateTask promise is null.");
        return;
    }

    LabelLocateResult result;
    result.success = false;

    try {
        std::string targetText;
        
        // 根据任务类型获取目标文本
        if (task->isUseRoi()) {
            // 使用OCR识别ROI区域的文本
            if (!controllerInfo->visionProcessor) {
                result.errorMessage = "Vision processor not available for OCR.";
                promise->set_value(result);
                return;
            }

            cv::Rect roi = task->getRoi();
            int sliceNumber = task->getSliceNumber();

            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 开始ROI文本识别，区域: (" + 
                       std::to_string(roi.x) + "," + std::to_string(roi.y) + "," + 
                       std::to_string(roi.width) + "," + std::to_string(roi.height) + ")");

            // 创建文本识别任务
            auto ocrPromise = std::make_shared<std::promise<TextRecognizeResult>>();
            auto ocrFuture = ocrPromise->get_future();
            
            auto ocrTask = std::make_shared<TextRecognizeVisionTask>(vmName, roi, sliceNumber, ocrPromise);
            processTextRecognizeTask(vmName, controllerInfo, ocrTask.get());

            // 等待OCR结果
            auto ocrResult = ocrFuture.get();
            if (!ocrResult.success) {
                result.errorMessage = "OCR识别失败: " + ocrResult.errorMessage;
                promise->set_value(result);
                return;
            }

            if (ocrResult.recognizedTexts.empty()) {
                result.errorMessage = "OCR未识别到任何文本";
                promise->set_value(result);
                return;
            }

            // 使用第一个识别到的文本作为目标
            targetText = ocrResult.recognizedTexts[0];
            result.recognizedText = targetText;

            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": OCR识别到文本: \"" + targetText + "\"");
        } else {
            // 直接使用提供的文本
            targetText = task->getTargetText();
            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 使用直接提供的文本: \"" + targetText + "\"");
        }

        // 在脚本内容中搜索标签
        std::string scriptContent = task->getScriptContent();
        std::string labelPattern = "LABEL:" + targetText;
        
        AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 搜索标签: \"" + labelPattern + "\"");

        std::stringstream ss(scriptContent);
        std::string line;
        int lineNumber = 0;
        bool found = false;

        while (std::getline(ss, line)) {
            lineNumber++;
            
            // 去除行首和行尾的空白字符
            line.erase(0, line.find_first_not_of(" \t\n\r"));
            line.erase(line.find_last_not_of(" \t\n\r") + 1);

            // 检查是否是目标标签（支持#LABEL:和LABEL:两种格式）
            if (line.find("LABEL:" + targetText) != std::string::npos || 
                line.find("#LABEL:" + targetText) != std::string::npos) {
                found = true;
                result.labelFound = true;
                result.foundLabelName = targetText;
                result.jumpToLine = lineNumber;
                result.success = true;
                result.errorMessage = "成功找到标签 \"" + targetText + "\" 在第 " + std::to_string(lineNumber) + " 行";
                
                AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 找到标签 \"" + targetText + "\" 在第 " + std::to_string(lineNumber) + " 行");
                break;
            }
        }

        if (!found) {
            result.success = true; // 操作成功执行，只是没找到标签
            result.labelFound = false;
            result.errorMessage = "未找到标签 \"" + targetText + "\"";
            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 未找到标签 \"" + targetText + "\"");
        }

    } catch (const std::exception& e) {
        result.success = false;
        result.errorMessage = "标签定位过程中发生异常: " + std::string(e.what());
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 标签定位异常: " + std::string(e.what()));
    } catch (...) {
        result.success = false;
        result.errorMessage = "标签定位过程中发生未知异常";
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 标签定位未知异常");
    }

    promise->set_value(result);
}

// 新增：停止任务轮询器
void DispatchCenter::stopTaskPoller(const std::string& vmName) {
    std::lock_guard<std::mutex> lock(controllersMutex_);
    VMControllerInfo* controllerInfo = findVMController_unlocked(vmName);
    if (controllerInfo) {
        // 使用原子操作检查是否已经停止，避免重复停止
        bool expected = false;
        if (controllerInfo->pollerStopRequested.compare_exchange_strong(expected, true)) {
            // 第一次设置停止标志，执行停止操作
            // 重置结束日志标志，为下次启动做准备
            controllerInfo->pollerEndLogged.store(false);
            controllerInfo->pollerCV_.notify_all();
            // 删除：任务轮询器停止请求已发送日志
        } else {
            // 已经设置过停止标志，避免重复操作
            AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 任务轮询器已处于停止状态");
        }
    } else {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] stopTaskPoller: 找不到虚拟机: " + vmName);
    }
}

// 新增：重新启动任务轮询器
void DispatchCenter::startTaskPoller(const std::string& vmName) {
    std::lock_guard<std::mutex> lock(controllersMutex_);
    VMControllerInfo* controllerInfo = findVMController_unlocked(vmName);
    if (controllerInfo) {
        // 检查轮询器是否已经停止
        if (controllerInfo->pollerStopRequested.load()) {
            // 重置停止标志，重新启动轮询器
            controllerInfo->pollerStopRequested.store(false);
            controllerInfo->pollerPaused.store(false);
            controllerInfo->pollerEndLogged.store(false);
            
            // 尝试停止现有线程（如果还在运行）
            std::string pollerThreadName = "TaskPoller_" + vmName;
            threadManager_.stopNamedThread(pollerThreadName);
            
            // 重新创建轮询器线程
            std::string pollerResult = threadManager_.createNamedThread(pollerThreadName,
                [this, controllerInfo]() {
                    taskPollerThreadWithStopToken(controllerInfo);
                });
            
            if (!pollerResult.empty()) {
                AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 任务轮询器重新启动成功");
            } else {
                AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName + ": 任务轮询器重新启动失败");
            }
        } else {
            // 轮询器还在运行，确保不处于暂停状态
            controllerInfo->pollerPaused.store(false);
            controllerInfo->pollerCV_.notify_all();
            AddLogInfo(LogLevel::Debug, "[DispatchCenter] " + vmName + ": 任务轮询器已在运行状态，已唤醒");
        }
    } else {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] startTaskPoller: 找不到虚拟机: " + vmName);
    }
}

// 新增：清除所有切片的等待状态
void DispatchCenter::clearSliceWaitingStates(const std::string& vmName) {
    std::lock_guard<std::mutex> lock(controllersMutex_);
    VMControllerInfo* controllerInfo = findVMController_unlocked(vmName);
    if (controllerInfo) {
        int clearedStates = 0;
        auto* stateManager = VMStateManager::getInstance();
        
        for (auto& slicePair : controllerInfo->sliceQueues) {
            if (slicePair.second && stateManager) {
                int sliceNumber = slicePair.first;
                bool wasWaiting = stateManager->isSliceWaiting(vmName, sliceNumber);
                if (wasWaiting) {
                    stateManager->setSliceState(vmName, sliceNumber, SliceState::IDLE_STATE);
                    clearedStates++;
                    AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 清除切片 " + 
                              std::to_string(sliceNumber) + " 的等待状态");
                }
            }
        }
        
        if (clearedStates > 0) {
            // 通知轮询器可以继续轮询这些切片
            controllerInfo->pollerCV_.notify_one();
            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 共清除了 " + 
                      std::to_string(clearedStates) + " 个切片的等待状态");
        }
    } else {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] clearSliceWaitingStates: 找不到虚拟机: " + vmName);
    }
}

// 新增：等待任务完成通知
void DispatchCenter::notifySliceWaitComplete(const std::string& vmName, int sliceNumber, VMTaskType taskType) {
    std::lock_guard<std::mutex> lock(controllersMutex_);
    VMControllerInfo* controllerInfo = findVMController_unlocked(vmName);
    if (controllerInfo) {
        auto* stateManager = VMStateManager::getInstance();
        if (stateManager) {
            bool stateChanged = false;
            
            if (taskType == VMTaskType::WAIT_FOR_SCREEN_STILL && 
                stateManager->isSliceWaitingForScreenStill(vmName, sliceNumber)) {
                stateManager->setSliceState(vmName, sliceNumber, SliceState::IDLE_STATE);
                stateChanged = true;
                AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 切片 " + 
                          std::to_string(sliceNumber) + " 画面静止等待完成");
            } else if (taskType == VMTaskType::WAIT_FOR_VISUAL_MATCH && 
                      stateManager->isSliceWaitingForVisualMatch(vmName, sliceNumber)) {
                stateManager->setSliceState(vmName, sliceNumber, SliceState::IDLE_STATE);
                stateChanged = true;
                AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 切片 " + 
                          std::to_string(sliceNumber) + " 视觉匹配等待完成");
            }
            
            if (stateChanged) {
                // 通知轮询器可以继续轮询该切片
                controllerInfo->pollerCV_.notify_one();
            }
        }
    } else {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] notifySliceWaitComplete: 找不到虚拟机: " + vmName);
    }
}

// 新增：检查切片是否在等待
bool DispatchCenter::isSliceWaiting(const std::string& vmName, int sliceNumber) const {
    auto* stateManager = VMStateManager::getInstance();
    return stateManager ? stateManager->isSliceWaiting(vmName, sliceNumber) : false;
}

// 新增：获取切片状态的方法
bool DispatchCenter::isSlicePaused(const std::string& vmName, int sliceNumber) const {
    auto* stateManager = VMStateManager::getInstance();
    return stateManager ? stateManager->isSlicePaused(vmName, sliceNumber) : false;
}

bool DispatchCenter::isSliceRunning(const std::string& vmName, int sliceNumber) const {
    auto* stateManager = VMStateManager::getInstance();
    if (!stateManager) return false;
    
    // 虚拟机本身必须处于连接状态
    if (!stateManager->isVMConnected(vmName)) {
        return false;
    }
    
    // 检查切片是否处于运行状态
    return stateManager->isSliceRunning(vmName, sliceNumber);
}

bool DispatchCenter::isSliceWaitingForScreenStill(const std::string& vmName, int sliceNumber) const {
    auto* stateManager = VMStateManager::getInstance();
    return stateManager ? stateManager->isSliceWaitingForScreenStill(vmName, sliceNumber) : false;
}

bool DispatchCenter::isSliceWaitingForVisualMatch(const std::string& vmName, int sliceNumber) const {
    auto* stateManager = VMStateManager::getInstance();
    return stateManager ? stateManager->isSliceWaitingForVisualMatch(vmName, sliceNumber) : false;
}

// 暂停所有切片
void DispatchCenter::pauseAllSlices(const std::string& vmName) {
    std::lock_guard<std::mutex> lock(controllersMutex_);
    VMControllerInfo* controllerInfo = findVMController_unlocked(vmName);
    if (controllerInfo) {
        int pausedCount = 0;
        auto* stateManager = VMStateManager::getInstance();
        
        for (auto& slicePair : controllerInfo->sliceQueues) {
            int sliceNumber = slicePair.first;
            if (stateManager && !stateManager->isSlicePaused(vmName, sliceNumber)) {
                stateManager->setSliceState(vmName, sliceNumber, SliceState::PAUSED_STATE);
                pausedCount++;
                AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 切片 " + std::to_string(sliceNumber) + " 已暂停");
            }
        }
        
        if (pausedCount > 0) {
            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 总共暂停了 " + std::to_string(pausedCount) + " 个切片");
        } else {
            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 所有切片都已处于暂停状态");
        }
    } else {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] 尝试暂停不存在的虚拟机的切片: " + vmName);
    }
}

// 新增：将失败的任务重新加入到队列头部
void DispatchCenter::reEnqueueFailedTask(const std::string& vmName, std::shared_ptr<IVMTask> task) {
    if (!task) {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] reEnqueueFailedTask: 任务为空");
        return;
    }

    std::lock_guard<std::mutex> lock(controllersMutex_);
    VMControllerInfo* controllerInfo = findVMController_unlocked(vmName);
    if (!controllerInfo) {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] reEnqueueFailedTask: 找不到虚拟机: " + vmName);
        return;
    }

    int sliceNumber = task->getSliceNumber();
    auto sliceIt = controllerInfo->sliceQueues.find(sliceNumber);
    if (sliceIt == controllerInfo->sliceQueues.end() || !sliceIt->second || !sliceIt->second->taskQueue) {
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] reEnqueueFailedTask: 找不到切片 " + std::to_string(sliceNumber) + " 的队列");
        return;
    }

    // 将任务重新加入到队列头部
    try {
        // 直接使用原任务重新入队（清除promise以避免重复设置）
        task->clearPromise(); // 需要实现clearPromise方法
        sliceIt->second->taskQueue->push_front(task);
        AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName + ": 失败任务已重新加入切片 " +
                  std::to_string(sliceNumber) + " 队列头部，类型: " + std::to_string(static_cast<int>(task->getType())));
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] reEnqueueFailedTask: 重新入队失败: " + std::string(e.what()));
    }
}

