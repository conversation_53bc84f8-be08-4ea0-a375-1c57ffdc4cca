#pragma once

#include <string>
#include <map>
#include <any>
#include <memory>
#include <mutex>
#include <functional>
#include <filesystem>
#include <chrono>
#include <vector>
#include <nlohmann/json.hpp>

/**
 * 配置项类型枚举
 */
enum class ConfigType {
    SYSTEM,        // 系统配置
    VM,            // 虚拟机配置
    VISION,        // 视觉配置
    SCRIPT,        // 脚本配置
    NETWORK,       // 网络配置
    PERFORMANCE    // 性能配置
};

/**
 * 配置项结构
 */
struct ConfigItem {
    std::string key;
    std::any value;
    ConfigType type;
    std::string description;
    bool isRequired = false;
    std::chrono::system_clock::time_point lastModified;
    
    template<typename T>
    T getValue() const {
        try {
            return std::any_cast<T>(value);
        } catch (const std::bad_any_cast& e) {
            throw std::runtime_error("配置项类型转换失败: " + key);
        }
    }
    
    template<typename T>
    void setValue(const T& val) {
        value = val;
        lastModified = std::chrono::system_clock::now();
    }
};

/**
 * 配置验证器接口
 */
class IConfigValidator {
public:
    virtual ~IConfigValidator() = default;
    virtual bool validate(const ConfigItem& item, std::string& errorMsg) const = 0;
    virtual std::string getValidatorName() const = 0;
};

/**
 * 配置变更监听器接口
 */
class IConfigChangeListener {
public:
    virtual ~IConfigChangeListener() = default;
    virtual void onConfigChanged(const std::string& key, const ConfigItem& oldItem, const ConfigItem& newItem) = 0;
    virtual void onConfigAdded(const std::string& key, const ConfigItem& item) = 0;
    virtual void onConfigRemoved(const std::string& key, const ConfigItem& item) = 0;
};

/**
 * 统一配置管理器
 * 
 * 功能特性：
 * 1. 支持多种配置文件格式（JSON）
 * 2. 支持配置热重载
 * 3. 支持配置验证
 * 4. 支持配置变更监听
 * 5. 支持配置缓存和优化
 * 6. 线程安全
 */
class ConfigManager {
public:
    /**
     * 获取单例实例
     */
    static ConfigManager& getInstance();
    
    /**
     * 初始化配置管理器
     * @param configDir 配置文件目录
     * @param enableHotReload 是否启用热重载
     */
    bool initialize(const std::string& configDir = "./config", bool enableHotReload = true);
    
    /**
     * 关闭配置管理器
     */
    void shutdown();
    
    // ==================== 配置加载和保存 ====================
    
    /**
     * 从文件加载配置
     * @param configFile 配置文件路径
     * @param type 配置类型
     */
    bool loadFromFile(const std::string& configFile, ConfigType type);
    
    /**
     * 保存配置到文件
     * @param configFile 配置文件路径
     * @param type 配置类型（可选，为空则保存所有类型）
     */
    bool saveToFile(const std::string& configFile, ConfigType type = ConfigType::SYSTEM);
    
    /**
     * 重新加载所有配置文件
     */
    bool reloadAll();
    
    // ==================== 配置访问 ====================
    
    /**
     * 获取配置值
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    template<typename T>
    T getConfig(const std::string& key, const T& defaultValue = T{}) const;
    
    /**
     * 设置配置值
     * @param key 配置键
     * @param value 配置值
     * @param type 配置类型
     * @param description 配置描述
     * @param isRequired 是否必需
     */
    template<typename T>
    void setConfig(const std::string& key, const T& value, ConfigType type = ConfigType::SYSTEM, 
                   const std::string& description = "", bool isRequired = false);
    
    /**
     * 检查配置是否存在
     */
    bool hasConfig(const std::string& key) const;
    
    /**
     * 移除配置
     */
    bool removeConfig(const std::string& key);
    
    /**
     * 获取指定类型的所有配置
     */
    std::map<std::string, ConfigItem> getConfigsByType(ConfigType type) const;
    
    // ==================== 配置验证 ====================
    
    /**
     * 添加配置验证器
     */
    void addValidator(const std::string& key, std::shared_ptr<IConfigValidator> validator);
    
    /**
     * 验证所有配置
     */
    bool validateAll(std::vector<std::string>& errorMessages) const;
    
    /**
     * 验证指定配置
     */
    bool validateConfig(const std::string& key, std::string& errorMsg) const;
    
    // ==================== 监听器管理 ====================
    
    /**
     * 添加配置变更监听器
     */
    void addChangeListener(std::shared_ptr<IConfigChangeListener> listener);
    
    /**
     * 移除配置变更监听器
     */
    void removeChangeListener(std::shared_ptr<IConfigChangeListener> listener);
    
    // ==================== 实用方法 ====================
    
    /**
     * 获取配置目录
     */
    std::string getConfigDirectory() const { return configDirectory_; }
    
    /**
     * 获取配置统计信息
     */
    struct ConfigStats {
        size_t totalConfigs = 0;
        size_t configsByType[6] = {0}; // 对应ConfigType枚举
        std::chrono::system_clock::time_point lastReloadTime;
        size_t reloadCount = 0;
        size_t validationErrors = 0;
    };
    ConfigStats getStats() const;
    
    /**
     * 导出配置为JSON字符串（用于调试）
     */
    std::string exportToJson(ConfigType type = ConfigType::SYSTEM) const;

private:
    // 单例相关
    ConfigManager() = default;
    ~ConfigManager() = default;
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;
    
    // 内部方法
    void startHotReloadWatcher();
    void stopHotReloadWatcher();
    void checkFileChanges();
    void notifyListeners(const std::string& key, const ConfigItem* oldItem, const ConfigItem* newItem);
    nlohmann::json configItemToJson(const ConfigItem& item) const;
    ConfigItem jsonToConfigItem(const std::string& key, const nlohmann::json& json, ConfigType type) const;
    ConfigType getConfigTypeFromCategory(const std::string& category) const;
    void loadDefaultConfigs();
    
    // 数据成员
    mutable std::mutex mutex_;
    std::map<std::string, ConfigItem> configs_;
    std::map<std::string, std::shared_ptr<IConfigValidator>> validators_;
    std::vector<std::weak_ptr<IConfigChangeListener>> listeners_;
    
    std::string configDirectory_;
    bool hotReloadEnabled_ = false;
    std::map<std::string, std::filesystem::file_time_type> fileTimestamps_;
    
    // 统计信息
    mutable ConfigStats stats_;
    
    // 热重载线程
    std::unique_ptr<std::thread> hotReloadThread_;
    std::atomic<bool> hotReloadRunning_{false};
};

// ==================== 模板实现 ====================

template<typename T>
T ConfigManager::getConfig(const std::string& key, const T& defaultValue) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = configs_.find(key);
    if (it == configs_.end()) {
        return defaultValue;
    }
    
    try {
        return it->second.getValue<T>();
    } catch (const std::exception&) {
        return defaultValue;
    }
}

template<typename T>
void ConfigManager::setConfig(const std::string& key, const T& value, ConfigType type, 
                             const std::string& description, bool isRequired) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    ConfigItem* oldItem = nullptr;
    auto it = configs_.find(key);
    if (it != configs_.end()) {
        oldItem = &it->second;
    }
    
    ConfigItem newItem;
    newItem.key = key;
    newItem.setValue(value);
    newItem.type = type;
    newItem.description = description;
    newItem.isRequired = isRequired;
    
    configs_[key] = newItem;
    
    // 通知监听器
    notifyListeners(key, oldItem, &newItem);
}

// ==================== 预定义验证器 ====================

/**
 * 范围验证器（用于数值类型）
 */
template<typename T>
class RangeValidator : public IConfigValidator {
public:
    RangeValidator(T minVal, T maxVal) : minValue_(minVal), maxValue_(maxVal) {}
    
    bool validate(const ConfigItem& item, std::string& errorMsg) const override {
        try {
            T value = item.getValue<T>();
            if (value < minValue_ || value > maxValue_) {
                errorMsg = "值 " + std::to_string(value) + " 超出范围 [" + 
                          std::to_string(minValue_) + ", " + std::to_string(maxValue_) + "]";
                return false;
            }
            return true;
        } catch (const std::exception& e) {
            errorMsg = "类型转换失败: " + std::string(e.what());
            return false;
        }
    }
    
    std::string getValidatorName() const override {
        return "RangeValidator";
    }
    
private:
    T minValue_, maxValue_;
};

/**
 * 文件存在验证器
 */
class FileExistsValidator : public IConfigValidator {
public:
    bool validate(const ConfigItem& item, std::string& errorMsg) const override {
        try {
            std::string filePath = item.getValue<std::string>();
            if (!std::filesystem::exists(filePath)) {
                errorMsg = "文件不存在: " + filePath;
                return false;
            }
            return true;
        } catch (const std::exception& e) {
            errorMsg = "验证文件存在性时出错: " + std::string(e.what());
            return false;
        }
    }
    
    std::string getValidatorName() const override {
        return "FileExistsValidator";
    }
}; 