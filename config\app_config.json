{"system": {"system.config_dir": {"value": "./config", "type": "string", "description": "配置文件目录", "required": true}, "system.assets_dir": {"value": "./assets", "type": "string", "description": "资源文件目录", "required": true}, "system.script_dir": {"value": "./script", "type": "string", "description": "脚本文件目录", "required": true}, "system.log_level": {"value": "Info", "type": "string", "description": "日志级别", "required": true}, "system.window_title": {"value": "游戏脚本自动化工具", "type": "string", "description": "应用程序窗口标题", "required": false}, "system.main_window_width": {"value": 1280, "type": "int", "description": "主窗口宽度", "required": false}, "system.main_window_height": {"value": 800, "type": "int", "description": "主窗口高度", "required": false}, "system.enable_hot_reload": {"value": true, "type": "bool", "description": "是否启用配置热重载", "required": false}}, "network": {"network.vnc_timeout": {"value": 5000, "type": "int", "description": "VNC连接超时时间(毫秒)", "required": true}, "network.connection_retry_count": {"value": 3, "type": "int", "description": "连接重试次数", "required": true}, "network.retry_delay": {"value": 1000, "type": "int", "description": "重试延迟时间(毫秒)", "required": true}, "network.keep_alive_interval": {"value": 30000, "type": "int", "description": "保活间隔时间(毫秒)", "required": false}, "network.max_concurrent_connections": {"value": 10, "type": "int", "description": "最大并发连接数", "required": false}, "network.buffer_size": {"value": 8192, "type": "int", "description": "网络缓冲区大小", "required": false}, "network.enable_compression": {"value": true, "type": "bool", "description": "是否启用数据压缩", "required": false}}, "performance": {"performance.thread_pool_size": {"value": 8, "type": "int", "description": "线程池大小", "required": true}, "performance.max_worker_threads": {"value": 16, "type": "int", "description": "最大工作线程数", "required": true}, "performance.task_queue_size": {"value": 1000, "type": "int", "description": "任务队列最大大小", "required": true}, "performance.thread_idle_timeout": {"value": 30000, "type": "int", "description": "线程空闲超时时间(毫秒)", "required": false}, "performance.enable_thread_monitoring": {"value": true, "type": "bool", "description": "是否启用线程监控", "required": false}, "performance.memory_pool_size": {"value": 104857600, "type": "int", "description": "内存池大小(字节)", "required": false}, "performance.gc_interval": {"value": 60000, "type": "int", "description": "垃圾回收间隔(毫秒)", "required": false}}, "vision": {"vision.ocr_confidence_threshold": {"value": 0.3, "type": "double", "description": "OCR识别置信度阈值", "required": true}, "vision.image_match_threshold": {"value": 0.6, "type": "double", "description": "图像匹配阈值", "required": true}, "vision.screenshot_quality": {"value": 100, "type": "int", "description": "截图质量(1-100)", "required": false}, "vision.enable_image_cache": {"value": true, "type": "bool", "description": "是否启用图像缓存", "required": false}, "vision.cache_size": {"value": 100, "type": "int", "description": "图像缓存大小", "required": false}, "vision.ocr_language": {"value": "chi_sim", "type": "string", "description": "OCR识别语言", "required": false}, "vision.preprocessing_enabled": {"value": true, "type": "bool", "description": "是否启用图像预处理", "required": false}, "vision.template_match_method": {"value": "TM_CCOEFF_NORMED", "type": "string", "description": "模板匹配方法", "required": false}}}