#include "ConfigManager.h"
#include <fstream>
#include <thread>
#include <chrono>
#include <algorithm>
#include <GUI/consolelog/consolelog.h>

// 静态成员定义
ConfigManager& ConfigManager::getInstance() {
    static ConfigManager instance;
    return instance;
}

bool ConfigManager::initialize(const std::string& configDir, bool enableHotReload) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    try {
        configDirectory_ = configDir;
        hotReloadEnabled_ = enableHotReload;
        
        // 确保配置目录存在
        if (!std::filesystem::exists(configDirectory_)) {
            std::filesystem::create_directories(configDirectory_);
            AddLogInfo(LogLevel::Info, "创建配置目录: " + configDirectory_);
        }
        
        // 加载默认配置
        loadDefaultConfigs();
        
        // 加载合并的配置文件
        std::string mergedConfigPath = configDirectory_ + "/app_config.json";

        
        // 启动热重载监控
        if (hotReloadEnabled_) {
            startHotReloadWatcher();
        }
        
        stats_.lastReloadTime = std::chrono::system_clock::now();
        stats_.reloadCount = 1;
        
        AddLogInfo(LogLevel::Info, "配置管理器初始化完成，共加载 " + 
                  std::to_string(configs_.size()) + " 项配置");
        
        return true;
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ConfigManager] 初始化失败: " + std::string(e.what()));
        return false;
    }
}

void ConfigManager::shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    try {
        stopHotReloadWatcher();
        
        // 清理资源
        configs_.clear();
        validators_.clear();
        listeners_.clear();
        fileTimestamps_.clear();
        
        AddLogInfo(LogLevel::Info, "配置管理器已关闭");
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "关闭时发生异常: " + std::string(e.what()));
    }
}

bool ConfigManager::loadFromFile(const std::string& configFile, ConfigType type) {
    try {
        std::ifstream file(configFile);
        if (!file.is_open()) {
            AddLogInfo(LogLevel::Error, "无法打开配置文件: " + configFile);
            return false;
        }
        
        nlohmann::json jsonConfig;
        file >> jsonConfig;
        
        // 更新文件时间戳
        fileTimestamps_[configFile] = std::filesystem::last_write_time(configFile);
        
        // 解析配置项
        size_t loadedCount = 0;
        
        // 加载合并配置文件格式（按分类组织）
        for (auto& [category, categoryConfig] : jsonConfig.items()) {
            ConfigType categoryType = getConfigTypeFromCategory(category);
            
            if (categoryConfig.is_object()) {
                for (auto& [key, value] : categoryConfig.items()) {
                    try {
                        ConfigItem item = jsonToConfigItem(key, value, categoryType);
                        configs_[key] = item;
                        loadedCount++;
                        
                        // 通知监听器
                        notifyListeners(key, nullptr, &item);
                    } catch (const std::exception& e) {
                        AddLogInfo(LogLevel::Warning, "[ConfigManager] 解析配置项失败 " + key + ": " + e.what());
                    }
                }
            }
        }
        
        return true;
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "加载配置文件异常 " + configFile + ": " + e.what());
        return false;
    }
}

bool ConfigManager::saveToFile(const std::string& configFile, ConfigType type) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    try {
        nlohmann::json jsonConfig;
        
        for (const auto& [key, item] : configs_) {
            if (item.type == type || type == ConfigType::SYSTEM) {
                jsonConfig[key] = configItemToJson(item);
            }
        }
        
        std::ofstream file(configFile);
        if (!file.is_open()) {
            AddLogInfo(LogLevel::Error, "无法写入配置文件: " + configFile);
            return false;
        }
        
        file << jsonConfig.dump(4);
        file.close();
        
        // 更新文件时间戳
        fileTimestamps_[configFile] = std::filesystem::last_write_time(configFile);
        
        AddLogInfo(LogLevel::Info, "配置已保存到: " + configFile);
        return true;
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "保存配置文件异常 " + configFile + ": " + e.what());
        return false;
    }
}

bool ConfigManager::reloadAll() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    try {
        configs_.clear();
        
        // 重新加载所有配置文件
        for (const auto& [filePath, timestamp] : fileTimestamps_) {
            // 对于合并配置文件，使用SYSTEM类型
            ConfigType type = ConfigType::SYSTEM;
            
            if (!loadFromFile(filePath, type)) {
                AddLogInfo(LogLevel::Warning, "重新加载配置文件失败: " + filePath);
            }
        }
        
        stats_.lastReloadTime = std::chrono::system_clock::now();
        stats_.reloadCount++;
        
        AddLogInfo(LogLevel::Info, "配置重新加载完成，共 " + 
                  std::to_string(configs_.size()) + " 项配置");
        
        return true;
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "重新加载配置时发生异常: " + std::string(e.what()));
        return false;
    }
}

bool ConfigManager::hasConfig(const std::string& key) const {
    std::lock_guard<std::mutex> lock(mutex_);
    return configs_.find(key) != configs_.end();
}

bool ConfigManager::removeConfig(const std::string& key) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = configs_.find(key);
    if (it == configs_.end()) {
        return false;
    }
    
    ConfigItem removedItem = it->second;
    configs_.erase(it);
    
    // 通知监听器
    notifyListeners(key, &removedItem, nullptr);
    
    return true;
}

std::map<std::string, ConfigItem> ConfigManager::getConfigsByType(ConfigType type) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::map<std::string, ConfigItem> result;
    for (const auto& [key, item] : configs_) {
        if (item.type == type) {
            result[key] = item;
        }
    }
    
    return result;
}

void ConfigManager::addValidator(const std::string& key, std::shared_ptr<IConfigValidator> validator) {
    std::lock_guard<std::mutex> lock(mutex_);
    validators_[key] = validator;
}

bool ConfigManager::validateAll(std::vector<std::string>& errorMessages) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    errorMessages.clear();
    bool allValid = true;
    
    for (const auto& [key, item] : configs_) {
        std::string errorMsg;
        if (!validateConfig(key, errorMsg)) {
            errorMessages.push_back("配置项 " + key + ": " + errorMsg);
            allValid = false;
        }
    }
    
    stats_.validationErrors = errorMessages.size();
    
    return allValid;
}

bool ConfigManager::validateConfig(const std::string& key, std::string& errorMsg) const {
    auto configIt = configs_.find(key);
    if (configIt == configs_.end()) {
        errorMsg = "配置项不存在";
        return false;
    }
    
    auto validatorIt = validators_.find(key);
    if (validatorIt == validators_.end()) {
        return true; // 没有验证器，认为有效
    }
    
    return validatorIt->second->validate(configIt->second, errorMsg);
}

void ConfigManager::addChangeListener(std::shared_ptr<IConfigChangeListener> listener) {
    std::lock_guard<std::mutex> lock(mutex_);
    listeners_.push_back(listener);
}

void ConfigManager::removeChangeListener(std::shared_ptr<IConfigChangeListener> listener) {
    std::lock_guard<std::mutex> lock(mutex_);
    listeners_.erase(
        std::remove_if(listeners_.begin(), listeners_.end(),
            [&listener](const std::weak_ptr<IConfigChangeListener>& weak) {
                return weak.lock() == listener;
            }),
        listeners_.end()
    );
}

ConfigManager::ConfigStats ConfigManager::getStats() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    ConfigStats currentStats = stats_;
    currentStats.totalConfigs = configs_.size();
    
    // 统计各类型配置数量
    std::fill(std::begin(currentStats.configsByType), std::end(currentStats.configsByType), 0);
    for (const auto& [key, item] : configs_) {
        int typeIndex = static_cast<int>(item.type);
        if (typeIndex >= 0 && typeIndex < 6) {
            currentStats.configsByType[typeIndex]++;
        }
    }
    
    return currentStats;
}

std::string ConfigManager::exportToJson(ConfigType type) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    nlohmann::json result;
    for (const auto& [key, item] : configs_) {
        if (item.type == type || type == ConfigType::SYSTEM) {
            result[key] = configItemToJson(item);
        }
    }
    
    return result.dump(4);
}

// ==================== 私有方法实现 ====================

void ConfigManager::startHotReloadWatcher() {
    if (hotReloadRunning_.load()) {
        return;
    }
    
    hotReloadRunning_.store(true);
    hotReloadThread_ = std::make_unique<std::thread>([this]() {

        while (hotReloadRunning_.load()) {
            try {
                checkFileChanges();
                std::this_thread::sleep_for(std::chrono::seconds(2));
            } catch (const std::exception& e) {
                AddLogInfo(LogLevel::Error, "热重载监控异常: " + std::string(e.what()));
            }
        }
        
        AddLogInfo(LogLevel::Info, "热重载监控线程已停止");
    });
}

void ConfigManager::stopHotReloadWatcher() {
    if (!hotReloadRunning_.load()) {
        return;
    }
    
    hotReloadRunning_.store(false);
    
    if (hotReloadThread_ && hotReloadThread_->joinable()) {
        hotReloadThread_->join();
    }
    
    hotReloadThread_.reset();
}

void ConfigManager::checkFileChanges() {
    for (auto& [filePath, lastTimestamp] : fileTimestamps_) {
        try {
            if (!std::filesystem::exists(filePath)) {
                continue;
            }
            
            auto currentTimestamp = std::filesystem::last_write_time(filePath);
            if (currentTimestamp > lastTimestamp) {
                AddLogInfo(LogLevel::Info, "检测到配置文件变更: " + filePath);
                
                // 对于合并配置文件，使用SYSTEM类型
                ConfigType type = ConfigType::SYSTEM;
                
                if (loadFromFile(filePath, type)) {
                    lastTimestamp = currentTimestamp;
                    AddLogInfo(LogLevel::Info, "配置热重载成功: " + filePath);
                } else {
                    AddLogInfo(LogLevel::Error, "配置热重载失败: " + filePath);
                }
            }
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "检查文件变更时异常 " + filePath + ": " + e.what());
        }
    }
}

void ConfigManager::notifyListeners(const std::string& key, const ConfigItem* oldItem, const ConfigItem* newItem) {
    for (auto it = listeners_.begin(); it != listeners_.end();) {
        auto listener = it->lock();
        if (!listener) {
            it = listeners_.erase(it);
            continue;
        }
        
        try {
            if (oldItem && newItem) {
                listener->onConfigChanged(key, *oldItem, *newItem);
            } else if (newItem) {
                listener->onConfigAdded(key, *newItem);
            } else if (oldItem) {
                listener->onConfigRemoved(key, *oldItem);
            }
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Warning, "配置通知监听器时异常: " + std::string(e.what()));
        }
        
        ++it;
    }
}

nlohmann::json ConfigManager::configItemToJson(const ConfigItem& item) const {
    nlohmann::json json;
    
    // 根据存储的类型进行转换
    try {
        // 尝试不同的类型转换
        if (auto intVal = std::any_cast<int>(&item.value)) {
            json["value"] = *intVal;
            json["type"] = "int";
        } else if (auto doubleVal = std::any_cast<double>(&item.value)) {
            json["value"] = *doubleVal;
            json["type"] = "double";
        } else if (auto stringVal = std::any_cast<std::string>(&item.value)) {
            json["value"] = *stringVal;
            json["type"] = "string";
        } else if (auto boolVal = std::any_cast<bool>(&item.value)) {
            json["value"] = *boolVal;
            json["type"] = "bool";
        } else {
            // 默认转换为字符串
            json["value"] = "unknown_type";
            json["type"] = "unknown";
        }
    } catch (const std::exception& e) {
        json["value"] = "conversion_error";
        json["type"] = "error";
    }
    
    json["description"] = item.description;
    json["required"] = item.isRequired;
    json["config_type"] = static_cast<int>(item.type);
    
    return json;
}

ConfigItem ConfigManager::jsonToConfigItem(const std::string& key, const nlohmann::json& json, ConfigType type) const {
    ConfigItem item;
    item.key = key;
    item.type = type;
    
    if (json.is_object()) {
        // 新格式：包含元数据的对象
        if (json.contains("value")) {
            std::string valueType = json.value("type", "auto");
            
            if (valueType == "int" && json["value"].is_number_integer()) {
                item.setValue(json["value"].get<int>());
            } else if (valueType == "double" && json["value"].is_number()) {
                item.setValue(json["value"].get<double>());
            } else if (valueType == "bool" && json["value"].is_boolean()) {
                item.setValue(json["value"].get<bool>());
            } else if (valueType == "string" && json["value"].is_string()) {
                item.setValue(json["value"].get<std::string>());
            } else {
                // 自动推断类型
                if (json["value"].is_string()) {
                    item.setValue(json["value"].get<std::string>());
                } else if (json["value"].is_number_integer()) {
                    item.setValue(json["value"].get<int>());
                } else if (json["value"].is_number()) {
                    item.setValue(json["value"].get<double>());
                } else if (json["value"].is_boolean()) {
                    item.setValue(json["value"].get<bool>());
                }
            }
        }
        
        item.description = json.value("description", "");
        item.isRequired = json.value("required", false);
        
        if (json.contains("config_type")) {
            item.type = static_cast<ConfigType>(json["config_type"].get<int>());
        }
    } else {
        // 旧格式：直接值
        if (json.is_string()) {
            item.setValue(json.get<std::string>());
        } else if (json.is_number_integer()) {
            item.setValue(json.get<int>());
        } else if (json.is_number()) {
            item.setValue(json.get<double>());
        } else if (json.is_boolean()) {
            item.setValue(json.get<bool>());
        }
    }
    
    return item;
}

ConfigType ConfigManager::getConfigTypeFromCategory(const std::string& category) const {
    if (category == "system") return ConfigType::SYSTEM;
    if (category == "vm" || category == "ip") return ConfigType::VM;
    if (category == "vision") return ConfigType::VISION;
    if (category == "script") return ConfigType::SCRIPT;
    if (category == "network") return ConfigType::NETWORK;
    if (category == "performance") return ConfigType::PERFORMANCE;
    
    return ConfigType::SYSTEM; // 默认类型
}

void ConfigManager::loadDefaultConfigs() {
    // 直接操作configs_映射，因为我们已经在mutex锁内
    auto addDefaultConfig = [this](const std::string& key, auto value, ConfigType type, const std::string& desc, bool required = false) {
        ConfigItem item;
        item.key = key;
        item.setValue(value);
        item.type = type;
        item.description = desc;
        item.isRequired = required;
        configs_[key] = item;
    };
    
    // 系统配置默认值
    addDefaultConfig("threading.general_pool_size", 4, ConfigType::PERFORMANCE, "通用线程池大小", true);
    addDefaultConfig("threading.ocr_pool_size", 2, ConfigType::PERFORMANCE, "OCR线程池大小", true);
    addDefaultConfig("threading.vision_pool_size", 2, ConfigType::PERFORMANCE, "视觉处理线程池大小", true);
    addDefaultConfig("threading.max_named_threads", 50, ConfigType::PERFORMANCE, "最大命名线程数", true);
    
    addDefaultConfig("performance.task_queue_size", 1000, ConfigType::PERFORMANCE, "任务队列大小", true);
    addDefaultConfig("performance.vision_cache_size", 100, ConfigType::PERFORMANCE, "视觉缓存大小", true);
    addDefaultConfig("performance.log_buffer_size", 10000, ConfigType::PERFORMANCE, "日志缓冲区大小", true);
    
    addDefaultConfig("system.config_dir", std::string("./config"), ConfigType::SYSTEM, "配置文件目录", true);
    addDefaultConfig("system.assets_dir", std::string("./assets"), ConfigType::SYSTEM, "资源文件目录", true);
    addDefaultConfig("system.script_dir", std::string("./script"), ConfigType::SYSTEM, "脚本文件目录", true);
    addDefaultConfig("system.log_level", std::string("Info"), ConfigType::SYSTEM, "日志级别", true);
    
    addDefaultConfig("network.connection_timeout", 5000, ConfigType::NETWORK, "连接超时时间(毫秒)", true);
    addDefaultConfig("network.retry_count", 3, ConfigType::NETWORK, "重试次数", true);
    addDefaultConfig("network.retry_delay", 1000, ConfigType::NETWORK, "重试延迟(毫秒)", true);
    
    addDefaultConfig("vision.default_threshold", 0.7, ConfigType::VISION, "默认匹配阈值", true);
    addDefaultConfig("vision.template_cache_enabled", true, ConfigType::VISION, "模板缓存开关", true);
    addDefaultConfig("vision.ocr_language", std::string("chi_sim"), ConfigType::VISION, "OCR识别语言", true);
} 