//标签定位功能测试脚本
//用法说明：
//方案一：LABEL_LOCATE(x,y,width,height) - OCR识别指定区域的文字，然后搜索对应标签并跳转
//方案二：LABEL_LOCATE(文本) - 直接搜索指定文本的标签并跳转

slice(1) {
    // 示例1：使用ROI识别文字，然后定位到对应标签
    // 假设屏幕区域(100,200,150,30)中显示了"猫儿偷鸡"文字
    // 系统会OCR识别这个区域，然后搜索LABEL:猫儿偷鸡并跳转执行
    LABEL_LOCATE(100,200,150,30)
    
    // 示例2：直接指定文本进行标签定位
    // 直接搜索LABEL:燃犀之灯并跳转执行
    LABEL_LOCATE(燃犀之灯)
    
    // 示例3：如果没有找到对应标签，会继续执行后续命令
    LABEL_LOCATE(不存在的标签)
    MOUSE_MOVE_CLICK(500,500)
    
    // 标签定义区域
    LABEL:燃犀之灯
    TEXT_MATCH(燃犀之灯, left, 1050, 400, 230, 200)
    TEXT_MATCH(燃犀之灯, left, 470,270,350,480)
    MOUSE_LEFT()
    
    LABEL:猫儿偷鸡  
    TEXT_MATCH(猫儿偷鸡, left, 1050, 400, 230, 200)
    DELAY(1000)
    MOUSE_LEFT()
    DELAY(5000)
    VISUAL_MATCH(lingxi,right)
    
    LABEL:小鸡咕咕
    TEXT_MATCH(小鸡咕咕, left, 1050, 400, 230, 200)
    MOUSE_MOVE_CLICK(800,600)
    
    // 更多任务...
    MOUSE_MOVE(100,100)
    DELAY(2000)
} 