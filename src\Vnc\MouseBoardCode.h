#pragma once
#include "VNCControl.h"
#include <vector>

InputTask createKeyboardTask(const std::string& keyName);
InputTask mouseLeftClick();
InputTask mouseRightClick();
InputTask mouseMoveCoord(int x, int y);
InputTask mouseDragCoord(int x, int y);

// 鼠标双击操作 - 返回包含两次点击的向量
std::vector<InputTask> mouseDoubleClick();

// 组合操作函数 - 返回多个InputTask的向量
std::vector<InputTask> mouseMoveAndLeftClick(int x, int y);
std::vector<InputTask> mouseMoveAndDoubleClick(int x, int y);
std::vector<InputTask> mouseMoveAndRightClick(int x, int y);

