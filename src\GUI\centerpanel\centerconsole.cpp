#include "imgui.h"
#include <vector>
#include <string>
#include <cstdio>
#include "centerconsole.h"
#include <fstream>
#include <algorithm>
#include<json.hpp>
#include <iostream>


// 列表项数据结构
struct ListItem {
    int id;
    std::string name;
    std::string region;
    std::string role;
    std::string team;
    std::string grad;
    std::string task;
    std::string status;
    std::string banknote;
    std::string tael;
    std::string other;
    bool selected = false; // 选中状态
};
// 大区数据结构
struct GameZone {
    std::string name;
    std::vector<std::string> servers;
};
//角色信息
std::vector<std::string> roleList = { "天狼方士", "道法方士", "画魂","偃师","战魂甲士" ,"金刚甲士" ,"射手" ,"断恨刀客" ,"藏刃刀客" ,"侠客" ,"医师" ,"魅者","异人"};

// 全局数据
namespace GlobalData {
    std::vector<ListItem> items;
    int itemContextMenuOpen = -1; // 当前打开上下文菜单的项索引，-1表示无
}

// 全局数据
namespace GameData {
    std::vector<GameZone> zones = {
        {"山河念", {
            "好巳成双","巳巳如意","卧麒麟","琅琊榜","监天司","大千录","坐忘道","白玉京"
        }}
    };
}


// 初始化示例数据
void InitializeSampleData() {
    if (GlobalData::items.empty()) {
        for (int i = 1; i <= 20; ++i) {
            GlobalData::items.push_back({
                i,   //id
                "Item " + std::to_string(i), //name
				"分区",//GameData::zones[1].servers[0], //region
                roleList[i%11],//role
				"一",   //team
				"34",   //grad
				"正在进行中···", //task
				(i % 3 == 0) ? "运行" : "停止", //status
				"100",  //banknote,
				"200",  //tael
				"其他信息" //other
            });
        }
    }
}

// 渲染表格头部
void RenderTableHeader() {
    //ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 1.0f, 1.0f, 1.0f)); //设置表头字体颜色
    // 设置表格列
    ImGui::TableSetupColumn("序号", ImGuiTableColumnFlags_WidthFixed, 50.0f);
    ImGui::TableSetupColumn("账号", ImGuiTableColumnFlags_WidthFixed, 50.0f);
    ImGui::TableSetupColumn("区服", ImGuiTableColumnFlags_WidthFixed, 50.0f);
    ImGui::TableSetupColumn("角色", ImGuiTableColumnFlags_WidthFixed, 50.0f);
    ImGui::TableSetupColumn("组队", ImGuiTableColumnFlags_WidthFixed, 50.0f);
    ImGui::TableSetupColumn("等级", ImGuiTableColumnFlags_WidthFixed, 50.0f);
    ImGui::TableSetupColumn("当前任务", ImGuiTableColumnFlags_WidthStretch, 200.0f);
    ImGui::TableSetupColumn("状态", ImGuiTableColumnFlags_WidthFixed, 50.0f);
	ImGui::TableSetupColumn("银票", ImGuiTableColumnFlags_WidthFixed, 50.0f);
	ImGui::TableSetupColumn("银两", ImGuiTableColumnFlags_WidthFixed, 50.0f);
	ImGui::TableSetupColumn("其他", ImGuiTableColumnFlags_WidthFixed, 50.0f);
    ImGui::TableSetupScrollFreeze(0, 1); // 冻结表头行
    ImGui::TableHeadersRow();            // 显示表头
    /*ImGui::PopStyleColor();*/


}

// 处理项选择逻辑
void HandleItemSelection(int index) {
    if (!ImGui::GetIO().KeyCtrl) { // 如果没按住Ctrl键，清除其他选择
        for (auto& item : GlobalData::items) {
            item.selected = false;
        }
    }
    GlobalData::items[index].selected = !GlobalData::items[index].selected;
}

// 渲染上下文菜单
void RenderContextMenu(int index) {
    char popup_id[64];
    snprintf(popup_id, sizeof(popup_id), "item_context_%d", index);

    if (ImGui::BeginPopupContextItem(popup_id)) {
        ImGui::Text("Actions for Item %d", GlobalData::items[index].id);
        ImGui::Separator();

        if (ImGui::MenuItem("Start")) {
            printf("Action: Start item %d\n", GlobalData::items[index].id);
            GlobalData::items[index].status = "运行";
        }

        if (ImGui::MenuItem("Stop")) {
            printf("Action: Stop item %d\n", GlobalData::items[index].id);
            GlobalData::items[index].status = "停止";
        }

        if (ImGui::MenuItem("Delete")) {
            printf("Action: Delete item %d\n", GlobalData::items[index].id);
            // 实际项目中应考虑安全删除逻辑
        }

        ImGui::Separator();
        if (ImGui::MenuItem("Close Menu")) {
            // 点击外部也会关闭菜单
        }

        ImGui::EndPopup();
    }
}
//int id;
//std::string name;
//std::string region;
//std::string role;
//std::string team;
//std::string grad;
//std::string task;
//std::string status;
//std::string banknote;
//std::string tael;
//std::string other;
// 渲染表格内容
void RenderTableContent() {
    for (int i = 0; i < GlobalData::items.size(); ++i) {
        ImGui::TableNextRow();

        // 第1列: ID (可选中)
        ImGui::TableNextColumn();
        char label[64];
        snprintf(label, sizeof(label), "%d##%d", GlobalData::items[i].id, i);

        if (ImGui::Selectable(label, GlobalData::items[i].selected,
            ImGuiSelectableFlags_SpanAllColumns | ImGuiSelectableFlags_AllowItemOverlap)) {
            HandleItemSelection(i);
        }

        RenderContextMenu(i);

        // 第2列: 名称
        ImGui::TableNextColumn();
        ImGui::TextUnformatted(GlobalData::items[i].name.c_str());

        // 第3列: 区服
        ImGui::TableNextColumn();
        ImGui::TextUnformatted(GlobalData::items[i].region.c_str());

        // 第4列: 角色
        ImGui::TableNextColumn();
        ImGui::TextUnformatted(GlobalData::items[i].role.c_str());
        // 第5列: 组队
        ImGui::TableNextColumn();
        ImGui::TextUnformatted(GlobalData::items[i].team.c_str());

        // 第6列: 等级
        ImGui::TableNextColumn();
        ImGui::TextUnformatted(GlobalData::items[i].grad.c_str());

        // 第7列: 当前任务
        ImGui::TableNextColumn();
        ImGui::TextUnformatted(GlobalData::items[i].task.c_str());

        // 第8列: 状态 (带颜色)
        ImGui::TableNextColumn();
        if (GlobalData::items[i].status == "运行") {
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.0f, 1.0f, 0.0f, 1.0f)); // 绿色
        }
        else {
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.0f, 0.0f, 1.0f)); // 红色
        }
        ImGui::TextUnformatted(GlobalData::items[i].status.c_str());
        ImGui::PopStyleColor();

        // 第9列: 银票
        ImGui::TableNextColumn();
        ImGui::TextUnformatted(GlobalData::items[i].banknote.c_str());

        // 第10列: 银两
        ImGui::TableNextColumn();
        ImGui::TextUnformatted(GlobalData::items[i].tael.c_str());

        // 第11列: 其他
        ImGui::TableNextColumn();
        ImGui::TextUnformatted(GlobalData::items[i].other.c_str());
    }
}

// 渲染底部控制按钮
void RenderBottomControls() {
    ImGui::Separator();
    if (ImGui::Button("Add Item")) {
        int newId = GlobalData::items.empty() ? 1 : GlobalData::items.back().id + 1;
        GlobalData::items.push_back({ newId, "New Item " + std::to_string(newId), "Stopped" });
    }
}

//下半段代码
struct AccountEntry {
    int id;
    char username[128];
    char password[128];
};

std::vector<AccountEntry> accounts;
int next_id = 1;
char new_username[128] = "";
char new_password[128] = "";
int selected_row = -1;

void AddAccount() {
    if (strlen(new_username) > 0 && strlen(new_password) > 0) {
        AccountEntry entry;
        entry.id = 1;
        //strcpy_s(entry.username, new_username);
        //strcpy_s(entry.password, new_password);
        accounts.push_back(entry);
        //new_username[0] = '\0';
        //new_password[0] = '\0';
    }

}

void UpdateAccount(int index) {
    if (index >= 0 && index < accounts.size()) {
        strcpy_s(new_username, accounts[index].username);
        strcpy_s(new_password, accounts[index].password);
        selected_row = index;
    }
}

void DeleteAccount(int index) {
    if (index >= 0 && index < accounts.size()) {
        accounts.erase(accounts.begin() + index);
        if (selected_row == index) {
            selected_row = -1;
        }
    }
}

void DrawAccountTable() {
    ImGui::BeginChild("Account Manager", ImVec2(0, 0));
    ImGui::BeginChild("Accountleft",ImVec2(500, 0));
    // 表格
    ImGui::BeginGroup();
    if (ImGui::BeginTable("Accounts", 3, ImGuiTableFlags_Borders | ImGuiTableFlags_RowBg | ImGuiTableFlags_Resizable)) {
        ImGui::TableSetupColumn("序号");
        ImGui::TableSetupColumn("账号");
        ImGui::TableSetupColumn("密码");
        ImGui::TableHeadersRow();

        // 拖拽排序逻辑
        for (int i = 0; i < accounts.size(); i++) {
            ImGui::PushID(i); // 确保唯一 ID

            ImGui::TableNextRow();
            ImGui::TableSetColumnIndex(0);

            // 可选中行（带拖拽和右键菜单）
            bool is_selected = (selected_row == i);
            ImGuiSelectableFlags flags = ImGuiSelectableFlags_SpanAllColumns | ImGuiSelectableFlags_AllowItemOverlap;
            if (ImGui::Selectable(std::to_string(accounts[i].id).c_str(), is_selected, flags)) {
                selected_row = i;
            }

            // 拖拽排序
            if (ImGui::BeginDragDropSource(ImGuiDragDropFlags_None)) {
                ImGui::SetDragDropPayload("ACCOUNT_ROW", &i, sizeof(int));
                ImGui::Text("Move Row %d", i + 1);
                ImGui::EndDragDropSource();
            }
            if (ImGui::BeginDragDropTarget()) {
                if (const ImGuiPayload* payload = ImGui::AcceptDragDropPayload("ACCOUNT_ROW")) {
                    int src_row = *(const int*)payload->Data;
                    if (src_row != i) {
                        std::swap(accounts[src_row], accounts[i]);
                        selected_row = i; // 更新选中行
                    }
                }
                ImGui::EndDragDropTarget();
            }

            // 其他列
            ImGui::TableSetColumnIndex(1);
            ImGui::Text("%s", accounts[i].username);
            ImGui::TableSetColumnIndex(2);
            ImGui::Text("%s", accounts[i].password);

            // 右键菜单
            if (ImGui::BeginPopupContextItem()) {
                if (ImGui::MenuItem("修改")) {
                    strcpy_s(new_username, accounts[i].username);
                    strcpy_s(new_password, accounts[i].password);
                    selected_row = i;
                }
                if (ImGui::MenuItem("删除")) {
                    DeleteAccount(i);
                    if (selected_row >= accounts.size()) selected_row = -1;
                }
                ImGui::EndPopup();
            }
            ImGui::PopID(); // 结束唯一 ID 作用域
        }
        ImGui::EndTable();
    }
    ImGui::EndGroup();
    ImGui::EndChild();

    ImGui::SameLine();
    // 右侧输入框和添加按钮
    ImGui::BeginChild("Accountright",ImVec2(0, 0));
    ImGui::BeginGroup();
    ImGui::Text("新增账号");
    ImGui::InputText("账号", new_username, IM_ARRAYSIZE(new_username));
    ImGui::InputText("密码", new_password, IM_ARRAYSIZE(new_password));

    if (selected_row == -1) {
        if (ImGui::Button("新增")) {
            AddAccount();
        }
    }
    else {
        if (ImGui::Button("更新")) {
            if (strlen(new_username) > 0 && strlen(new_password) > 0) {
                strcpy_s(accounts[selected_row].username, new_username);
                strcpy_s(accounts[selected_row].password, new_password);
                selected_row = -1;
                new_username[0] = '\0';
                new_password[0] = '\0';
            }
        }
        ImGui::SameLine();
        if (ImGui::Button("取消")) {
            selected_row = -1;
            new_username[0] = '\0';
            new_password[0] = '\0';
        }
    }
    ImGui::EndGroup();
    ImGui::EndChild();
    ImGui::EndChild();
}

// 主面板渲染函数
void MainPanel() {
    // 设置表格背景颜色
    ImGui::PushStyleColor(ImGuiCol_TableRowBg, ImVec4(1.0f, 1.0f, 1.0f, 1.0f));        // 默认行背景
    ImGui::PushStyleColor(ImGuiCol_TableBorderLight, ImVec4(0.89f, 0.91f, 0.93f, 1.0f));  // 表格边框颜色
    // 设置悬停和选中颜色
    //ImGui::PushStyleColor(ImGuiCol_Header, ImVec4(24 / 256.0f, 142 / 256.0f, 252 / 256.0f, 1.0f));        // 选中项背景
    ImGui::PushStyleColor(ImGuiCol_HeaderHovered, ImVec4(106 / 256.0f, 184 / 256.0f, 255 / 256.0f, 1.0f));  // 悬停项背景
    ImGui::PushStyleColor(ImGuiCol_HeaderActive, ImVec4(24 / 256.0f, 142 / 256.0f, 252 / 256.0f, 1.0f));   // 点击项背景
    ImGui::PushStyleColor(ImGuiCol_TableHeaderBg, ImVec4(150 / 256.0f, 203 / 256.0f, 255 / 256.0f, 1.0f));//表头背景
    ImGui::PushStyleVar(ImGuiStyleVar_CellPadding, ImVec2(3.0f,3.0f));  //表格内边距
    InitializeSampleData();

    ImGui::PushStyleColor(ImGuiCol_ChildBg, ImVec4(64/256.0f, 68/256.0f, 73/256.0f, 1.0f));        //子窗口背景颜色
    // 开始中心控制台子窗口
    ImGui::BeginChild("centerconsole", ImVec2(0, 0));

    // 表格标志
    ImGuiTableFlags table_flags =
        ImGuiTableFlags_BordersH |   //内部水平分隔线
        ImGuiTableFlags_Resizable |  //允许调整列宽（用户可拖动列边缘）
        ImGuiTableFlags_ScrollY |      //启用纵向滚动条（行数多时）
        ImGuiTableFlags_NoBordersInBody;	//隐藏表格主体内的边框（保留外边框）
        /*ImGuiTableFlags_NoClip;*/

    // 计算表格高度 (使用可用空间)
    float table_height = ImGui::GetContentRegionAvail().y - ImGui::GetStyle().ItemSpacing.y * 2-300;

    // 开始表格
    if (ImGui::BeginTable("item_list_table", 11, table_flags, ImVec2(0.0f, table_height))) {
        RenderTableHeader();
        RenderTableContent();
        ImGui::EndTable();
    }

    RenderBottomControls();
    DrawAccountTable();
    ImGui::PopStyleVar(1);
    ImGui::PopStyleColor(6);
    ImGui::EndChild();
}

//1. 边框与外观（Borders & Appearance）
//标志	作用
//ImGuiTableFlags_Borders	所有边框（外边框 + 内部分隔线）
//ImGuiTableFlags_BordersOuter	仅外边框（表格四周）
//ImGuiTableFlags_BordersInner	仅内部分隔线（行 / 列之间）
//ImGuiTableFlags_BordersH	水平分隔线（行之间）
//ImGuiTableFlags_BordersV	垂直分隔线（列之间）
//ImGuiTableFlags_NoBordersInBody	隐藏表格主体内的边框（保留外边框）
//ImGuiTableFlags_RowBg	交替行背景色（斑马纹效果）
//ImGuiTableFlags_NoHostExtendX	表格宽度不自动扩展（默认会占满可用空间）
//2. 列与行控制（Columns & Rows）
//标志	作用
//ImGuiTableFlags_Resizable	允许调整列宽（用户可拖动列边缘）
//ImGuiTableFlags_Reorderable	允许拖拽调整列顺序
//ImGuiTableFlags_Hideable	允许隐藏列（显示 / 隐藏按钮）
//ImGuiTableFlags_NoKeepColumnsVisible	自动隐藏超出宽度的列（无横向滚动条）
//ImGuiTableFlags_PreciseWidths	精确控制列宽（避免自动对齐）
//3. 表头与交互（Headers & Interaction）
//标志	作用
//ImGuiTableFlags_NoHeaders	隐藏表头（不显示列标题）
//ImGuiTableFlags_HighlightHoveredColumn	高亮悬停的列（鼠标悬停时显示背景色）
//ImGuiTableFlags_HighlightHoveredRow	高亮悬停的行
//ImGuiTableFlags_SizingStretchSame	所有列等宽拉伸
//ImGuiTableFlags_SizingStretchProp	按比例拉伸列宽
//ImGuiTableFlags_SizingFixedFit	固定列宽（自动适应内容）
//4. 滚动与布局（Scrolling & Layout）
//标志	作用
//ImGuiTableFlags_ScrollX	启用横向滚动条（列数多时）
//ImGuiTableFlags_ScrollY	启用纵向滚动条（行数多时）
//ImGuiTableFlags_NoClip	禁用裁剪（允许表格内容超出窗口边界）
//5. 其他功能（Miscellaneous）
//标志	作用
//ImGuiTableFlags_Sortable	允许点击表头排序（需配合 ImGuiTableColumnFlags_NoSort 控制列）
//ImGuiTableFlags_ContextMenuInBody	在表格主体内右键弹出菜单