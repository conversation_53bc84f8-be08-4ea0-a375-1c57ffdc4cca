#pragma once

#include <memory>
#include <functional>
#include <cstdlib>

/**
 * @brief 现代C++内存管理工具类
 * 
 * 提供RAII模式的内存管理工具，用于替代传统的C风格内存管理
 * 支持自动内存释放、异常安全和资源管理
 */
namespace MemoryUtils {

    /**
     * @brief C字符串自动释放器
     * 用于管理通过malloc/strdup分配的C字符串
     */
    struct CStringDeleter {
        void operator()(char* ptr) const noexcept {
            if (ptr) {
                std::free(ptr);
            }
        }
    };

    /**
     * @brief C内存自动释放器  
     * 用于管理通过malloc分配的内存
     */
    struct CMemoryDeleter {
        void operator()(void* ptr) const noexcept {
            if (ptr) {
                std::free(ptr);
            }
        }
    };

    /**
     * @brief 智能C字符串类型别名
     * 使用unique_ptr管理C字符串，自动调用free释放内存
     */
    using smart_cstring = std::unique_ptr<char, CStringDeleter>;

    /**
     * @brief 智能C内存类型别名
     * 使用unique_ptr管理C内存，自动调用free释放内存
     */
    using smart_cmemory = std::unique_ptr<void, CMemoryDeleter>;

    /**
     * @brief 创建智能C字符串
     * 
     * @param str 要复制的字符串
     * @return smart_cstring 自动管理内存的智能指针
     */
    inline smart_cstring make_smart_cstring(const char* str) {
        if (!str) {
            return smart_cstring(nullptr);
        }
        return smart_cstring(strdup(str));
    }

    /**
     * @brief 创建智能C字符串 (std::string版本)
     * 
     * @param str 要复制的字符串
     * @return smart_cstring 自动管理内存的智能指针
     */
    inline smart_cstring make_smart_cstring(const std::string& str) {
        return smart_cstring(strdup(str.c_str()));
    }

    /**
     * @brief 创建智能C内存
     * 
     * @param size 要分配的内存大小
     * @return smart_cmemory 自动管理内存的智能指针
     */
    inline smart_cmemory make_smart_cmemory(size_t size) {
        return smart_cmemory(std::malloc(size));
    }

    /**
     * @brief RAII资源管理器模板
     * 
     * 用于管理任意类型的资源，支持自定义清理函数
     * 
     * @tparam T 资源类型
     * @tparam Deleter 清理函数类型
     */
    template<typename T, typename Deleter>
    class RAIIGuard {
    private:
        T resource_;
        Deleter deleter_;
        bool released_;

    public:
        explicit RAIIGuard(T resource, Deleter deleter = Deleter{}) 
            : resource_(resource), deleter_(deleter), released_(false) {}

        ~RAIIGuard() {
            if (!released_) {
                deleter_(resource_);
            }
        }

        // 禁用拷贝
        RAIIGuard(const RAIIGuard&) = delete;
        RAIIGuard& operator=(const RAIIGuard&) = delete;

        // 支持移动
        RAIIGuard(RAIIGuard&& other) noexcept 
            : resource_(other.resource_), deleter_(std::move(other.deleter_)), released_(other.released_) {
            other.released_ = true;
        }

        RAIIGuard& operator=(RAIIGuard&& other) noexcept {
            if (this != &other) {
                if (!released_) {
                    deleter_(resource_);
                }
                resource_ = other.resource_;
                deleter_ = std::move(other.deleter_);
                released_ = other.released_;
                other.released_ = true;
            }
            return *this;
        }

        // 获取资源
        T get() const noexcept { return resource_; }

        // 释放资源所有权
        T release() noexcept {
            released_ = true;
            return resource_;
        }

        // 重置资源
        void reset(T new_resource = T{}) {
            if (!released_) {
                deleter_(resource_);
            }
            resource_ = new_resource;
            released_ = false;
        }

        // 检查是否有效
        explicit operator bool() const noexcept {
            return !released_ && resource_ != T{};
        }
    };

    /**
     * @brief 创建RAII资源守护
     * 
     * @tparam T 资源类型
     * @tparam Deleter 清理函数类型
     * @param resource 资源
     * @param deleter 清理函数
     * @return RAIIGuard<T, Deleter> RAII守护对象
     */
    template<typename T, typename Deleter>
    auto make_raii_guard(T resource, Deleter deleter) {
        return RAIIGuard<T, Deleter>(resource, deleter);
    }

    /**
     * @brief 检查内存分配是否成功的工具函数
     * 
     * @tparam T 指针类型
     * @param ptr 待检查的指针
     * @param error_msg 错误消息
     * @return true 分配成功
     * @return false 分配失败
     */
    template<typename T>
    bool check_allocation(T* ptr, const std::string& error_msg = "Memory allocation failed") {
        if (!ptr) {
            // 这里可以添加日志记录
            return false;
        }
        return true;
    }

} // namespace MemoryUtils 