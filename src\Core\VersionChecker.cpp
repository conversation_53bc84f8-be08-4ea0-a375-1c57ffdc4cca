#include "VersionChecker.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <ctime>
#include <locale>
#include <codecvt>
#include <GUI/consolelog/consolelog.h>

// 初始化静态成员
const std::string VersionChecker::CURRENT_VERSION = "1.0.0";
const time_t VersionChecker::RELEASE_TIMESTAMP = 1759513600; //1739513600 1749513600
const std::wstring VersionChecker::SERVER_NAME = L"yiliao-1258575299.cos.ap-chengdu.myqcloud.com";
const std::wstring VersionChecker::OBJECT_NAME = L"/qnyhMain2/version.txt";
const int VersionChecker::SERVER_PORT = INTERNET_DEFAULT_HTTPS_PORT;
const DWORD VersionChecker::NETWORK_TIMEOUT = 10000; // 10秒超时
const time_t VersionChecker::MAX_USAGE_TIME = 30 * 24 * 60 * 60; // 30天

std::string VersionChecker::lastError;
time_t VersionChecker::lastServerTime = 0;

VersionChecker::CheckResult VersionChecker::checkVersion() {
    try {
        //AddLogInfo(LogLevel::Info, "[VersionChecker] 开始版本检查...");
        //
        // 1. 获取远程版本信息和服务器时间
        auto [remoteVersion, serverTime] = fetchVersionAndTime();
        lastServerTime = serverTime;
        
        AddLogInfo(LogLevel::Info, "[VersionChecker]当前: " + CURRENT_VERSION + " 最新:" + remoteVersion);
        
        // 2. 验证版本号
        if (remoteVersion != CURRENT_VERSION) {
            lastError = "版本号不匹配：当前版本 " + CURRENT_VERSION + "，最新版本 " + remoteVersion;
            return CheckResult::VERSION_MISMATCH;
        }
        
        // 3. 验证时间
        if (!validateTime(serverTime)) {
            time_t timeDiff = serverTime - RELEASE_TIMESTAMP;
            int daysPassed = static_cast<int>(timeDiff / (24 * 60 * 60));
            /*lastError = "软件已过期，已使用 " + std::to_string(daysPassed) + " 天";*/
            lastError = "软件已过期";
            return CheckResult::TIME_EXPIRED;
        }
        
        // 计算剩余天数
        int remainingDays = getRemainingDays();
        //AddLogInfo(LogLevel::Info, "[VersionChecker] 版本检查通过，剩余使用天数: " + std::to_string(remainingDays));
        //
        return CheckResult::SUCCESS;
        
    } catch (const std::exception& e) {
        lastError = "版本检查失败：" + std::string(e.what());
        return CheckResult::UNKNOWN_ERROR;
    }
}

std::pair<std::string, time_t> VersionChecker::fetchVersionAndTime() {
    time_t serverTime = 0;
    std::string content = downloadString(SERVER_NAME, OBJECT_NAME, SERVER_PORT, serverTime);
    
    //AddLogInfo(LogLevel::Info, "[VersionChecker] 获取到内容: " + content);
    
    std::string version = extractVersion(content);
    
    return {version, serverTime};
}

std::string VersionChecker::downloadString(const std::wstring& serverName, 
                                          const std::wstring& objectName, 
                                          int port, 
                                          time_t& serverTime) {
    
    //AddLogInfo(LogLevel::Info, "[VersionChecker] 发送网络请求到: " + wstringToString(serverName + objectName));
    
    // 初始化WinHTTP
    HINTERNET hSession = WinHttpOpen(L"VersionChecker/1.0",
                                    WINHTTP_ACCESS_TYPE_AUTOMATIC_PROXY,
                                    WINHTTP_NO_PROXY_NAME,
                                    WINHTTP_NO_PROXY_BYPASS,
                                    0);
    
    if (!hSession) {
        lastError = "无法初始化HTTP会话，错误代码: " + std::to_string(GetLastError());
        throw std::runtime_error("HTTP初始化失败");
    }
    
    // 设置超时
    WinHttpSetTimeouts(hSession, NETWORK_TIMEOUT, NETWORK_TIMEOUT, NETWORK_TIMEOUT, NETWORK_TIMEOUT);
    
    // 连接到服务器
    HINTERNET hConnect = WinHttpConnect(hSession, serverName.c_str(), port, 0);
    if (!hConnect) {
        WinHttpCloseHandle(hSession);
        lastError = "无法连接到服务器，错误代码: " + std::to_string(GetLastError());
        throw std::runtime_error("服务器连接失败");
    }
    
    // 创建请求
    HINTERNET hRequest = WinHttpOpenRequest(hConnect, L"GET", objectName.c_str(),
                                           nullptr, WINHTTP_NO_REFERER,
                                           WINHTTP_DEFAULT_ACCEPT_TYPES,
                                           WINHTTP_FLAG_SECURE);
    
    if (!hRequest) {
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);
        lastError = "无法创建HTTP请求，错误代码: " + std::to_string(GetLastError());
        throw std::runtime_error("HTTP请求创建失败");
    }
    
    // 发送请求
    BOOL bResults = WinHttpSendRequest(hRequest, WINHTTP_NO_ADDITIONAL_HEADERS, 0,
                                      WINHTTP_NO_REQUEST_DATA, 0, 0, 0);
    
    if (!bResults) {
        WinHttpCloseHandle(hRequest);
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);
        lastError = "发送HTTP请求失败，错误代码: " + std::to_string(GetLastError());
        throw std::runtime_error("HTTP请求发送失败");
    }
    
    // 接收响应
    bResults = WinHttpReceiveResponse(hRequest, nullptr);
    if (!bResults) {
        WinHttpCloseHandle(hRequest);
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);
        lastError = "接收HTTP响应失败，错误代码: " + std::to_string(GetLastError());
        throw std::runtime_error("HTTP响应接收失败");
    }
    
    // 检查状态码
    DWORD statusCode = 0;
    DWORD statusCodeSize = sizeof(statusCode);
    WinHttpQueryHeaders(hRequest, WINHTTP_QUERY_STATUS_CODE | WINHTTP_QUERY_FLAG_NUMBER,
                       WINHTTP_HEADER_NAME_BY_INDEX, &statusCode, &statusCodeSize, WINHTTP_NO_HEADER_INDEX);
    
    if (statusCode != 200) {
        WinHttpCloseHandle(hRequest);
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);
        lastError = "HTTP状态码错误: " + std::to_string(statusCode);
        throw std::runtime_error("HTTP状态码错误");
    }
    
    // 获取Date头信息
    wchar_t dateBuffer[256] = {0};
    DWORD dateBufferLength = sizeof(dateBuffer);
    if (WinHttpQueryHeaders(hRequest, WINHTTP_QUERY_DATE,
                           WINHTTP_HEADER_NAME_BY_INDEX, dateBuffer, &dateBufferLength, WINHTTP_NO_HEADER_INDEX)) {
        std::string dateStr = wstringToString(dateBuffer);
        serverTime = parseHttpDate(dateStr);
    } else {
        // 使用当前时间作为备选
        serverTime = time(nullptr);
    }
    
    // 读取响应内容
    std::string responseContent;
    DWORD dwSize = 0;
    DWORD dwDownloaded = 0;
    
    do {
        // 检查可用数据大小
        if (!WinHttpQueryDataAvailable(hRequest, &dwSize)) {
            break;
        }
        
        if (dwSize == 0) {
            break;
        }
        
        // 分配缓冲区
        char* pszOutBuffer = new char[dwSize + 1];
        ZeroMemory(pszOutBuffer, dwSize + 1);
        
        // 读取数据
        if (WinHttpReadData(hRequest, pszOutBuffer, dwSize, &dwDownloaded)) {
            responseContent.append(pszOutBuffer, dwDownloaded);
        }
        
        delete[] pszOutBuffer;
        
    } while (dwSize > 0);
    
    // 清理资源
    WinHttpCloseHandle(hRequest);
    WinHttpCloseHandle(hConnect);
    WinHttpCloseHandle(hSession);
    
    return responseContent;
}

std::string VersionChecker::extractVersion(const std::string& content) {
    // 使用正则表达式提取版本号
    std::regex pattern(R"(version:\s*([0-9]+\.[0-9]+\.[0-9]+))");
    std::smatch matches;
    
    if (std::regex_search(content, matches, pattern)) {
        std::string version = matches[1].str();
        return version;
    }
    
    lastError = "无法从内容中提取版本号，内容格式可能不正确";
    throw std::runtime_error("版本号解析失败");
}

bool VersionChecker::validateTime(time_t serverTime) {
    // 计算时间差
    time_t timeDiff = serverTime - RELEASE_TIMESTAMP;
    
    //AddLogInfo(LogLevel::Info, "[VersionChecker] 发布时间戳: " + std::to_string(RELEASE_TIMESTAMP));
    //AddLogInfo(LogLevel::Info, "[VersionChecker] 服务器时间戳: " + std::to_string(serverTime));
    //AddLogInfo(LogLevel::Info, "[VersionChecker] 时间差(秒): " + std::to_string(timeDiff));
    //AddLogInfo(LogLevel::Info, "[VersionChecker] 时间差(天): " + std::to_string(timeDiff / (24 * 60 * 60)));
    
    // 如果时间差超过最大使用时间，返回false
    return timeDiff <= MAX_USAGE_TIME;
}

int VersionChecker::getRemainingDays() {
    if (lastServerTime == 0) {
        return 0;
    }
    
    time_t timeDiff = lastServerTime - RELEASE_TIMESTAMP;
    time_t remainingSeconds = MAX_USAGE_TIME - timeDiff;
    
    if (remainingSeconds <= 0) {
        return 0;
    }
    
    return static_cast<int>(remainingSeconds / (24 * 60 * 60));
}

std::string VersionChecker::getFormattedErrorMessage(CheckResult result) {
    switch (result) {
        case CheckResult::SUCCESS:
            return "版本检查成功";
            
        case CheckResult::NETWORK_ERROR:
            return "网络连接失败，无法验证版本信息。\n\n详细错误：" + lastError + "\n\n请检查网络连接后重试。";
            
        case CheckResult::VERSION_MISMATCH:
            return "版本验证失败，当前版本不是最新版本。\n\n" + lastError + "\n\n请下载并安装最新版本。";
            
        case CheckResult::TIME_EXPIRED:
            return "软件使用期限已到期。\n\n" + lastError + "\n\n请更新到最新版本以继续使用。";
            
        case CheckResult::PARSE_ERROR:
            return "版本信息解析失败。\n\n详细错误：" + lastError + "\n\n服务器数据可能存在问题，请联系技术支持。";
            
        case CheckResult::UNKNOWN_ERROR:
        default:
            return "未知错误导致版本检查失败。\n\n详细错误：" + lastError + "\n\n请检查网络的连接状态或联系技术支持。";
    }
}

time_t VersionChecker::parseHttpDate(const std::string& dateStr) {
    // 解析HTTP日期格式，例如: "Wed, 21 Oct 2015 07:28:00 GMT"
    std::tm tm = {};
    std::istringstream ss(dateStr);
    
    // 尝试多种常见的HTTP日期格式
    ss >> std::get_time(&tm, "%a, %d %b %Y %H:%M:%S GMT");
    if (ss.fail()) {
        ss.clear();
        ss.str(dateStr);
        ss >> std::get_time(&tm, "%A, %d-%b-%y %H:%M:%S GMT");
        if (ss.fail()) {
            ss.clear();
            ss.str(dateStr);
            ss >> std::get_time(&tm, "%a %b %d %H:%M:%S %Y");
        }
    }
    
    if (!ss.fail()) {
        return std::mktime(&tm);
    }
    
    // 如果解析失败，返回当前时间
    return time(nullptr);
}

std::string VersionChecker::wstringToString(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();
    
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), (int)wstr.size(), 
                                         nullptr, 0, nullptr, nullptr);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), (int)wstr.size(), 
                       &strTo[0], size_needed, nullptr, nullptr);
    return strTo;
}

std::wstring VersionChecker::stringToWstring(const std::string& str) {
    if (str.empty()) return std::wstring();
    
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), (int)str.size(), nullptr, 0);
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, str.c_str(), (int)str.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

const std::string& VersionChecker::getVersion() {
    return CURRENT_VERSION;
} 