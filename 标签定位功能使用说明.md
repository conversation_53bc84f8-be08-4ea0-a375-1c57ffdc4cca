# 标签定位功能使用说明

## 概述

标签定位法是一个强大的脚本跳转功能，允许根据屏幕上的文字内容或直接指定的文本来动态定位到脚本中的特定位置执行。这个功能特别适合处理游戏或应用中动态变化的界面内容。

## 功能特点

- **双模式支持**：支持OCR识别模式和直接文本模式
- **无需映射表**：自动搜索脚本中的标签，无需预先建立映射关系
- **灵活跳转**：根据识别结果动态跳转到对应的执行位置
- **容错处理**：如果找不到对应标签，会继续执行后续命令

## 命令格式

### 方式一：ROI OCR识别模式
```
LABEL_LOCATE(x, y, width, height)
```

**参数说明：**
- `x, y`：ROI区域的左上角坐标
- `width, height`：ROI区域的宽度和高度

**执行流程：**
1. OCR识别指定ROI区域的文字内容
2. 在当前脚本中搜索 `LABEL:识别文字` 或 `#LABEL:识别文字`
3. 如果找到匹配的标签，跳转到该标签位置继续执行
4. 如果找不到，继续执行下一行命令

### 方式二：直接文本模式
```
LABEL_LOCATE(文本内容)
```

**参数说明：**
- `文本内容`：要搜索的标签文本

**执行流程：**
1. 直接使用提供的文本内容
2. 在当前脚本中搜索 `LABEL:文本内容` 或 `#LABEL:文本内容`
3. 如果找到匹配的标签，跳转到该标签位置继续执行
4. 如果找不到，继续执行下一行命令

## 标签定义格式

在脚本中定义标签时，支持两种格式：

```
LABEL:标签名称
```
或
```
#LABEL:标签名称
```

**注意事项：**
- 标签名称必须与LABEL_LOCATE命令中的文本完全匹配
- 标签可以放在脚本的任意位置
- 建议将相关的任务代码放在对应标签之后

## 使用示例

### 示例1：游戏任务动态识别

```
slice(1) {
    // OCR识别任务栏区域的任务名称
    LABEL_LOCATE(1050, 400, 230, 200)
    
    // 如果上面没找到匹配标签，执行默认操作
    MOUSE_MOVE_CLICK(500, 500)
    
    // 任务标签定义
    LABEL:燃犀之灯
    TEXT_MATCH(燃犀之灯, left, 1050, 400, 230, 200)
    TEXT_MATCH(燃犀之灯, left, 470, 270, 350, 480)
    MOUSE_LEFT()
    
    LABEL:猫儿偷鸡
    TEXT_MATCH(猫儿偷鸡, left, 1050, 400, 230, 200)
    DELAY(1000)
    MOUSE_LEFT()
    VISUAL_MATCH(lingxi, right)
    
    LABEL:小鸡咕咕
    TEXT_MATCH(小鸡咕咕, left, 1050, 400, 230, 200)
    MOUSE_MOVE_CLICK(800, 600)
}
```

### 示例2：直接文本跳转

```
slice(1) {
    // 直接跳转到指定任务
    LABEL_LOCATE(燃犀之灯)
    
    // 这行不会执行，因为会跳转到燃犀之灯标签
    MOUSE_MOVE(100, 100)
    
    LABEL:燃犀之灯
    TEXT_MATCH(燃犀之灯, left, 1050, 400, 230, 200)
    MOUSE_LEFT()
}
```

### 示例3：条件执行

```
slice(1) {
    // 根据屏幕内容动态执行不同任务
    LABEL_LOCATE(50, 50, 200, 30)
    
    // 如果没有找到匹配标签，执行默认流程
    DELAY(1000)
    MOUSE_MOVE_CLICK(400, 400)
    
    LABEL:新手任务
    MOUSE_MOVE_CLICK(100, 200)
    KEYBOARD_INPUT(hello)
    
    LABEL:高级任务
    VISUAL_MATCH(advanced_button, left)
    DELAY(2000)
    
    LABEL:特殊事件
    TEXT_MATCH(特殊, left, 0, 0, 800, 600)
    MOUSE_RIGHT()
}
```

## 最佳实践

### 1. ROI区域选择
- 选择文字清晰、稳定的区域进行OCR识别
- 避免包含过多干扰元素的区域
- 确保ROI区域大小适中，既不过小导致识别不全，也不过大导致识别混乱

### 2. 标签命名
- 使用有意义的标签名称，便于维护
- 标签名称应与游戏或应用中的实际文字内容保持一致
- 避免使用特殊字符或过长的标签名

### 3. 脚本组织
- 将标签和相关任务放在一起，提高可读性
- 在LABEL_LOCATE命令后面放置默认处理逻辑
- 使用注释说明每个标签的用途

### 4. 错误处理
- 为每个LABEL_LOCATE命令提供默认处理路径
- 考虑OCR识别可能的误差和失败情况
- 合理设置延迟时间，确保界面稳定后再执行识别

## 技术原理

1. **OCR识别**：使用PaddleOCR引擎识别指定区域的文字内容
2. **标签搜索**：在脚本内容中逐行搜索匹配的标签定义
3. **执行跳转**：找到匹配标签后，从该位置继续解析和执行脚本
4. **异步处理**：标签定位任务在专门的任务队列中异步执行

## 注意事项

- 标签定位功能需要OCR引擎正常工作
- ROI坐标应在屏幕有效范围内
- 标签名称区分大小写，需要精确匹配
- 建议在稳定的界面状态下执行标签定位
- 如果脚本内容过大，标签搜索可能需要一定时间

## 故障排除

### 常见问题

1. **OCR识别失败**
   - 检查ROI区域是否正确
   - 确认区域内文字是否清晰
   - 调整ROI大小和位置

2. **标签未找到**
   - 检查标签名称是否完全匹配
   - 确认标签格式是否正确（LABEL:或#LABEL:）
   - 查看日志了解具体的搜索过程

3. **跳转无效**
   - 检查脚本语法是否正确
   - 确认标签后的命令是否有效
   - 查看执行日志排查问题

### 调试建议

- 启用详细日志查看执行过程
- 使用简单的测试脚本验证功能
- 先测试直接文本模式，再测试OCR模式
- 分步调试，逐个验证各个环节 