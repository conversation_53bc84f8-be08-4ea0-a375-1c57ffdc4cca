#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#pragma once

#include <vector>
#include <string>
#include <thread>
#include <mutex>
#include <map>
#include <atomic>
#include <memory> // Required for std::unique_ptr
#include <utility> // Required for std::piecewise_construct, std::forward_as_tuple
#include <future> // Required for std::promise and std::future
#include <condition_variable>
#include <queue>
#include <chrono> // Required for time-related functionality in destructor

#include "ThreadSafeQueue.h"
#include "VMVision.h"
#include "VMTasks.h" // 包含 VMTasks.h 以使用 IVMTask

// Forward declarations  
enum class VMConnectionState : int;
enum class VMTaskState : int;
enum class SliceState : int;
#include "VMStateManager.h" // 需要完整定义因为内联方法使用了VMStateManager
#include "Core/ThreadPool.h" // 线程池实现
#include "VNCControl.h" // For InputTask struct and other declarations
#include "Core/UnifiedThreadManager.h" // 统一线程管理器

// Forward declaration for VNCControl class - the VNCControl.h doesn't define this class
class VNCControl;

// 枚举，表示虚拟机控制器线程的状态
enum class VMThreadState {
    STOPPED,                // 已停止
    INITIALIZING,           // 正在初始化 (尝试连接VNC)
    RUNNING,                // VNC已连接，线程运行中 (可以认为是就绪/空闲状态)
    // PROCESSING_TASK,     // (可选) 明确表示正在处理一个任务, 如果需要可以取消注释
    PAUSED,                 // 已暂停 (如果支持)
    ERROR_CONNECTION_FAILED // VNC连接失败
};

// 结构体，用于存储每个虚拟机控制器的信息
struct VMControllerInfo {
    std::string vmName; // 虚拟机名称
    std::string ip;     // IP address of the VM
    int port;           // Port for VNC connection
    std::string password; // Password for VNC connection
    std::weak_ptr<rfbClient> vncClient; // VNC 客户端弱引用
    // 控制器线程已迁移到统一线程管理器中
    //mutable std::mutex stateMutex; // 将 stateMutex 标记为 mutable
	std::promise<bool> initialized_promise; // 用于线程间通信的 promise
	std::shared_future<bool> initialized_future; // 共享的 future，用于线程间通信

    std::unique_ptr<ThreadSafeQueue<std::shared_ptr<IVMTask>>> taskQueue; // 修改: 任务队列类型
    std::atomic<VMThreadState> currentState; // 当前状态
    // std::atomic<size_t> currentTaskIndex; // 考虑移除或调整
    // std::vector<InputTask> taskHistory; // 考虑移除或调整
    std::atomic<bool> successfullyConnected{false}; // Track VNC connection success
    std::atomic<bool> stopRequested{false}; // 标记是否请求停止线程
    
    std::unique_ptr<VMVision> visionProcessor; // 添加 VMVision 对象
    std::unique_ptr<VNCControl> vncControl; // VNC控制实例

    // Mutex and CV for taskQueue operations by DispatchCenter
    std::mutex taskQueueMutex_; // 
    std::condition_variable taskQueueCV_; // 

    // 新的轮询器设计：切片系统
    struct SliceInfo {
        std::unique_ptr<ThreadSafeQueue<std::shared_ptr<IVMTask>>> taskQueue;
        // 移除状态变量，统一使用VMStateManager管理状态
        // std::atomic<bool> isWaitingForScreenStill{false};     // 已迁移到VMStateManager
        // std::atomic<bool> isWaitingForVisualMatch{false};     // 已迁移到VMStateManager  
        // std::atomic<bool> isPaused{false};                    // 已迁移到VMStateManager
        
        SliceInfo() {
            taskQueue = std::make_unique<ThreadSafeQueue<std::shared_ptr<IVMTask>>>(500);
        }
        
        // 禁用拷贝，允许移动
        SliceInfo(const SliceInfo&) = delete;
        SliceInfo& operator=(const SliceInfo&) = delete;
        SliceInfo(SliceInfo&&) = default;
        SliceInfo& operator=(SliceInfo&&) = default;
        
        // 检查切片是否可以执行任务（使用VMStateManager统一状态管理）
        bool canExecuteTask(const std::string& vmName, int sliceNumber) const {
            auto* stateManager = VMStateManager::getInstance();
            if (!stateManager) {
                return false; // 如果状态管理器不可用，认为无法执行任务
            }
            
            SliceState state = stateManager->getSliceState(vmName, sliceNumber);
            
            // 只有在IDLE_STATE和RUNNING_STATE时才能执行任务
            // 暂停、等待状态都不能执行新任务
            return (state == SliceState::IDLE_STATE || state == SliceState::RUNNING_STATE);
        }
    };
    
    std::map<int, std::unique_ptr<SliceInfo>> sliceQueues;  // 每个切片的任务队列
    int currentSliceIndex = 0;  // 当前轮询到的切片索引（在sliceOrder中的索引）
    std::vector<int> sliceOrder;  // 切片轮询顺序
    
    // 新的轮询器架构：同步轮询器
    // 任务轮询器线程已迁移到统一线程管理器中
    std::atomic<bool> pollerStopRequested{false};          // 轮询器停止标志
    std::atomic<bool> pollerPaused{false};                 // 轮询器暂停标志
    std::atomic<bool> pollerEndLogged{false};              // 轮询器结束日志已记录标志
    std::mutex pollerMutex_;                               // 轮询器互斥锁
    std::condition_variable pollerCV_;                     // 轮询器条件变量
    
    // 任务执行同步：轮询器每次只获取一个任务，等任务执行完再轮询下一个
    std::mutex executionMutex_;                            // 任务执行互斥锁
    std::condition_variable executionCV_;                  // 任务执行条件变量
    std::atomic<bool> isExecutingTask{false};              // 是否正在执行任务
    std::shared_ptr<IVMTask> currentExecutingTask{nullptr}; // 当前执行的任务
    int currentExecutingSlice{-1};                         // 当前执行任务的切片号
    
    // 等待任务完成通知机制
    std::mutex waitTaskMutex_;                             // 等待任务互斥锁
    std::condition_variable waitTaskCV_;                   // 等待任务条件变量
    
    // 切片配置信息
    int sliceRows = 1;    // 切片行数，默认1行
    int sliceCols = 2;    // 切片列数，默认2列

    // 旧的中间队列系统（将被移除）
    struct TaskWithSlice {
        std::shared_ptr<IVMTask> task;
        int sliceNumber;
        
        TaskWithSlice(std::shared_ptr<IVMTask> t, int slice) : task(t), sliceNumber(slice) {}
    };
    std::unique_ptr<ThreadSafeQueue<TaskWithSlice>> intermediateQueue;  // 保留以兼容现有代码，但不再使用

    // 脚本执行相关成员变量
    std::string scriptContent;                  // 当前脚本内容
    std::vector<std::string> scriptLines;       // 脚本行数组
    std::atomic<size_t> currentLineIndex{0};    // 当前执行的行索引
    std::atomic<int> currentSliceForScript{-1}; // 当前脚本执行的切片号

    // 默认构造函数，用于 map::emplace
    VMControllerInfo() : currentState(VMThreadState::STOPPED), successfullyConnected(false) {
        taskQueue = std::make_unique<ThreadSafeQueue<std::shared_ptr<IVMTask>>>(100); // Initialize with capacity of 100
        
        // 初始化中间队列（保留兼容性）
        intermediateQueue = std::make_unique<ThreadSafeQueue<TaskWithSlice>>(200);
        
        // 初始化默认切片配置（1行2列 = 切片1,2）
        initializeSlices(1, 2);
    }
    
    // 初始化切片系统
    void initializeSlices(int rows, int cols) {
        // 使用 ScreenFrameDistributor 的验证和工具函数
        if (!ScreenFrameDistributor::isValidSliceConfiguration(rows, cols)) {
            AddLogInfo(LogLevel::Error, "无法初始化切片系统: " + 
                       ScreenFrameDistributor::getSliceConfigurationDescription(rows, cols));
            return;
        }
        
        sliceRows = rows;
        sliceCols = cols;
        
        // 清空现有数据
        sliceQueues.clear();
        
        // 使用 ScreenFrameDistributor 生成切片顺序
        sliceOrder = ScreenFrameDistributor::generateSliceOrder(rows, cols);
        
        // 初始化切片队列和状态
        for (int sliceNumber : sliceOrder) {
            sliceQueues[sliceNumber] = std::make_unique<SliceInfo>();
            // 切片状态由VMStateManager统一管理，在addVMController时初始化
        }
        
        // 重置轮询索引
        currentSliceIndex = 0;
    }

    // 显式删除拷贝构造函数和拷贝赋值运算符，因为包含不可拷贝的成员 (mutex, thread, unique_ptr, promise, condition_variable)
    VMControllerInfo(const VMControllerInfo&) = delete;
    VMControllerInfo& operator=(const VMControllerInfo&) = delete;

    // 允许移动操作
    VMControllerInfo(VMControllerInfo&& other) noexcept
        : vmName(std::move(other.vmName)),
        ip(std::move(other.ip)), 
        port(other.port),         
        password(std::move(other.password)), 
        vncClient(other.vncClient),
        // controllerThread已迁移到统一线程管理器中
        initialized_promise(std::move(other.initialized_promise)),
        initialized_future(std::move(other.initialized_future)),
        taskQueue(std::move(other.taskQueue)),
        currentState(other.currentState.load()),
        successfullyConnected(other.successfullyConnected.load()),
        stopRequested(other.stopRequested.load()), 
        visionProcessor(std::move(other.visionProcessor)),
        vncControl(std::move(other.vncControl)),
        sliceQueues(std::move(other.sliceQueues)),
        currentSliceIndex(other.currentSliceIndex),
        sliceOrder(std::move(other.sliceOrder)),
        // taskPollerThread已迁移到统一线程管理器中
        pollerStopRequested(other.pollerStopRequested.load()),
        pollerPaused(other.pollerPaused.load()),
        pollerEndLogged(other.pollerEndLogged.load()),
        sliceRows(other.sliceRows),
        sliceCols(other.sliceCols),
        // 脚本执行相关成员变量
        scriptContent(std::move(other.scriptContent)),
        scriptLines(std::move(other.scriptLines)),
        currentLineIndex(other.currentLineIndex.load()),
        currentSliceForScript(other.currentSliceForScript.load())
    {
        // Mutexes and CVs are not moved; they are default-constructed or re-initialized if needed.
        // Source object's threads should be joined or detached before destruction if they use old mutexes/CVs.
        other.vncClient = std::weak_ptr<rfbClient>();
        other.port = 0; // Reset port
        other.currentSliceIndex = 1; // Reset slice index
        other.currentLineIndex = 0;
        other.currentSliceForScript = -1;
    }

    VMControllerInfo& operator=(VMControllerInfo&& other) noexcept {
        if (this != &other) {

            // Move data members
            vmName = std::move(other.vmName);
            ip = std::move(other.ip); 
            port = other.port;       
            password = std::move(other.password); 
            vncClient = other.vncClient;
            // controllerThread已迁移到统一线程管理器中
            initialized_promise = std::move(other.initialized_promise);
            initialized_future = std::move(other.initialized_future);
            taskQueue = std::move(other.taskQueue);
            currentState = other.currentState.load();
            successfullyConnected = other.successfullyConnected.load();
            stopRequested = other.stopRequested.load(); 
            visionProcessor = std::move(other.visionProcessor);
            vncControl = std::move(other.vncControl); 
            sliceQueues = std::move(other.sliceQueues);
            currentSliceIndex = other.currentSliceIndex;
            sliceOrder = std::move(other.sliceOrder);
            // taskPollerThread已迁移到统一线程管理器中
            pollerStopRequested = other.pollerStopRequested.load();
            pollerPaused = other.pollerPaused.load();
            pollerEndLogged = other.pollerEndLogged.load();
            sliceRows = other.sliceRows;
            sliceCols = other.sliceCols;
            
            // Move script execution related members
            scriptContent = std::move(other.scriptContent);
            scriptLines = std::move(other.scriptLines);
            currentLineIndex = other.currentLineIndex.load();
            currentSliceForScript = other.currentSliceForScript.load();

            // Reset source object's pointers/handles
            other.vncClient = std::weak_ptr<rfbClient>();
            other.port = 0;
            other.currentSliceIndex = 0;
            other.sliceRows = 1;
            other.sliceCols = 2;
            other.currentLineIndex = 0;
            other.currentSliceForScript = -1;
        }
        return *this;
    }

    // 析构函数会自动处理 unique_ptr 和 joinable 的 thread
    ~VMControllerInfo() {
        // 设置停止标志
        stopRequested.store(true);
        pollerStopRequested.store(true);
        
        // 通知轮询器线程停止
        try {
            pollerCV_.notify_all();
        } catch (...) {
            // 忽略异常
        }
        
        // 线程由统一线程管理器管理，无需手动处理
        
        // 不进行任何资源清理，让smart pointer自动处理
        // 避免在析构函数中调用可能阻塞或抛出异常的操作
    }
};

class DispatchCenter {
private: // For Singleton and internal members
    static DispatchCenter* instance_;
    static std::mutex instanceMutex_;

    std::map<std::string, VMControllerInfo> vmControllers_;
    std::mutex controllersMutex_; // Protects vmControllers_ map
    mutable std::condition_variable controllersCV_; // For vmControllers_ map related waits if any
    // 线程池已迁移到统一线程管理器中
    std::atomic<bool> running_; // Tracks if the DispatchCenter is actively running
    
    // 统一线程管理器引用
    UnifiedThreadManager& threadManager_; // 统一线程管理器的引用

    // Private constructor for singleton
    DispatchCenter();


    

    
    // 支持停止信号的线程函数版本（用于统一线程管理器）
    void vmControllerThreadWithStopToken(VMControllerInfo* controllerInfo);
    void taskPollerThreadWithStopToken(VMControllerInfo* controllerInfo);

    // Helper to find a controller (non-const version)
    VMControllerInfo* findVMController(const std::string& vmName, std::chrono::milliseconds timeout = std::chrono::milliseconds(5000));
    // Helper to find a controller (const version)
    const VMControllerInfo* findVMController(const std::string& vmName, std::chrono::milliseconds timeout = std::chrono::milliseconds(5000)) const;

    // 确保线程管理器已初始化
    bool ensureThreadManagerInitialized();

    // Internal task processing methods
    void processDirectKeyboardMouseTask(const std::string& vmName, VMControllerInfo* controllerInfo, class DirectKeyboardMouseActionTask* task);
    void processImageMatchTask(const std::string& vmName, VMControllerInfo* controllerInfo, class ImageMatchVisionTask* task);
    void processWordsMatchTask(const std::string& vmName, VMControllerInfo* controllerInfo, class WordsMatchVisionTask* task);
    void processTextRecognizeTask(const std::string& vmName, VMControllerInfo* controllerInfo, class TextRecognizeVisionTask* task);
    void processWaitForScreenStillTask(const std::string& vmName, VMControllerInfo* controllerInfo, class WaitForScreenStillTask* task);
    void processWaitForVisualMatchTask(const std::string& vmName, VMControllerInfo* controllerInfo, class WaitForVisualMatchTask* task);
    void processLabelLocateTask(const std::string& vmName, VMControllerInfo* controllerInfo, class LabelLocateTask* task);

    // Unlocked versions of methods
    void addVMController_unlocked(const std::string& vmName, const std::string& ip, int port, const std::string& password);
    bool isVMInitialized_unlocked(const std::string& vmName) const;
    VMControllerInfo* findVMController_unlocked(const std::string& vmName, std::chrono::milliseconds timeout = std::chrono::milliseconds(0)); // Default timeout 0 for unlocked version
    const VMControllerInfo* findVMController_unlocked(const std::string& vmName, std::chrono::milliseconds timeout = std::chrono::milliseconds(0)) const; // Default timeout 0 for unlocked version

    // 内部调度器函数（私有函数，仅供内部使用）

    void executeTask(std::shared_ptr<IVMTask> task, int sliceNumber, VMControllerInfo* controllerInfo);

    // 内部方法
    void startHotReloadWatcher();
    void stopHotReloadWatcher();
    void checkFileChanges();
    void notifyListeners(const std::string& key, const ConfigItem* oldItem, const ConfigItem* newItem);
    nlohmann::json configItemToJson(const ConfigItem& item) const;
    ConfigItem jsonToConfigItem(const std::string& key, const nlohmann::json& json, ConfigType type) const;
    ConfigType getConfigTypeFromCategory(const std::string& category) const;
    void loadDefaultConfigs();
    
    // 任务重试相关（已禁用重试机制）
    // void requeueFailedTask(const std::string& vmName, int sliceNumber, std::shared_ptr<IVMTask> task);

public:
    static DispatchCenter* getInstance();

    ~DispatchCenter();

    // 禁用拷贝构造函数和赋值运算符
    DispatchCenter(const DispatchCenter&) = delete;
    DispatchCenter& operator=(const DispatchCenter&) = delete;

    // Config loading
    // 初始化方法
    bool initialize(const std::vector<std::pair<std::string, int>>& vm_configs);

    // 管理虚拟机控制器
    bool removeVMController(const std::string& vmName);
    void stopAllVMControllers();
    void disconnectAllVMs(); // 添加声明
    
    // 安全的停止函数，只设置停止标志，不进行线程操作
    void stopAllVMControllersSafe();
    
    // 清理函数，用于程序退出时
    void cleanup();

    // 获取虚拟机状态和信息
    VMControllerInfo* getVMControllerInfo(const std::string& vmName);
    VMThreadState getVMState(const std::string& vmName) const;
    bool isVMInitialized(const std::string& vmName) const; // 添加声明并设为const
    bool isVMConnected(const std::string& vmName) const;
    bool isVMPaused(const std::string& vmName) const; // Declaration updated/ensured
    void pauseVM(const std::string& vmName);          // New declaration
    void resumeVM(const std::string& vmName);         // New declaration
    void pauseSlice(const std::string& vmName, int sliceNumber);   // 暂停指定切片
    void resumeSlice(const std::string& vmName, int sliceNumber);  // 恢复指定切片
    void pauseAllSlices(const std::string& vmName);               // 暂停所有切片
    void resumeAllPausedSlices(const std::string& vmName);         // 恢复所有暂停的切片
    void pauseTaskPoller(const std::string& vmName);              // 暂停任务轮询器
    void resumeTaskPoller(const std::string& vmName);             // 恢复任务轮询器
    void clearPendingTasks(const std::string& vmName); // 新增：声明清空待处理任务的函数
    void clearAllTasksAndResetSlices(const std::string& vmName); // 新增：清除所有任务并重置切片状态
    
    // 新增：获取切片状态的方法
    bool isSlicePaused(const std::string& vmName, int sliceNumber) const;
    bool isSliceRunning(const std::string& vmName, int sliceNumber) const;
    bool isSliceWaitingForScreenStill(const std::string& vmName, int sliceNumber) const;
    bool isSliceWaitingForVisualMatch(const std::string& vmName, int sliceNumber) const;

    // 新增：失败任务重新入队
    void reEnqueueFailedTask(const std::string& vmName, std::shared_ptr<IVMTask> task);
    
    // 新增：同步轮询器控制方法
    void stopTaskPoller(const std::string& vmName);               // 停止任务轮询器（用于完全停止）
    void startTaskPoller(const std::string& vmName);              // 重新启动任务轮询器
    void clearSliceWaitingStates(const std::string& vmName);      // 清除所有切片的等待状态
    void notifySliceWaitComplete(const std::string& vmName, int sliceNumber, VMTaskType taskType); // 等待任务完成通知
    bool isSliceWaiting(const std::string& vmName, int sliceNumber) const; // 检查切片是否在等待
    
    VMVision* getVisionProcessor(const std::string& vmName, std::chrono::milliseconds timeout = std::chrono::milliseconds(5000));
    std::vector<std::string> getAllVMNames() const;
    // 获取已连接的虚拟机名称列表
    std::vector<std::string> getConnectedVMNames() const;
    size_t getActiveVMCount() const;
    bool reconnectVMController(const std::string& vmName);
    bool hasTasks() const;

    // 检测画面是否静止 - 通过比较固定区域的文字识别结果
    void waitForScreenStill(
        const std::string& vmName,
        int sliceNumber,
        cv::Rect roi,
        int checkIntervalSeconds = 1,
        int maxTimeoutSeconds = 30
    );

    // 键盘鼠标操作
    std::future<KeyboardMouseResult> addDirectKeyboardMouseActionTask(const std::string& vmName, const std::vector<InputTask>& actions, int sliceNumber);
    std::future<VisionMatchResult> addImageMatchTask(
        const std::string& vmName,
        const std::string& templateName,
        double threshold,
        const std::string& imagePath, // 模板图像文件的路径
        cv::Rect roi, // cv::Rect 是 OpenCV 中的一个矩形结构体，表示屏幕上要进行搜索的兴趣区域 (Region of Interest)。如果设置了 ROI，匹配算法将只在这个区域内搜索，从而提高效率并减少误报。
        int sliceNumber,
        const std::string& mouseClickType = "left" // 新增：鼠标点击类型，默认为左键
    );
    std::future<WordsMatchResult> addWordsMatchTask(
        const std::string& vmName,
        const std::vector<std::string>& wordsToMatch,
        double threshold,
        cv::Rect roi,
        int sliceNumber,
        const std::string& mouseClickType = "left" // 新增：鼠标点击类型，默认为左键
    );
    std::future<TextRecognizeResult> addTextRecognizeTask(const std::string& vmName, cv::Rect roi, int sliceNumber);
    std::future<WaitForScreenStillResult> addWaitForScreenStillTask(const std::string& vmName, int sliceNumber, int x, int y, int width, int height, int checkIntervalSeconds, int maxTimeoutSeconds);
    std::future<WaitForVisualMatchResult> addWaitForVisualMatchTask(const std::string& vmName, int sliceNumber, const std::string& templateName, int checkIntervalSeconds, int maxTimeoutSeconds, double threshold = 0.7);

    // 标签定位任务 - Label Locate Tasks
    std::future<LabelLocateResult> addLabelLocateTask(const std::string& vmName, cv::Rect roi, int sliceNumber, const std::string& scriptContent);
    std::future<LabelLocateResult> addLabelLocateTask(const std::string& vmName, const std::string& targetText, int sliceNumber, const std::string& scriptContent);

    // 脚本解析器（公有函数，供外部调用）
    void executeScript(const std::string& vmName, const std::string& scriptContent);
    
    // 切片配置管理
    void setVMSliceConfiguration(const std::string& vmName, int rows, int cols);
    std::pair<int, int> getVMSliceConfiguration(const std::string& vmName) const;
    
    // 检查并暂停虚拟机脚本执行（当所有切片暂停或有切片暂停且其他切片无任务时）
    void checkAndPauseVMIfNeeded(VMControllerInfo* controllerInfo, const std::string& vmName);
    
    // 检查并恢复虚拟机脚本执行（当有切片恢复且有可执行任务时）
    void checkAndResumeVMIfNeeded(VMControllerInfo* controllerInfo, const std::string& vmName);
    

};
