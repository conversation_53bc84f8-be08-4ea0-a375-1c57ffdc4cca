# 坐标系统修复总结

## 🎯 **修复目标**
确保所有命令和任务都正确处理基于切片左上角的坐标系统，使得：
1. **输入坐标**：脚本中的坐标被视为切片内的相对坐标
2. **内部处理**：自动转换为全局屏幕坐标进行操作
3. **返回坐标**：视觉匹配结果转换回切片内坐标给脚本使用

## ✅ **已完成的修复**

### 1. **坐标转换辅助函数** (DispatchCenter.h/cpp)
- `convertSliceToGlobalCoordinate()` - 切片坐标 → 全局坐标
- `convertSliceToGlobalROI()` - 切片ROI → 全局ROI  
- `convertGlobalToSliceCoordinate()` - 全局坐标 → 切片坐标
- `convertSliceInputTasksToGlobal()` - 切片输入任务 → 全局输入任务

### 2. **键盘鼠标任务修复** (DispatchCenter.cpp)
- ✅ `processDirectKeyboardMouseTask()` - 直接键盘鼠标任务坐标转换
- ✅ `processTitleBarKeyboardMouseTask()` - 标题栏键盘鼠标任务坐标转换

### 3. **视觉匹配任务修复** (DispatchCenter.cpp)
- ✅ `processImageMatchTask()` - 图像匹配任务
  - ROI转换：切片ROI → 全局ROI
  - 结果转换：全局匹配坐标 → 切片坐标（返回给脚本）
  - 鼠标操作：使用全局坐标点击

### 4. **文字匹配任务修复** (DispatchCenter.cpp)
- ✅ `processWordsMatchTask()` - 文字匹配任务
  - ROI转换：切片ROI → 全局ROI
  - 结果转换：全局匹配坐标 → 切片坐标（返回给脚本）
  - 鼠标操作：使用全局坐标点击

### 5. **文本识别任务修复** (DispatchCenter.cpp)
- ✅ `processTextRecognizeTask()` - 文本识别任务
  - ROI转换：切片ROI → 全局ROI

### 6. **等待画面静止任务修复** (DispatchCenter.cpp)
- ✅ `processWaitForScreenStillTask()` - 等待画面静止任务
  - ROI转换：切片ROI → 全局ROI

## 🔧 **修复工作原理**

### 坐标转换流程：
```
脚本命令坐标 (切片内) 
    ↓ [convertSliceToGlobalCoordinate]
全局屏幕坐标 (实际操作)
    ↓ [视觉处理/鼠标操作]
全局匹配结果坐标
    ↓ [convertGlobalToSliceCoordinate] 
切片内坐标 (返回给脚本)
```

### 示例说明：
- **MOUSE_MOVE(100, 200)** 在切片2上：
  - 输入：切片内坐标 (100, 200)
  - 转换：全局坐标 (100+切片2偏移X, 200+切片2偏移Y)
  - 执行：鼠标移动到全局坐标位置

- **VISUAL_MATCH(img, left, 0.8)** 在切片1上：
  - 搜索：整个切片1区域（自动转换ROI）
  - 匹配：返回全局坐标
  - 点击：使用全局坐标点击
  - 返回：转换为切片1内坐标给脚本

## 📋 **支持的命令**

所有命令现在都正确支持基于切片的坐标系统：

### 🖱️ **鼠标操作命令**
- ✅ `MOUSE_MOVE(x, y)` - 移动到切片内坐标
- ✅ `MOUSE_LEFT(x, y)` - 点击切片内坐标  
- ✅ `MOUSE_RIGHT(x, y)` - 右键点击切片内坐标
- ✅ `MOUSE_DOUBLE(x, y)` - 双击切片内坐标
- ✅ `VISION_MOUSE_DRAG(img, x, y, w, h, x1, y1)` - 视觉拖动

### 🔍 **视觉识别命令** 
- ✅ `VISUAL_MATCH(templateName, clickType, threshold)` - 视觉匹配
- ✅ `TEXT_MATCH(text, clickType, x, y, w, h, threshold)` - 文字匹配
- ✅ `TEXT_RECOGNIZE(x, y, width, height)` - 文本识别

### ⚙️ **流程控制命令**
- ✅ `LABEL_LOCATE(x, y, width, height)` - 标签定位（ROI模式）
- ✅ `LABEL_LOCATE(targetText)` - 标签定位（文本模式）
- ✅ `WAIT_FOR_SCREEN_STILL(x, y, w, h, interval, timeout)` - 等待画面静止

### ⌨️ **键盘操作命令**
- ✅ `KEYBOARD_INPUT(key1, key2, ...)` - 连续键盘输入（带随机间隔）
- ✅ `TITLEBAR_KEYBOARD_INPUT(key1, key2, ...)` - 标题栏键盘输入

### ⏱️ **辅助命令**
- ✅ `DELAY(milliseconds)` - 延时等待

## 🎯 **最终效果**

现在无论在哪个切片上执行脚本命令：
1. **同样的坐标输入** → **相同的切片内位置**
2. **自动坐标转换** → **准确的全局操作**  
3. **正确的结果返回** → **可重用的脚本**

例如：`MOUSE_MOVE(100, 200)` 在任何切片上都会移动到该切片的 (100, 200) 位置，而不是屏幕的绝对坐标。

## 🔍 **测试建议**

建议测试以下场景确认修复效果：
1. 在不同切片上执行相同的鼠标操作命令
2. 在不同切片上执行视觉匹配命令  
3. 验证返回的坐标是否为切片内坐标
4. 验证点击操作是否准确到达目标位置 