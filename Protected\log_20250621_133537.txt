【info】[VersionChecker] 当前版本: 1.0.0 [13:35]
【info】[VersionChecker] 最新版本: 1.0.0 [13:35]
【info】[ThreadPool] 线程池创建成功，活跃线程数: 4 [13:35]
【info】[ThreadPool] 线程池创建成功，活跃线程数: 4 [13:35]
【info】[主程序] 正在启动所有虚拟机... [13:59]
【info】[主程序] 清理 DispatchCenter 资源 (停止所有VM控制器) [13:59]
【info】模板路径映射加载完成, 共 6 项 [13:59]
【warn】[VisionConfig] 文件不存在: ./screen/closeicon_1750067451.jpg [13:59]
【info】[VisionConfig] 成功加载 6 项配置 (其中 1 个文件不存在) [13:59]
【info】[主程序] 自动加载匹配设置界面配置成功 [13:59]
【info】[VNCControl] 已经连接到 127.0.0.1:5902 [13:59]
【info】[VNCControl] 已经连接到 127.0.0.1:5901 [13:59]
【info】[VMVision] 成功主动获取了新帧 [13:59]
【info】[VMVision] 连接状态正常 [13:59]
【info】[VM1] 视觉处理器连接正常，等待匹配模板··· [13:59]
【info】[主程序] VM1 视觉线程启动成功 [13:59]
【info】[VMVision] 成功主动获取了新帧 [13:59]
【info】[VMVision] 连接状态正常 [13:59]
【info】[VM2] 视觉处理器连接正常，等待匹配模板··· [13:59]
【info】[主程序] VM2 视觉线程启动成功 [13:59]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [13:59]
【info】[主程序] 最终状态：所有 2 个虚拟机连接成功 [13:59]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [13:59]
【warn】[VMVision] VM2 收到新帧: 1920x1080, 通道: 3 [13:59]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [13:59]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [13:59]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:00]
【warn】[VMVision] VM2 收到新帧: 1920x1080, 通道: 3 [14:00]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:00]
【debug】[DispatchCenter] VM2: 主循环运行中，总切片数: 2 [14:00]
【debug】[DispatchCenter] VM1: 主循环运行中，总切片数: 2 [14:00]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:00]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:00]
【warn】[VMVision] VM2 收到新帧: 1920x1080, 通道: 3 [14:00]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:00]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:00]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:01]
【warn】[VMVision] VM2 收到新帧: 1920x1080, 通道: 3 [14:01]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:01]
【debug】[DispatchCenter] VM2: 主循环运行中，总切片数: 2 [14:01]
【debug】[DispatchCenter] VM1: 主循环运行中，总切片数: 2 [14:01]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:01]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:01]
【warn】[VMVision] VM2 收到新帧: 1920x1080, 通道: 3 [14:01]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:01]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:01]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:02]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:02]
【warn】[VMVision] VM2 收到新帧: 1920x1080, 通道: 3 [14:02]
【debug】[DispatchCenter] VM2: 主循环运行中，总切片数: 2 [14:02]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:02]
【debug】[DispatchCenter] VM1: 主循环运行中，总切片数: 2 [14:02]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:02]
【info】在虚拟机 [VM1] 上执行任务 [14:02]
【info】正在执行任务 [1/1]: 主线任务1-30 [14:02]
【info】[ScriptParser] VM1: 可用切片号: 1 2  [14:02]
【info】[ScriptParser] 开始解析切片 1 的任务 [14:02]
【warn】[ScriptParser] 第 8 行: 未知或未实现的命令 'ABEL_LOCATE' (完整行: 'ABEL_LOCATE(1050,400,230,200)') [14:02]
【warn】[ScriptParser] 第 9 行: 未知或未实现的命令 '##1级任务' (完整行: '##1级任务') [14:02]
【warn】[ScriptParser] 第 11 行: 未知或未实现的命令 'LABEL:燃犀之灯' (完整行: 'LABEL:燃犀之灯') [14:02]
【info】[DispatchCenter] VM1: 从切片 1 取出任务，任务类型: 3 [14:02]
【info】[DispatchCenter] VM1: 开始执行切片 1 的任务，类型: 3 [14:02]
【info】[DispatchCenter] VM1: 执行文字匹配任务 [14:02]
【info】使用共享锁读取帧数据 [14:02]
【err】[VMVision] VM1使用切片编号：1 [14:02]
【debug】切片1: frame=(2560,1440), grid=(1,2), sliceSize=(1280,1440), logicalRect=(0,0,1,1) [14:02]
【info】[VMVision] VM1 正在初始化OCR引擎... [14:02]
【info】[VMVision] VM1 OCR引擎初始化成功 [14:02]
【debug】[VMVision] VM1 使用ROI: (1050,400,230,200) [14:02]
【warn】[VMVision] VM1开始进行文字匹配：燃犀之灯 [14:02]
【info】[VMVision] VM1[ocr] 查找文本燃犀之灯 [14:02]
【info】[VMVision] VM1[ocr] 使用配置高精度配置 [14:02]
【info】VM1 [OCR] 文本在切片 0 中的位置: (106, 31) [14:02]
【info】[VMVision] VM1识别燃犀之灯[ocr] 最佳匹配结果106,31 [14:02]
【info】VM1 [OCR] 文本 '燃犀之灯' 已找到，在 (1156, 431) with confidence 0.818639 [14:02]
【info】[DispatchCenter] VM1: 文字匹配成功，移动鼠标到位置 (1156, 431) [14:02]
【info】操作1156:431 [14:02]
【debug】[DispatchCenter] VM1: 鼠标移动完成 [14:02]
【info】操作0:0 [14:02]
【info】[DispatchCenter] VM1: 鼠标左击完成 [14:02]
【info】[DispatchCenter] VM1: 切片 1 的任务执行完成 [14:02]
【warn】[ScriptParser] 第 20 行: 未知或未实现的命令 'LABEL:红月之照' (完整行: 'LABEL:红月之照') [14:02]
【warn】[ScriptParser] 第 26 行: 未知或未实现的命令 'LABEL:月下织娘' (完整行: 'LABEL:月下织娘') [14:02]
【info】[DispatchCenter] VM1: 从切片 1 取出任务，任务类型: 3 [14:02]
【info】[DispatchCenter] VM1: 开始执行切片 1 的任务，类型: 3 [14:02]
【info】[DispatchCenter] VM1: 执行文字匹配任务 [14:02]
【info】使用共享锁读取帧数据 [14:02]
【err】[VMVision] VM1使用切片编号：1 [14:02]
【debug】切片1: frame=(2560,1440), grid=(1,2), sliceSize=(1280,1440), logicalRect=(0,0,1,1) [14:02]
【debug】[VMVision] VM1 使用ROI: (470,270,350,480) [14:02]
【warn】[VMVision] VM1开始进行文字匹配：燃犀之灯 [14:02]
【info】[VMVision] VM1[ocr] 查找文本燃犀之灯 [14:02]
【warn】[VMVision] VM2 收到新帧: 1920x1080, 通道: 3 [14:02]
【info】[VMVision] VM1[ocr] 使用配置高精度配置 [14:02]
【info】VM1 [OCR] 文本 '燃犀之灯' 没有找到. 置信度: 0.000000 [14:02]
【warn】[DispatchCenter] VM1: TEXT_MATCH匹配失败，暂停切片 1 [14:02]
【info】[DispatchCenter] VM1: 切片 1 已暂停（匹配失败） [14:02]
【info】[DispatchCenter] VM1: 切片 1 的任务执行完成 [14:02]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:02]
【warn】[ScriptParser] 第 33 行: 未知或未实现的命令 'LABEL:蒲家村落' (完整行: 'LABEL:蒲家村落') [14:02]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:02]
【warn】[ScriptParser] 第 39 行: 未知或未实现的命令 'LABEL:厨娘小宝' (完整行: 'LABEL:厨娘小宝') [14:02]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:02]
【warn】[ScriptParser] 第 46 行: 未知或未实现的命令 'LABEL:木匠爷爷' (完整行: 'LABEL:木匠爷爷') [14:02]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:02]
【warn】[ScriptParser] 第 52 行: 未知或未实现的命令 'LABEL:失控木人' (完整行: 'LABEL:失控木人') [14:02]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:02]
【warn】[ScriptParser] 第 63 行: 未知或未实现的命令 'LABEL:木花城' (完整行: 'LABEL:木花城') [14:02]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:02]
【warn】[ScriptParser] 第 69 行: 未知或未实现的命令 'LABEL:掩藏秘密' (完整行: 'LABEL:掩藏秘密') [14:02]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:02]
【warn】[ScriptParser] 第 76 行: 未知或未实现的命令 'LABEL:姑娘' (完整行: 'LABEL:姑娘') [14:02]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:02]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:02]
【warn】[ScriptParser] 第 87 行: 未知或未实现的命令 'LABEL:花田扑蝶' (完整行: 'LABEL:花田扑蝶') [14:02]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【err】不支持这个按键，请检查空格或者其他符号 [14:02]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:02]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:02]
【warn】[ScriptParser] 第 97 行: 未知或未实现的命令 'LABEL:小妖' (完整行: 'LABEL:小妖') [14:02]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:02]
【warn】[ScriptParser] 第 103 行: 未知或未实现的命令 'LABEL:猫儿偷鸡' (完整行: 'LABEL:猫儿偷鸡') [14:02]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:02]
【warn】[DispatchCenter] VM1模板文本为：lingxi [14:02]
【warn】[ScriptParser] 第 113 行: 未知或未实现的命令 'LABEL:小鸡哒哒' (完整行: 'LABEL:小鸡哒哒') [14:03]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:03]
【warn】[ScriptParser] 第 119 行: 未知或未实现的命令 'LABEL:灵兽出战' (完整行: 'LABEL:灵兽出战') [14:03]
【warn】[DispatchCenter] VM1模板文本为：chuzhan [14:03]
【warn】[DispatchCenter] VM1模板文本为：xiuxi [14:03]
【warn】[ScriptParser] 第 133 行: 未知或未实现的命令 'LABEL:意外失踪' (完整行: 'LABEL:意外失踪') [14:03]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:03]
【warn】[ScriptParser] 第 143 行: 未知或未实现的命令 'LABEL:狐妖阿纤' (完整行: 'LABEL:狐妖阿纤') [14:03]
【warn】[ScriptParser] 第 149 行: 未知或未实现的命令 'LABEL:小蜘蛛娘' (完整行: 'LABEL:小蜘蛛娘') [14:03]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:03]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:03]
【warn】[ScriptParser] 第 158 行: 未知或未实现的命令 'LABEL:祸之试' (完整行: 'LABEL:祸之试') [14:03]
【warn】[ScriptParser] 第 163 行: 未知或未实现的命令 'LABEL:蛛丝' (完整行: 'LABEL:蛛丝') [14:03]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:03]
【warn】[ScriptParser] 第 173 行: 未知或未实现的命令 'LABEL:丝洞探秘' (完整行: 'LABEL:丝洞探秘') [14:03]
【info】[DispatchCenter] VM1: 等待画面静止任务已添加到队列 [14:03]
【info】[DispatchCenter] 暂停虚拟机: VM1 [14:03]
【info】虚拟机 [VM1] 已暂停，当前无待执行任务 [14:03]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:03]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:03]
【warn】[VMVision] VM2 收到新帧: 1920x1080, 通道: 3 [14:03]
【debug】[DispatchCenter] VM2: 主循环运行中，总切片数: 2 [14:03]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:03]
【debug】[DispatchCenter] VM1: 主循环运行中，总切片数: 2 [14:03]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:03]
【warn】[VMVision] VM2 收到新帧: 1920x1080, 通道: 3 [14:03]
【warn】[VMVision] VM1 收到新帧: 2560x1440, 通道: 3 [14:03]
