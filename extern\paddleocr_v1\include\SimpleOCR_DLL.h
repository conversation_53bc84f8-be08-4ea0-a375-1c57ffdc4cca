#pragma once

#ifdef _WIN32
#  ifdef PADDLEOCR_DLL_EXPORTS
#    define PADDLEOCR_API __declspec(dllexport)
#  else
#    define PADDLEOCR_API __declspec(dllimport)
#  endif
#else
#  define PADDLEOCR_API
#endif

#ifdef __cplusplus
extern "C" {
#endif

// 文本检测结果结构体
typedef struct {
    char* text;           // 识别的文本
    int* box;             // 边界框坐标 [x1, y1, x2, y2, x3, y3, x4, y4]
    float confidence;     // 置信度
    int center_x;         // 中心点X坐标
    int center_y;         // 中心点Y坐标
} OCRResult;

// 创建OCR引擎
// @param det_model_dir 检测模型目录
// @param rec_model_dir 识别模型目录
// @param dict_path 字典文件路径
// @param use_gpu 是否使用GPU
// @param gpu_id GPU ID
// @return OCR引擎句柄，失败返回NULL
PADDLEOCR_API void* OCR_Create(
    const char* det_model_dir,
    const char* rec_model_dir,
    const char* dict_path,
    int use_gpu,
    int gpu_id
);

// 释放OCR引擎
// @param handle OCR引擎句柄
PADDLEOCR_API void OCR_Release(void* handle);

// 识别图像中的所有文本
// @param handle OCR引擎句柄
// @param image_data 图像数据（BGR格式）
// @param width 图像宽度
// @param height 图像高度
// @param channels 图像通道数（必须为3，BGR格式）
// @param results 结果数组，由调用者释放
// @param count 结果数量
// @return 成功返回1，失败返回0
PADDLEOCR_API int OCR_Recognize(
    void* handle,
    const unsigned char* image_data,
    int width,
    int height,
    int channels,
    OCRResult** results,
    int* count
);

// 查找图像中的特定文本
// @param handle OCR引擎句柄
// @param image_data 图像数据（BGR格式）
// @param width 图像宽度
// @param height 图像高度
// @param channels 图像通道数（必须为3，BGR格式）
// @param text 要查找的文本
// @param center_x 返回中心点X坐标
// @param center_y 返回中心点Y坐标
// @param confidence 返回置信度
// @return 成功返回1，失败返回0
PADDLEOCR_API int OCR_FindText(
    void* handle,
    const unsigned char* image_data,
    int width,
    int height,
    int channels,
    const char* text,
    int* center_x,
    int* center_y,
    float* confidence
);

// 释放OCR结果
// @param results 结果数组
// @param count 结果数量
PADDLEOCR_API void OCR_FreeResults(OCRResult* results, int count);

#ifdef __cplusplus
}
#endif
