#pragma once

#include <vector>
#include <queue>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <future>
#include <functional>
#include <stdexcept>
#include <atomic>
#include <string>
#include <type_traits>
// #include <GUI/consolelog/consolelog.h> // 移除，避免静态初始化顺序问题

/**
 * 线程池类 - 简化版本，兼容C++17
 * 支持提交任务并获取任务执行结果
 */
class ThreadPool {
public:
    /**
     * 构造函数
     */
    explicit ThreadPool(size_t numThreads = 0, 
                       const std::string& name = "ThreadPool");
    
    /**
     * 析构函数
     */
    ~ThreadPool();
    
    // 禁用拷贝和移动
    ThreadPool(const ThreadPool&) = delete;
    ThreadPool& operator=(const ThreadPool&) = delete;
    ThreadPool(ThreadPool&&) = delete;
    ThreadPool& operator=(ThreadPool&&) = delete;
    
    // ==================== 任务提交 ====================
    
    /**
     * 提交任务
     */
    template<typename F, typename... Args>
    auto enqueue(F&& f, Args&&... args) 
        -> std::future<typename std::result_of<F(Args...)>::type>;
    
    // ==================== 控制方法 ====================
    
    /**
     * 停止线程池
     */
    void stop(bool graceful = true, std::chrono::milliseconds timeout = std::chrono::seconds(5));
    
    /**
     * 等待所有任务完成
     */
    void waitForCompletion(std::chrono::milliseconds timeout = std::chrono::milliseconds::max());
    
    // ==================== 状态查询 ====================
    
    /**
     * 获取队列中的任务数
     */
    size_t getQueuedTaskCount() const;
    
    /**
     * 是否正在运行
     */
    bool isRunning() const;
    
    /**
     * 获取线程池名称
     */
    const std::string& getName() const;
    
    /**
     * 获取线程数量
     */
    size_t size() const;

private:
    // ==================== 内部实现 ====================
    
    // 线程管理
    std::vector<std::thread> workers_;
    std::string name_;
    
    // 任务队列
    std::queue<std::function<void()>> tasks_;
    
    // 同步原语
    mutable std::mutex queueMutex_;
    std::condition_variable condition_;
    
    // 状态管理
    std::atomic<bool> running_{true};
    std::atomic<bool> stopping_{false};
    
    // 内部方法
    void workerLoop();
};

// ==================== 模板实现 ====================

template<typename F, typename... Args>
auto ThreadPool::enqueue(F&& f, Args&&... args) 
    -> std::future<typename std::result_of<F(Args...)>::type> {
    
    using return_type = typename std::result_of<F(Args...)>::type;
    
    auto task = std::make_shared<std::packaged_task<return_type()>>(
        std::bind(std::forward<F>(f), std::forward<Args>(args)...)
    );
    
    std::future<return_type> result = task->get_future();
    
    {
        std::unique_lock<std::mutex> lock(queueMutex_);
        
        if (stopping_) {
            // 静默处理线程池正在停止的情况，返回无效的future
            std::promise<return_type> promise;
            auto future = promise.get_future();
            return future;
        }
        
        tasks_.emplace([task]() { (*task)(); });
    }
    
    condition_.notify_one();
    return result;
} 