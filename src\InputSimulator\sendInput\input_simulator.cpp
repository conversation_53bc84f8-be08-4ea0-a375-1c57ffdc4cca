#include "input_simulator.h"
#include <cmath>
#include <random>
#include <chrono>
#include <thread>
#include <vector>
#include <algorithm>

InputSimulator::InputSimulator() : min_delay(10), max_delay(100),
delay_distribution(min_delay, max_delay),
error_probability(0.01)
{
    // 初始化随机数生成器
    std::random_device rd;
    rng = std::mt19937(rd());
}

InputSimulator::~InputSimulator() {}

void InputSimulator::moveMouse(int x, int y, bool human_like) {
    if (human_like) {
        POINT current_pos;
        GetCursorPos(&current_pos);

        // 生成人类化路径
        std::vector<POINT> path = generateHumanLikePath(current_pos.x, current_pos.y, x, y);

        // 沿路径移动鼠标
        for (const auto& point : path) {
            SetCursorPos(point.x, point.y);
            humanDelay(MouseMovement); // 指定操作类型
        }
    }
    else {
        // 直接移动到目标位置
        SetCursorPos(x, y);
    }
}

void InputSimulator::moveMouseRelative(int dx, int dy, bool human_like) {
    POINT current_pos;
    GetCursorPos(&current_pos);
    moveMouse(current_pos.x + dx, current_pos.y + dy, human_like);
}

void InputSimulator::mouseLeftClick(bool human_like) {
    INPUT inputs[2] = {};

    // 鼠标按下
    inputs[0].type = INPUT_MOUSE;
    inputs[0].mi.dwFlags = MOUSEEVENTF_LEFTDOWN;

    // 鼠标释放
    inputs[1].type = INPUT_MOUSE;
    inputs[1].mi.dwFlags = MOUSEEVENTF_LEFTUP;

    // 发送输入
    SendInput(2, inputs, sizeof(INPUT) * 2); // 发送两个输入事件

    if (human_like) {
        humanDelay(MouseClick); // 指定操作类型
    }
}

void InputSimulator::mouseRightClick(bool human_like) {
    INPUT inputs[2] = {};

    // 鼠标按下
    inputs[0].type = INPUT_MOUSE;
    inputs[0].mi.dwFlags = MOUSEEVENTF_RIGHTDOWN;

    // 鼠标释放
    inputs[1].type = INPUT_MOUSE;
    inputs[1].mi.dwFlags = MOUSEEVENTF_RIGHTUP;

    // 发送输入
    SendInput(2, inputs, sizeof(INPUT) * 2); // 发送两个输入事件

    if (human_like) {
        humanDelay(MouseClick); // 指定操作类型
    }
}

void InputSimulator::mouseDoubleClick(bool human_like) {
    mouseLeftClick(false);

    if (human_like) {
        // 双击间隔
        std::uniform_int_distribution<int> dist(80, 180); // 稍微调整双击间隔范围
        std::this_thread::sleep_for(std::chrono::milliseconds(dist(rng)));
    }

    mouseLeftClick(false);
}

void InputSimulator::mouseDrag(int start_x, int start_y, int end_x, int end_y, bool human_like) {
    // 移动到起始位置
    moveMouse(start_x, start_y, human_like);

    // 按下鼠标左键
    INPUT input_down = {};
    input_down.type = INPUT_MOUSE;
    input_down.mi.dwFlags = MOUSEEVENTF_LEFTDOWN;
    SendInput(1, &input_down, sizeof(INPUT));

    if (human_like) {
        humanDelay(MouseDrag); // 指定操作类型
    }

    // 移动到结束位置
    moveMouse(end_x, end_y, human_like);

    // 释放鼠标左键
    INPUT input_up = {};
    input_up.type = INPUT_MOUSE;
    input_up.mi.dwFlags = MOUSEEVENTF_LEFTUP;
    SendInput(1, &input_up, sizeof(INPUT));
}

void InputSimulator::mouseScroll(int amount) {
    INPUT input = {};
    input.type = INPUT_MOUSE;
    input.mi.dwFlags = MOUSEEVENTF_WHEEL;
    input.mi.mouseData = amount;
    SendInput(1, &input, sizeof(INPUT));
    humanDelay(MouseScroll); // 为滚轮操作添加延迟
}

void InputSimulator::keyPress(WORD key_code) {
    keyDown(key_code);
    humanDelay(KeyPress); // 指定操作类型
    keyUp(key_code);
}

void InputSimulator::keyDown(WORD key_code) {
    INPUT input = {};
    input.type = INPUT_KEYBOARD;
    input.ki.wVk = key_code;
    input.ki.dwFlags = 0; // Key down event
    SendInput(1, &input, sizeof(INPUT));
}

void InputSimulator::keyUp(WORD key_code) {
    INPUT input = {};
    input.type = INPUT_KEYBOARD;
    input.ki.wVk = key_code;
    input.ki.dwFlags = KEYEVENTF_KEYUP; // Key up event
    SendInput(1, &input, sizeof(INPUT));
}

void InputSimulator::typeText(const std::string& text, bool human_like) {
    std::vector<INPUT> inputs;
    std::uniform_real_distribution<> error_dist(0.0, 1.0);
    std::uniform_int_distribution<> error_char_dist(33, 126); // 可打印字符范围
    std::uniform_int_distribution<> key_press_delay_dist(20, 80); // 按键按下和释放之间的延迟

    for (const char c : text) {
        if (human_like && error_dist(rng) < error_probability) {
            // 模拟按错一个字符
            SHORT wrong_vk = VkKeyScanA(static_cast<char>(error_char_dist(rng)));
            WORD wrong_key_code = LOBYTE(wrong_vk);
            bool wrong_shift = HIBYTE(wrong_vk) & 1;

            std::vector<INPUT> error_inputs;
            if (wrong_shift) {
                INPUT shift_down = {};
                shift_down.type = INPUT_KEYBOARD;
                shift_down.ki.wVk = VK_SHIFT;
                error_inputs.push_back(shift_down);
            }
            INPUT wrong_key_down = {};
            wrong_key_down.type = INPUT_KEYBOARD;
            wrong_key_down.ki.wVk = wrong_key_code;
            error_inputs.push_back(wrong_key_down);

            std::this_thread::sleep_for(std::chrono::milliseconds(key_press_delay_dist(rng)));

            INPUT wrong_key_up = {};
            wrong_key_up.type = INPUT_KEYBOARD;
            wrong_key_up.ki.wVk = wrong_key_code;
            wrong_key_up.ki.dwFlags = KEYEVENTF_KEYUP;
            error_inputs.push_back(wrong_key_up);
            if (wrong_shift) {
                INPUT shift_up = {};
                shift_up.type = INPUT_KEYBOARD;
                shift_up.ki.wVk = VK_SHIFT;
                shift_up.ki.dwFlags = KEYEVENTF_KEYUP;
                error_inputs.push_back(shift_up);
            }
            SendInput(error_inputs.size(), error_inputs.data(), sizeof(INPUT));
            humanDelay(Typing);

            // 模拟按下退格键修正错误
            keyDown(VK_BACK);
            humanDelay(Typing);
            keyUp(VK_BACK);
            humanDelay(Typing);
        }

        SHORT vk = VkKeyScanA(c);
        WORD key_code = LOBYTE(vk);
        bool shift = HIBYTE(vk) & 1;

        std::vector<INPUT> char_inputs;
        if (shift) {
            INPUT shift_down = {};
            shift_down.type = INPUT_KEYBOARD;
            shift_down.ki.wVk = VK_SHIFT;
            char_inputs.push_back(shift_down);
        }

        INPUT key_down = {};
        key_down.type = INPUT_KEYBOARD;
        key_down.ki.wVk = key_code;
        char_inputs.push_back(key_down);

        std::this_thread::sleep_for(std::chrono::milliseconds(key_press_delay_dist(rng)));

        INPUT key_up = {};
        key_up.type = INPUT_KEYBOARD;
        key_up.ki.wVk = key_code;
        key_up.ki.dwFlags = KEYEVENTF_KEYUP;
        char_inputs.push_back(key_up);

        if (shift) {
            INPUT shift_up = {};
            shift_up.type = INPUT_KEYBOARD;
            shift_up.ki.wVk = VK_SHIFT;
            shift_up.ki.dwFlags = KEYEVENTF_KEYUP;
            char_inputs.push_back(shift_up);
        }

        if (human_like) {
            // 发送当前字符的输入并添加延迟
            SendInput(char_inputs.size(), char_inputs.data(), sizeof(INPUT));
            char_inputs.clear();

            // 不同字符有不同的打字速度
            std::uniform_int_distribution<int> dist(60, 180); // 调整打字速度范围
            std::this_thread::sleep_for(std::chrono::milliseconds(dist(rng)));
        }
        else {
            // 存储输入事件以便稍后一次性发送
            inputs.insert(inputs.end(), char_inputs.begin(), char_inputs.end());
        }
    }

    // 如果不是人类化模式，一次性发送所有输入
    if (!human_like && !inputs.empty()) {
        SendInput(inputs.size(), inputs.data(), sizeof(INPUT));
    }
}

void InputSimulator::hotkey(const std::vector<WORD>& keys) {
    // 按下所有键
    std::vector<INPUT> key_down_inputs(keys.size());
    for (size_t i = 0; i < keys.size(); ++i) {
        key_down_inputs[i].type = INPUT_KEYBOARD;
        key_down_inputs[i].ki.wVk = keys[i];
        key_down_inputs[i].ki.dwFlags = 0; // Key down
    }

    // 释放所有键（反向顺序）
    std::vector<INPUT> key_up_inputs(keys.size());
    for (size_t i = 0; i < keys.size(); ++i) {
        key_up_inputs[i].type = INPUT_KEYBOARD;
        key_up_inputs[i].ki.wVk = keys[keys.size() - 1 - i];
        key_up_inputs[i].ki.dwFlags = KEYEVENTF_KEYUP; // Key up
    }

    // 发送按下操作
    SendInput(key_down_inputs.size(), key_down_inputs.data(), sizeof(INPUT));

    // 短暂延迟
    humanDelay(Hotkey); // 为热键操作添加延迟

    // 发送释放操作
    SendInput(key_up_inputs.size(), key_up_inputs.data(), sizeof(INPUT));
}

POINT InputSimulator::getCurrentMousePos() const {
    POINT pos;
    GetCursorPos(&pos);
    return pos;
}

void InputSimulator::setDelaySettings(int min_delay, int max_delay) {
    this->min_delay = min_delay;
    this->max_delay = max_delay;
    delay_distribution = std::uniform_int_distribution<>(min_delay, max_delay);
}

// 使用高斯分布生成延迟
int InputSimulator::generateGaussianDelay(int mean, int stddev) {
    std::normal_distribution<> distribution(mean, stddev);
    int delay = static_cast<int>(distribution(rng));
    return std::max(min_delay, std::min(max_delay, delay)); // 确保延迟在合理范围内
}

void InputSimulator::humanDelay(OperationType type) {
    int delay_ms;
    switch (type) {
    case MouseMovement:
        delay_ms = generateGaussianDelay(5, 3); // 更短的移动延迟，但有波动
        break;
    case MouseClick:
        delay_ms = generateGaussianDelay(80, 20); // 点击延迟
        break;
    case MouseDrag:
        delay_ms = generateGaussianDelay(15, 5); // 拖拽时的微小延迟
        break;
    case MouseScroll:
        delay_ms = generateGaussianDelay(30, 10); // 滚轮延迟
        break;
    case KeyPress:
        delay_ms = generateGaussianDelay(70, 15); // 单个按键按下释放的整体延迟
        break;
    case Typing:
        delay_ms = generateGaussianDelay(60, 20); // 打字时的字符间隔
        break;
    case Hotkey:
        delay_ms = generateGaussianDelay(40, 10); // 热键操作的延迟
        break;
    default:
        delay_ms = delay_distribution(rng); // 默认延迟
        break;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms));
}

std::vector<POINT> InputSimulator::generateHumanLikePath(int start_x, int start_y, int end_x, int end_y) {
    std::vector<POINT> path;

    // 计算两点之间的直线距离
    double distance = std::sqrt(std::pow(end_x - start_x, 2) + std::pow(end_y - start_y, 2));

    // 如果距离很短，就直接返回终点
    if (distance < 5) {
        POINT end_point = { end_x, end_y };
        path.push_back(end_point);
        return path;
    }

    // 确定路径点的数量（根据距离动态调整）
    int num_points = static_cast<int>(distance / 8.0) + 5; // 增加路径点数量

    std::uniform_real_distribution<> t_dist(0.0, 1.0);
    std::uniform_real_distribution<> offset_dist(-5.0, 5.0); // 随机偏移范围

    // 生成控制点（数量也根据距离动态调整）
    int num_control_points = std::max(1, static_cast<int>(distance / 30.0));
    std::vector<POINT> control_points(num_control_points);
    for (int i = 0; i < num_control_points; ++i) {
        control_points[i].x = static_cast<LONG>((start_x + end_x) / 2.0 + offset_dist(rng) * distance / 100.0);
        control_points[i].y = static_cast<LONG>((start_y + end_y) / 2.0 + offset_dist(rng) * distance / 100.0);
    }

    // 生成路径点
    for (int i = 0; i <= num_points; ++i) {
        double t = static_cast<double>(i) / num_points;
        double x = 0.0;
        double y = 0.0;

        // 使用多项式（例如三次贝塞尔曲线的简化形式）模拟路径
        double omt = 1.0 - t;
        double omt2 = omt * omt;
        double t2 = t * t;

        x = omt2 * start_x + t2 * end_x;
        y = omt2 * start_y + t2 * end_y;

        // 加入控制点的影响
        for (const auto& cp : control_points) {
            double weight = 2.0 * omt * t; // 简化的权重
            x += weight * cp.x;
            y += weight * cp.y;
        }

        // 添加微小的随机偏移
        x += offset_dist(rng);
        y += offset_dist(rng);

        POINT point = { static_cast<LONG>(x), static_cast<LONG>(y) };
        path.push_back(point);
    }

    // 确保最后一个点是精确的目标位置
    POINT end_point = { end_x, end_y };
    path.push_back(end_point);

    return path;
}