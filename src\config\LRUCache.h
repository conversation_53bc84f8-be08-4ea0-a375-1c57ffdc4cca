#pragma once
#include <list>
#include <unordered_map>
#include <string>
#include <opencv2/opencv.hpp>
#include <mutex>

//该缓存主要用于存储模板图像和OCR预处理后的图像，以便快速访问和减少重复计算
class LRUCache {
public:
    LRUCache(size_t capacity) : capacity_(capacity) {}

    // 获取缓存，如果不存在返回空Mat
    cv::Mat get(const std::string& key) {
        std::lock_guard<std::mutex> lock(mutex_);
        auto it = cacheMap_.find(key);
        if (it == cacheMap_.end()) return cv::Mat();
        // 移动到最前面
        cacheList_.splice(cacheList_.begin(), cacheList_, it->second);
        return it->second->second;
    }

    // 插入缓存
    void put(const std::string& key, const cv::Mat& value) {
        std::lock_guard<std::mutex> lock(mutex_);
        auto it = cacheMap_.find(key);
        if (it != cacheMap_.end()) {
            // 更新并移到最前
            it->second->second = value;
            cacheList_.splice(cacheList_.begin(), cacheList_, it->second);
        }
        else {
            // 插入新元素
            cacheList_.emplace_front(key, value);
            cacheMap_[key] = cacheList_.begin();
            if (cacheMap_.size() > capacity_) {
                // 超出容量，移除最后
                auto last = cacheList_.end();
                --last;
                cacheMap_.erase(last->first);
                cacheList_.pop_back();
            }
        }
    }

    // 清空缓存
    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        cacheList_.clear();
        cacheMap_.clear();
    }

private:
    size_t capacity_;
    std::list<std::pair<std::string, cv::Mat>> cacheList_;
    std::unordered_map<std::string, decltype(cacheList_.begin())> cacheMap_;
    std::mutex mutex_;
};
