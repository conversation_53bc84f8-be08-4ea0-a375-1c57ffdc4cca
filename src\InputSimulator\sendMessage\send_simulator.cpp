
#include <random>      // C++11 random numbers
#include <chrono>      // Timing
#include <thread>      // std::this_thread::sleep_for
#include <vector>      // Potentially for advanced pathing
#include <cmath>       // For std::max if needed, or use <algorithm>
#include <algorithm>   // For std::max
// #include <iostream>    // For warnings (cerr) - 已移除
#include "send_simulator.h"

// --- Implementation Details (Hidden from Header) ---

namespace { // Use an anonymous namespace for internal linkage

    // Random number generator setup
    std::random_device rd;
    std::mt19937 gen(rd());

    // Helper to generate random integer in [min, max]
    int GetRandomInt(int min, int max) {
        if (min > max) std::swap(min, max); // Ensure min <= max
        std::uniform_int_distribution<> distrib(min, max);
        return distrib(gen);
    }

    // Helper to build common lParam for WM_KEYDOWN
    LPARAM BuildKeyDownLParam(UINT vkCode) {
        UINT scanCode = MapVirtualKey(vkCode, MAPVK_VK_TO_VSC);
        // bits 0-15: Repeat count (usually 1 for non-autorepeat)
        // bits 16-23: Scan code
        // bit 24: Extended key flag
        // bits 25-28: Reserved
        // bit 29: Context code (usually 0)
        // bit 30: Previous key state (0 if up before, 1 if down)
        // bit 31: Transition state (0 for press)
        return (1 | (scanCode << 16));
    }

    // Helper to build common lParam for WM_KEYUP
    LPARAM BuildKeyUpLParam(UINT vkCode) {
        UINT scanCode = MapVirtualKey(vkCode, MAPVK_VK_TO_VSC);
        // bits 0-15: Repeat count (usually 1)
        // bits 16-23: Scan code
        // bit 24: Extended key flag
        // bits 25-28: Reserved
        // bit 29: Context code
        // bit 30: Previous key state (must be 1 for WM_KEYUP)
        // bit 31: Transition state (must be 1 for WM_KEYUP)
        return (1 | (scanCode << 16) | (1 << 30) | (1 << 31));
    }

    // Helper to get current modifier key/mouse button state for WM_MOUSEMOVE wParam
    WPARAM GetCurrentMouseWParam() {
        WPARAM wParam = 0;
        // Check standard mouse buttons using GetKeyState (reflects message queue state)
        if (GetKeyState(VK_LBUTTON) < 0) wParam |= MK_LBUTTON;
        if (GetKeyState(VK_RBUTTON) < 0) wParam |= MK_RBUTTON;
        if (GetKeyState(VK_MBUTTON) < 0) wParam |= MK_MBUTTON;
        // Check common modifier keys (GetAsyncKeyState checks immediate physical state)
        if (GetAsyncKeyState(VK_SHIFT) & 0x8000) wParam |= MK_SHIFT;
        if (GetAsyncKeyState(VK_CONTROL) & 0x8000) wParam |= MK_CONTROL;
        // Add other keys if needed (VK_MENU for Alt, VK_XBUTTON1, VK_XBUTTON2)
        if (GetKeyState(VK_XBUTTON1) < 0) wParam |= MK_XBUTTON1;
        if (GetKeyState(VK_XBUTTON2) < 0) wParam |= MK_XBUTTON2;
        return wParam;
    }

    // Helper function for smooth movement logic (used by both Post and Send versions)
    template<typename Func> // Func can be PostMessageW or SendMessageW
    void PerformSmoothMove(Func messageFunc, HWND hwnd, int endX, int endY, int steps, int totalDurationMs)
    {
        if (!IsWindow(hwnd) || steps <= 0) return;

        POINT startPoint;
        if (!GetCursorPos(&startPoint)) {
            // Fallback: If we can't get cursor pos, maybe start from center or corner?
            // Or just warn and potentially do a direct move.
            // For now, let's try ScreenToClient even if GetCursorPos fails (might get 0,0)
            startPoint = { 0, 0 }; // Default fallback
            // A better fallback might get client rect middle point
        }

        // Convert screen coordinates to client coordinates for the start point
        if (!ScreenToClient(hwnd, &startPoint)) {
            // If ScreenToClient fails, coordinates might be unreliable.
            // Maybe default startPoint to {0,0} within client?
            startPoint = { 0, 0 }; // Reset if conversion failed
            // std::cerr << "Warning: ScreenToClient failed in PerformSmoothMove." << std::endl;
        }

        int startX = startPoint.x;
        int startY = startPoint.y;

        int deltaX = endX - startX;
        int deltaY = endY - startY;

        // Ensure minimum duration per step to avoid hyper-fast sleeps
        int baseStepDuration = (totalDurationMs > 0) ? (totalDurationMs / steps) : 10;
        if (baseStepDuration <= 0) baseStepDuration = 5; // Minimum average step duration

        for (int i = 1; i <= steps; ++i) {
            // Linear interpolation for intermediate point
            int interX = startX + static_cast<int>(static_cast<double>(deltaX) * i / steps);
            int interY = startY + static_cast<int>(static_cast<double>(deltaY) * i / steps);

            // Optional: Add slight random jitter to intermediate points
            // interX += GetRandomInt(-1, 1);
            // interY += GetRandomInt(-1, 1);

            LPARAM interLParam = MAKELPARAM(interX, interY);
            WPARAM currentWParam = GetCurrentMouseWParam();

            // Use the provided function (PostMessage or SendMessage)
            messageFunc(hwnd, WM_MOUSEMOVE, currentWParam, interLParam);

            // If it's the last step, break loop early, final exact move is outside loop
            if (i == steps) break;

            // Calculate randomized sleep duration for this step
            int sleepDuration = GetRandomInt(
                std::max(1, baseStepDuration - 5), // Ensure at least 1ms sleep
                baseStepDuration + 5
            );
            std::this_thread::sleep_for(std::chrono::milliseconds(sleepDuration));
        }

        // Ensure the mouse ends up at the precise final location
        LPARAM finalLParam = MAKELPARAM(endX, endY);
        WPARAM finalWParam = GetCurrentMouseWParam(); // Get state again for final move
        messageFunc(hwnd, WM_MOUSEMOVE, finalWParam, finalLParam);
    }


} // end anonymous namespace

// --- Public Function Implementations ---

namespace InputSim {

    // --- PostMessage Implementations ---

    void PostKey(HWND hwnd, UINT vkCode, int minDelayMs, int maxDelayMs) {
        if (!IsWindow(hwnd)) return;
        LPARAM lParamDown = BuildKeyDownLParam(vkCode);
        LPARAM lParamUp = BuildKeyUpLParam(vkCode);

        PostMessageW(hwnd, WM_KEYDOWN, vkCode, lParamDown);
        int delay = GetRandomInt(minDelayMs, maxDelayMs);
        std::this_thread::sleep_for(std::chrono::milliseconds(delay));
        PostMessageW(hwnd, WM_KEYUP, vkCode, lParamUp);
    }

    void PostLeftClick(HWND hwnd, int x, int y, int minDelayMs, int maxDelayMs, int coordRandomness) {
        if (!IsWindow(hwnd)) return;
        int offsetX = GetRandomInt(-coordRandomness, coordRandomness);
        int offsetY = GetRandomInt(-coordRandomness, coordRandomness);
        int finalX = x + offsetX;
        int finalY = y + offsetY;
        LPARAM lParam = MAKELPARAM(finalX, finalY);

        PostMessageW(hwnd, WM_LBUTTONDOWN, MK_LBUTTON, lParam);
        int delay = GetRandomInt(minDelayMs, maxDelayMs);
        std::this_thread::sleep_for(std::chrono::milliseconds(delay));
        PostMessageW(hwnd, WM_LBUTTONUP, 0, lParam);
    }

    void PostRightClick(HWND hwnd, int x, int y, int minDelayMs, int maxDelayMs, int coordRandomness) {
        if (!IsWindow(hwnd)) return;
        int offsetX = GetRandomInt(-coordRandomness, coordRandomness);
        int offsetY = GetRandomInt(-coordRandomness, coordRandomness);
        int finalX = x + offsetX;
        int finalY = y + offsetY;
        LPARAM lParam = MAKELPARAM(finalX, finalY);

        PostMessageW(hwnd, WM_RBUTTONDOWN, MK_RBUTTON, lParam);
        int delay = GetRandomInt(minDelayMs, maxDelayMs);
        std::this_thread::sleep_for(std::chrono::milliseconds(delay));
        PostMessageW(hwnd, WM_RBUTTONUP, 0, lParam);
    }

    void PostMouseMoveSmooth(HWND hwnd, int endX, int endY, int steps, int totalDurationMs) {
        PerformSmoothMove(PostMessageW, hwnd, endX, endY, steps, totalDurationMs);
    }


    // --- SendMessage Implementations ---

    void SendKey(HWND hwnd, UINT vkCode, int minDelayMs, int maxDelayMs) {
        if (!IsWindow(hwnd)) return;
        LPARAM lParamDown = BuildKeyDownLParam(vkCode);
        LPARAM lParamUp = BuildKeyUpLParam(vkCode);

        // WARNING: Blocking calls
        SendMessageW(hwnd, WM_KEYDOWN, vkCode, lParamDown);
        int delay = GetRandomInt(minDelayMs, maxDelayMs);
        std::this_thread::sleep_for(std::chrono::milliseconds(delay));
        SendMessageW(hwnd, WM_KEYUP, vkCode, lParamUp);
    }

    void SendLeftClick(HWND hwnd, int x, int y, int minDelayMs, int maxDelayMs, int coordRandomness) {
        if (!IsWindow(hwnd)) return;
        int offsetX = GetRandomInt(-coordRandomness, coordRandomness);
        int offsetY = GetRandomInt(-coordRandomness, coordRandomness);
        int finalX = x + offsetX;
        int finalY = y + offsetY;
        LPARAM lParam = MAKELPARAM(finalX, finalY);

        // WARNING: Blocking calls
        SendMessageW(hwnd, WM_LBUTTONDOWN, MK_LBUTTON, lParam);
        int delay = GetRandomInt(minDelayMs, maxDelayMs);
        std::this_thread::sleep_for(std::chrono::milliseconds(delay));
        SendMessageW(hwnd, WM_LBUTTONUP, 0, lParam);
    }

    void SendRightClick(HWND hwnd, int x, int y, int minDelayMs, int maxDelayMs, int coordRandomness) {
        if (!IsWindow(hwnd)) return;
        int offsetX = GetRandomInt(-coordRandomness, coordRandomness);
        int offsetY = GetRandomInt(-coordRandomness, coordRandomness);
        int finalX = x + offsetX;
        int finalY = y + offsetY;
        LPARAM lParam = MAKELPARAM(finalX, finalY);

        // WARNING: Blocking calls
        SendMessageW(hwnd, WM_RBUTTONDOWN, MK_RBUTTON, lParam);
        int delay = GetRandomInt(minDelayMs, maxDelayMs);
        std::this_thread::sleep_for(std::chrono::milliseconds(delay));
        SendMessageW(hwnd, WM_RBUTTONUP, 0, lParam);
    }

    void SendMouseMoveSmooth(HWND hwnd, int endX, int endY, int steps, int totalDurationMs) {
        // 警告：使用平滑鼠标移动，每步都会阻塞
        PerformSmoothMove(SendMessageW, hwnd, endX, endY, steps, totalDurationMs);
    }

} // namespace InputSim