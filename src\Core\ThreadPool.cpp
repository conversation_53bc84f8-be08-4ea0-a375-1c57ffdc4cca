#include "ThreadPool.h"
#include <GUI/consolelog/consolelog.h>

// ==================== 构造和析构 ====================

ThreadPool::ThreadPool(size_t numThreads, const std::string& name)
    : name_(name) {
    
    if (numThreads == 0) {
        numThreads = std::thread::hardware_concurrency();
        if (numThreads == 0) numThreads = 2; // 后备值
    }
    
    // 静默创建线程池
    
    // 创建工作线程
    workers_.reserve(numThreads);
    for (size_t i = 0; i < numThreads; ++i) {
        workers_.emplace_back([this]() {
            workerLoop();
        });
    }
    
    // 线程池启动完成
}

ThreadPool::~ThreadPool() {
    if (running_) {
        stop(true, std::chrono::seconds(5));
    }
}

// ==================== 控制方法 ====================

void ThreadPool::stop(bool graceful, std::chrono::milliseconds timeout) {
    if (stopping_ || !running_) {
        return;
    }
    
    // 停止线程池
    
    stopping_ = true;
    running_ = false;
    
    if (!graceful) {
        // 立即清空任务队列
        std::unique_lock<std::mutex> lock(queueMutex_);
        while (!tasks_.empty()) {
            tasks_.pop();
        }
    }
    
    // 唤醒所有等待的线程
    condition_.notify_all();
    
    // 等待所有工作线程结束
    auto startTime = std::chrono::steady_clock::now();
    for (auto& worker : workers_) {
        if (worker.joinable()) {
            auto elapsed = std::chrono::steady_clock::now() - startTime;
            auto remainingTime = timeout - std::chrono::duration_cast<std::chrono::milliseconds>(elapsed);
            
            if (remainingTime > std::chrono::milliseconds::zero()) {
                worker.join(); // C++17 使用join等待线程结束
            } else {
                // 超时等待工作线程
                worker.detach(); // 超时时detach线程
            }
        }
    }
    
    workers_.clear();
    // 线程池已停止
}

void ThreadPool::waitForCompletion(std::chrono::milliseconds timeout) {
    auto startTime = std::chrono::steady_clock::now();
    
    while (running_ && getQueuedTaskCount() > 0) {
        auto elapsed = std::chrono::steady_clock::now() - startTime;
        if (elapsed >= timeout) {
            // 等待完成超时
            break;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

// ==================== 状态查询 ====================

size_t ThreadPool::getQueuedTaskCount() const {
    std::unique_lock<std::mutex> lock(queueMutex_);
    return tasks_.size();
}

bool ThreadPool::isRunning() const {
    return running_.load();
}

const std::string& ThreadPool::getName() const {
    return name_;
}

size_t ThreadPool::size() const {
    return workers_.size();
}

// ==================== 内部实现 ====================

void ThreadPool::workerLoop() {
    // 避免在工作线程中使用日志系统，可能导致静态初始化问题
    // std::cerr << "[ThreadPool:" << name_ << "] Worker thread started" << std::endl;
    
    while (running_) {
        std::function<void()> task;
        
        {
            std::unique_lock<std::mutex> lock(queueMutex_);
            condition_.wait(lock, [this] { 
                return !running_ || !tasks_.empty(); 
            });
            
            if (!running_ && tasks_.empty()) {
                break;
            }
            
            if (!tasks_.empty()) {
                task = std::move(tasks_.front());
                tasks_.pop();
            }
        }
        
        if (task) {
            try {
                task();
            } catch (const std::exception& e) {
                // 任务执行失败，静默处理
            } catch (...) {
                // 任务执行失败，静默处理未知异常
            }
        }
    }
    
    // 避免在工作线程结束时使用日志系统
    // std::cerr << "[ThreadPool:" << name_ << "] Worker thread stopped" << std::endl;
} 