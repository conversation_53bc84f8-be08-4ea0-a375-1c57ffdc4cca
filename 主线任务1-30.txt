// ========== 虚拟机1：测试切片1 ==========
//地名与坐标1110,30,170,25
//任务详情栏35,270,365,470
//中间任务执行栏470,270,350,480
//任务栏1050,400,230,200
//选择区域720,750,280,140
slice(1) {

	#1级任务
	MOUSE_DOUBLE(700,400)

	LABEL:神秘书斋
	TEXT_MATCH(神秘书斋, left, 1050, 400, 230, 200,0.1)
	DELAY(1000)
	TEXT_MATCH(神秘书斋, left, 470,270,350,480,0.1)
	DELAY(1000)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f)
	DELAY(1000)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f)
	DELAY(1000)
	MOUSE_DOUBLE(640,400)
	DELAY(1000)
	VISUAL_MATCH(vclose, left, 0, 0, 1920, 1080, 0.3)
	DELAY(1000)
	TEXT_MATCH(不去看了,left, 720,750,280,140,0.1)

}

// ========== 虚拟机1：测试切片2 ==========  
slice(2) {

	#1级任务
	MOUSE_DOUBLE(700,400)

	LABEL:神秘书斋
	TEXT_MATCH(神秘书斋, left, 1050, 400, 230, 200,0.1)
	DELAY(1000)
	TEXT_MATCH(神秘书斋, left, 470,270,350,480,0.1)
	DELAY(1000)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f)
	DELAY(1000)
	TITLEBAR_KEYBOARD_INPUT(f,f,f,f,f,f,f,f)
	DELAY(1000)
	MOUSE_DOUBLE(640,400)
	DELAY(1000)
	VISUAL_MATCH(vclose, left, 0, 0, 1920, 1080, 0.3)
	DELAY(1000)
	TEXT_MATCH(不去看了,left, 720,750,280,140,0.1)
}