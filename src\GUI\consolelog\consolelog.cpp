#include <ctype.h>          // 字符处理函数，例如 toupper（将字符转换为大写）
#include <limits.h>         // 整数的最小值和最大值常量INT_MIN, INT_MAX
#include <math.h>           // 提供数学函数，如 sqrtf（单精度平方根）、powf（单精度求幂）、cosf、sinf、floorf（向下取整）、ceilf（向上取整）等sqrtf, powf, cosf, sinf, floorf, ceilf
#include <stdio.h>          // 提供输入输出函数，如 vsnprintf、sscanf、printf
#include <stdlib.h>         // 提供动态内存管理及其它辅助函数，如 malloc、free、atoi（字符串转整数），以及常量 NULL。
#include <stdint.h>         // 定义了固定宽度整数类型，如 intptr_t（整型指针大小的整数）
#include "imgui.h"
#include <ctime>
#include <cstdio>
#include <string>
#include "consolelog.h"
#include <Windows.h>        // Windows API，用于剪贴板操作

// ThreadSafeLogQueue 实现
void ThreadSafeLogQueue::push(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (!shutdown_.load()) {
        queue_.push(entry);
        condition_.notify_one();
    }
}

bool ThreadSafeLogQueue::pop(LogEntry& entry, std::chrono::milliseconds timeout) {
    std::unique_lock<std::mutex> lock(mutex_);
    if (condition_.wait_for(lock, timeout, [this] { return !queue_.empty() || shutdown_.load(); })) {
        if (!queue_.empty()) {
            entry = queue_.front();
            queue_.pop();
            return true;
        }
    }
    return false;
}

void ThreadSafeLogQueue::shutdown() {
    {
        std::lock_guard<std::mutex> lock(mutex_);
        shutdown_.store(true);
    }
    condition_.notify_all();
}

size_t ThreadSafeLogQueue::size() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return queue_.size();
}

//结构体 AppLog，用于存储日志信息和处理日志显示 - 现在线程安全
struct AppLog
{
    ImGuiTextBuffer     Buf;   // 类型为 ImGuiTextBuffer，用于存储日志文本
    ImGuiTextFilter     Filter;  // 日志文本的过滤处理
    ImVector<int>       LineOffsets; // 保存每一行文本在日志缓冲区中的起始索引
    bool                AutoScroll;
    FILE*               LogFile;       // 日志文件指针
    int                 MaxDisplayLines; // 控制台显示的最大日志行数
    mutable std::mutex  mutex_;        // 线程安全保护

    // 构造函数：创建 AppLog 实例时，初始化日志数据并打开日志文件
    AppLog()
    {
        AutoScroll = true;
        MaxDisplayLines = 100; // 限制显示最新的100条日志
        
        // 创建日志文件名，使用当前日期和时间
        char filename[64];
        time_t now = time(nullptr);
        struct tm timeinfo;
        localtime_s(&timeinfo, &now);
        strftime(filename, sizeof(filename), "log_%Y%m%d_%H%M%S.txt", &timeinfo);
        
        // 打开日志文件
        fopen_s(&LogFile, filename, "w");
        if (!LogFile) {
            fprintf(stderr, "无法创建日志文件: %s\n", filename);
        }
        
        Clear();
    }
    
    // 析构函数：在程序结束时关闭日志文件
    ~AppLog()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        if (LogFile) {
            fclose(LogFile);
            LogFile = nullptr;
        }
    }

    // 清空日志缓冲 (Buf) 以及行偏移数组 (LineOffsets)，并插入一个初始值 0，表示文本开始位置。
    void Clear()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        Buf.clear();
        LineOffsets.clear();
        LineOffsets.push_back(0);
    }

    // 线程安全的日志添加方法
    void AddLogSafe(LogLevel level, const std::string& message, const std::chrono::system_clock::time_point& timestamp)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        
        const char* levelPrefix = "";
        switch (level) {
            case LogLevel::Error:    levelPrefix = "【err】"; break;
            case LogLevel::Warning: levelPrefix = "【warn】"; break;
            case LogLevel::Info:    levelPrefix = "【info】"; break;
            case LogLevel::Debug:   levelPrefix = "【debug】"; break;
        }

        // 格式化时间戳
        auto time_t_val = std::chrono::system_clock::to_time_t(timestamp);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            timestamp.time_since_epoch()) % 1000;
        
        struct tm timeinfo;
        localtime_s(&timeinfo, &time_t_val);
        
        char timeBuffer[32];
        snprintf(timeBuffer, sizeof(timeBuffer), "%02d:%02d:%02d.%03d",
                timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec, (int)ms.count());

        // 格式化完整的日志消息
        char logBuffer[4096];
        snprintf(logBuffer, sizeof(logBuffer), "%s %s [%s]\n", 
                levelPrefix, message.c_str(), timeBuffer);
        
        // 将日志写入文件
        if (LogFile) {
            fprintf(LogFile, "%s", logBuffer);
            fflush(LogFile); // 立即刷新到文件
        }
        
        // 限制控制台显示的日志数量
        if (LineOffsets.Size > MaxDisplayLines) {
            // 如果行数超过限制，重新创建日志缓冲区
            int linesToKeep = MaxDisplayLines;
            int startLine = LineOffsets.Size - linesToKeep;
            
            // 创建一个新的缓冲区来存储最新的日志
            ImGuiTextBuffer newBuf;
            
            // 复制最新的linesToKeep行日志
            const char* startPtr = Buf.begin() + LineOffsets[startLine];
            newBuf.append(startPtr, Buf.end());
            
            // 替换旧的缓冲区
            Buf = newBuf;
            
            // 重新计算行偏移量
            LineOffsets.clear();
            LineOffsets.push_back(0);
            
            // 重新计算每行的起始位置
            for (int i = 0; i < Buf.size(); i++)
                if (Buf[i] == '\n')
                    LineOffsets.push_back(i + 1);
        }
        
        // 将新日志添加到缓冲区
        int old_size = Buf.size();
        Buf.append(logBuffer);
        
        // 更新行偏移量
        for (int new_size = Buf.size(); old_size < new_size; old_size++)
            if (Buf[old_size] == '\n')
                LineOffsets.push_back(old_size + 1);
    }

    //下面是 Draw 方法，负责渲染日志界面 - 现在线程安全
    void Draw(const char* title, bool* p_open = NULL)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        
        // ImGui::SetNextWindowSize(ImVec2(500, 400), ImGuiCond_FirstUseEver);
        ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 1.0f);           // 圆角半径
        ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 2.0f);         // 边框厚度
        ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(1.0f, 1.0f, 1.0f, 1.0f));             //边框颜色
        ImGui::BeginChild(title, ImVec2(0, 0), ImGuiChildFlags_Border);

        // Main window;
        // 设置清除按钮为红色背景
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.8f, 0.0f, 0.0f, 1.0f));        // 红色背景
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.9f, 0.0f, 0.0f, 1.0f)); // 悬停时更亮的红色
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.7f, 0.0f, 0.0f, 1.0f));  // 按下时暗一点的红色
        
        bool clear = ImGui::Button("清除");
        
        ImGui::PopStyleColor(3); // 恢复按钮颜色设置
        
        ImGui::SameLine();
        bool copy = ImGui::Button("复制");
        ImGui::SameLine();
        Filter.Draw("过滤", -100.0f);

        //•	绘制一条分隔线，分隔界面区域
        ImGui::Separator();
        //•	开启另一个子窗口，标记为"scrolling"，用于实现可滚动区域，尺寸自适应，
        // 并启用水平滚动条（通过 ImGuiWindowFlags_HorizontalScrollbar）。
        if (ImGui::BeginChild("scrolling", ImVec2(0, 0), ImGuiChildFlags_None, ImGuiWindowFlags_HorizontalScrollbar))
        {
            if (clear)
                Clear();
                if (copy)
                {
                    // 使用 Windows 剪贴板 API 替代 ImGui::LogToClipboard
                    try {
                        // 准备要复制到剪贴板的文本
                        std::string clipboardText;
                        
                        // 如果有过滤条件，只复制符合条件的行
                        if (Filter.IsActive())
                        {
                            const char* buf = Buf.begin();
                            for (int line_no = 0; line_no < LineOffsets.Size; line_no++)
                            {
                                const char* line_start = buf + LineOffsets[line_no];
                                const char* line_end = (line_no + 1 < LineOffsets.Size) ? (buf + LineOffsets[line_no + 1] - 1) : Buf.end();
                                if (Filter.PassFilter(line_start, line_end))
                                {
                                    clipboardText.append(line_start, line_end - line_start + 1);
                                }
                            }
                        }
                        else
                        {
                            // 没有过滤条件，复制全部内容
                            clipboardText.assign(Buf.begin(), Buf.size());
                        }
                        
                        // 确保剪贴板文本不为空
                        if (!clipboardText.empty())
                        {
                            // 打开剪贴板
                            if (OpenClipboard(nullptr))
                            {
                                // 清空当前剪贴板内容
                                EmptyClipboard();
                                
                                // 计算需要的Unicode字符数
                                int wideCharCount = MultiByteToWideChar(CP_UTF8, 0, clipboardText.c_str(), -1, nullptr, 0);
                                if (wideCharCount > 0)
                                {
                                    // 分配全局内存用于存储Unicode文本
                                    HGLOBAL hClipboardData = GlobalAlloc(GMEM_MOVEABLE, wideCharCount * sizeof(wchar_t));
                                    if (hClipboardData)
                                    {
                                        // 锁定内存并获取指针
                                        wchar_t* pwchData = (wchar_t*)GlobalLock(hClipboardData);
                                        if (pwchData)
                                        {
                                            // 转换为Unicode
                                            MultiByteToWideChar(CP_UTF8, 0, clipboardText.c_str(), -1, pwchData, wideCharCount);
                                            
                                            // 解锁内存
                                            GlobalUnlock(hClipboardData);
                                            
                                            // 设置剪贴板数据为Unicode文本
                                            if (!SetClipboardData(CF_UNICODETEXT, hClipboardData))
                                            {
                                                // 如果设置失败，释放内存
                                                GlobalFree(hClipboardData);
                                            }
                                        }
                                        else
                                        {
                                            // 锁定失败，释放内存
                                            GlobalFree(hClipboardData);
                                        }
                                    }
                                }
                                
                                // 关闭剪贴板
                                CloseClipboard();
                            }
                        }
                    }
                    catch (const std::exception& e) {
                        // 记录异常，但不中断程序
                        fprintf(stderr, "复制到剪贴板时出错: %s\n", e.what());
                    }
                }
            //•	修改当前样式变量，将控件间的间距设置为 0，以便连续显示文本
            ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(0, 0));
            //•	定义指针 buf 指向日志缓冲的起始位置，buf_end 指向结束位置（结尾的空字符）
            const char* buf = Buf.begin();
            const char* buf_end = Buf.end();
            //•	检查过滤器是否处于激活状态（即用户已输入过滤条件）
            if (Filter.IsActive())
            {
                //•	如果过滤器激活，则遍历每一行，计算该行的开始和结束位置
                //调用 Filter.PassFilter 判断该行是否符合过滤条件，符合则用 ImGui::TextUnformatted 渲染该行文本。
                for (int line_no = 0; line_no < LineOffsets.Size; line_no++)
                {
                    const char* line_start = buf + LineOffsets[line_no];
                    const char* line_end = (line_no + 1 < LineOffsets.Size) ? (buf + LineOffsets[line_no + 1] - 1) : buf_end;
                    if (Filter.PassFilter(line_start, line_end))
                    {
                        // 根据日志级别设置不同的颜色
                        ImVec4 textColor;
                        if (strncmp(line_start, "【err】", 6) == 0) {
                            textColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f); // 红色
                        } else if (strncmp(line_start, "【warn】", 7) == 0) {
                            textColor = ImVec4(1.0f, 1.0f, 0.0f, 1.0f); // 黄色
                        } else {
                            textColor = ImVec4(0.8f, 0.8f, 0.8f, 1.0f); // 灰色（原来的颜色）
                        }
                        
                        ImGui::PushStyleColor(ImGuiCol_Text, textColor);
                        ImGui::TextUnformatted(line_start, line_end);
                        ImGui::PopStyleColor();
                    }
                }
            }
            else
            {
                //•	如果过滤器未激活，则直接使用 ImGui::TextUnformatted 渲染整个日志缓冲的内容。
                // 为每行设置颜色
                for (int line_no = 0; line_no < LineOffsets.Size; line_no++)
                {
                    const char* line_start = buf + LineOffsets[line_no];
                    const char* line_end = (line_no + 1 < LineOffsets.Size) ? (buf + LineOffsets[line_no + 1] - 1) : buf_end;
                    
                    // 根据日志级别设置不同的颜色
                    ImVec4 textColor;
                    if (strncmp(line_start, "【err】", 6) == 0) {
                        textColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f); // 红色
                    } else if (strncmp(line_start, "【warn】", 7) == 0) {
                        textColor = ImVec4(1.0f, 1.0f, 0.0f, 1.0f); // 黄色
                    } else {
                        textColor = ImVec4(0.8f, 0.8f, 0.8f, 1.0f); // 灰色（原来的颜色）
                    }
                    
                    ImGui::PushStyleColor(ImGuiCol_Text, textColor);
                    ImGui::TextUnformatted(line_start, line_end);
                    ImGui::PopStyleColor();
                }
            }
            ImGui::PopStyleVar();

            //•	检查当前滚动位置，如果已经滚动到最底部，则调用 ImGui::SetScrollHereY 保持滚动条始终位于底部（自动跟随最新日志）。
            if (AutoScroll && ImGui::GetScrollY() >= ImGui::GetScrollMaxY())
                ImGui::SetScrollHereY(1.0f);
        }
        ImGui::EndChild();
        ImGui::EndChild();
        ImGui::PopStyleColor();
        ImGui::PopStyleVar(2);  // 恢复两个样式变量：ChildRounding 和 ChildBorderSize
    }
};

// LogManager 实现
std::unique_ptr<LogManager> LogManager::instance_;
std::mutex LogManager::instanceMutex_;

AppLog GlobalAppLog;  // 全局唯一的日志实例，现在线程安全

LogManager::LogManager() = default;

LogManager::~LogManager() {
    shutdown();
}

std::unique_ptr<LogManager> LogManager::createInstance() {
    return std::unique_ptr<LogManager>(new LogManager());
}

LogManager* LogManager::getInstance() {
    std::lock_guard<std::mutex> lock(instanceMutex_);
    if (!instance_) {
        instance_ = createInstance();
    }
    return instance_.get();
}

void LogManager::startup() {
    if (running_.load()) {
        return;
    }
    
    running_.store(true);
    logThread_ = std::thread(&LogManager::logThreadFunction, this);
}

void LogManager::shutdown() {
    if (!running_.load()) {
        return;
    }
    
    running_.store(false);
    logQueue_.shutdown();
    
    if (logThread_.joinable()) {
        logThread_.join();
    }
}

void LogManager::addLog(LogLevel level, const std::string& message) {
    if (running_.load()) {
        logQueue_.push(LogEntry(level, message));
    } else {
        // 如果日志系统还未启动或已关闭，直接写入（作为备用方案）
        GlobalAppLog.AddLogSafe(level, message, std::chrono::system_clock::now());
    }
}

void LogManager::logThreadFunction() {
    LogEntry entry(LogLevel::Info, "");
    
    while (running_.load()) {
        if (logQueue_.pop(entry, std::chrono::milliseconds(100))) {
            try {
                GlobalAppLog.AddLogSafe(entry.level, entry.message, entry.timestamp);
            } catch (const std::exception& e) {
                // 日志处理出错，写入标准错误流作为备用
                fprintf(stderr, "日志处理异常: %s\n", e.what());
            } catch (...) {
                fprintf(stderr, "日志处理发生未知异常\n");
            }
        }
    }
    
    // 处理剩余的日志条目
    while (logQueue_.pop(entry, std::chrono::milliseconds(10))) {
        try {
            GlobalAppLog.AddLogSafe(entry.level, entry.message, entry.timestamp);
        } catch (...) {
            // 忽略关闭时的异常
        }
    }
}

std::string GetCurrentDateTimeString()
{
    char buffer[32];
    std::time_t now = std::time(nullptr);
    std::tm localTime;
    // 使用安全版本 localtime_s（Windows 平台）
    localtime_s(&localTime, &now);
    // 注意：月份范围为 [0,11]，因此需要加 1
    std::snprintf(buffer, sizeof(buffer), "%02d:%02d",
        localTime.tm_hour, localTime.tm_min);
    return std::string(buffer);
}

void AddLogInfo(LogLevel level, const std::string& message){
    // 现在通过线程安全的 LogManager 处理日志
    LogManager::getInstance()->addLog(level, message);
}

// Demonstrate creating a simple log window with basic filtering.
void ShowLog(bool* p_open)
{
    // •调用之前定义的 log.Draw 方法来绘制日志窗口，传入窗口标题 "Example: Log" 和可选开关 p_open。
    GlobalAppLog.Draw("Example: Log", p_open);
}

