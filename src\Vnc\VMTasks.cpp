#include "VMTasks.h"
#include "VMVision.h" 
#include <GUI/consolelog/consolelog.h>
#include "VNCControl.h" // For InputTask and handleInput
#include "ScreenFrameDistributor.h" // For coordinate transformation
#include <thread>
#include <chrono>
#include <random>

// --- DirectKeyboardMouseActionTask --- 

void DirectKeyboardMouseActionTask::execute(rfbClient* vncClient, VMVision* visionProcessor) {
    (void)visionProcessor; // Mark as unused for this task type

    KeyboardMouseResult result;
    result.success = true; // Assume success unless an exception occurs or client is null

    if (!vncClient) {
        result.success = false;
        result.errorMessage = "VNC client is null.";
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName_ + " [DirectKeyboardMouseTask] " + result.errorMessage);
        if (promise_) {
            try { promise_->set_value(result); } catch (const std::future_error& e) { AddLogInfo(LogLevel::Warning, "[Promise] set_value 多次调用: " + std::string(e.what())); }
        }
        return;
    }

    if (actions_.empty()) {
        result.errorMessage = "No actions provided to DirectKeyboardMouseActionTask.";
        AddLogInfo(LogLevel::Warning, "[DispatchCenter] " + vmName_ + " [DirectKeyboardMouseTask] " + result.errorMessage);
        if (promise_) {
            try { promise_->set_value(result); } catch (const std::future_error& e) { AddLogInfo(LogLevel::Warning, "[Promise] set_value 多次调用: " + std::string(e.what())); }
        }
        return;
    }

    try {
        // 识别常见的鼠标组合操作模式
        std::string logMessage;
        bool isRecognizedPattern = false;
        
        if (actions_.size() == 2 && 
            actions_[0].type == InputType::MOUSE_MOVE && 
            actions_[1].type == InputType::MOUSE_LEFT_CLICK) {
            // 鼠标移动+左键 = 左键点击
            logMessage = "左键点击 (" + std::to_string(actions_[0].x) + "," + std::to_string(actions_[0].y) + ")";
            isRecognizedPattern = true;
        }
        else if (actions_.size() == 2 && 
                 actions_[0].type == InputType::MOUSE_MOVE && 
                 actions_[1].type == InputType::MOUSE_RIGHT_CLICK) {
            // 鼠标移动+右键 = 右键点击
            logMessage = "右键点击 (" + std::to_string(actions_[0].x) + "," + std::to_string(actions_[0].y) + ")";
            isRecognizedPattern = true;
        }
        else if (actions_.size() == 3 && 
                 actions_[0].type == InputType::MOUSE_MOVE && 
                 actions_[1].type == InputType::MOUSE_LEFT_CLICK && 
                 actions_[2].type == InputType::MOUSE_LEFT_CLICK) {
            // 鼠标移动+左键+左键 = 双击
            logMessage = "双击 (" + std::to_string(actions_[0].x) + "," + std::to_string(actions_[0].y) + ")";
            isRecognizedPattern = true;
        }
        
        // 执行所有操作
        for (size_t i = 0; i < actions_.size(); ++i) {
            const auto& action = actions_[i];
            
            // 直接执行操作，不进行标题栏点击
            handleInput(vncClient, action); 
            
            // 在键盘操作之间添加延迟（除了最后一个操作）
            if (i < actions_.size() - 1 && action.type == InputType::KEYBOARD) {
                std::this_thread::sleep_for(std::chrono::milliseconds(50)); // 50ms延迟，模拟人类打字速度
            }
        }
        
        // 输出日志
        if (isRecognizedPattern) {
            // 使用识别出的操作模式描述
            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName_ + ": " + logMessage);
        } else {
            // 回退到通用的统计描述
            int mouseCount = 0, keyboardCount = 0;
            for (const auto& action : actions_) {
                if (action.type == InputType::KEYBOARD) {
                    keyboardCount++;
                } else {
                    mouseCount++;
                }
            }
            
            std::string summary;
            if (mouseCount > 0 && keyboardCount > 0) {
                summary = std::to_string(mouseCount) + "个鼠标操作 + " + std::to_string(keyboardCount) + "个键盘操作";
            } else if (mouseCount > 0) {
                summary = std::to_string(mouseCount) + "个鼠标操作";
            } else {
                summary = std::to_string(keyboardCount) + "个键盘操作";
            }
            AddLogInfo(LogLevel::Info, "[DispatchCenter] " + vmName_ + ": " + summary + " 执行成功");
        }
    } catch (const std::exception& e) {
        result.success = false;
        result.errorMessage = "Exception during direct keyboard/mouse action: " + std::string(e.what());
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName_ + " [DirectKeyboardMouseTask] " + result.errorMessage);
    } catch (...) {
        result.success = false;
        result.errorMessage = "Unknown exception during direct keyboard/mouse action.";
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName_ + " [DirectKeyboardMouseTask] " + result.errorMessage);
    }

    if (promise_) {
        try { promise_->set_value(result); } catch (const std::future_error& e) { AddLogInfo(LogLevel::Warning, "[Promise] set_value 多次调用: " + std::string(e.what())); }
    } else {
        AddLogInfo(LogLevel::Error, "[DispatchCenter] " + vmName_ + " [DirectKeyboardMouseTask] Promise is null after execution, cannot set value.");
    }
}




