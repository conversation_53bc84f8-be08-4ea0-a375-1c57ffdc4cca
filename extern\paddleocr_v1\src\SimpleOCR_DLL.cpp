#include "SimpleOCR_DLL.h"
#include "SimpleOCR.h"
#include <opencv2/opencv.hpp>
#include <string>
#include <cstring>
#include <memory>
#include <vector>

// 使用PIMPL模式包装SimpleOCR类
struct OCREngine {
    std::unique_ptr<PaddleOCR::SimpleOCR> ocr;
};

// 创建OCR引擎
PADDLEOCR_API void* OCR_Create(
    const char* det_model_dir,
    const char* rec_model_dir,
    const char* dict_path,
    int use_gpu,
    int gpu_id
) {
    try {
        OCREngine* engine = new OCREngine();
        engine->ocr = std::make_unique<PaddleOCR::SimpleOCR>(
            det_model_dir ? det_model_dir : "models/ch_PP-OCRv4_det_infer",
            rec_model_dir ? rec_model_dir : "models/ch_PP-OCRv4_rec_infer",
            dict_path ? dict_path : "models/ppocr_keys_v1.txt",
            use_gpu != 0,
            gpu_id
        );
        return engine;
    } catch (const std::exception& e) {
        // 创建OCR引擎错误
        return nullptr;
    }
}

// 释放OCR引擎
PADDLEOCR_API void OCR_Release(void* handle) {
    if (handle) {
        OCREngine* engine = static_cast<OCREngine*>(handle);
        delete engine;
    }
}

// 复制字符串到新分配的内存
char* strdup_safe(const std::string& str) {
    char* result = new char[str.length() + 1];
    std::strcpy(result, str.c_str());
    return result;
}

// 识别图像中的所有文本
PADDLEOCR_API int OCR_Recognize(
    void* handle,
    const unsigned char* image_data,
    int width,
    int height,
    int channels,
    OCRResult** results,
    int* count
) {
    if (!handle || !image_data || !results || !count || width <= 0 || height <= 0 || channels != 3) {
        return 0;
    }

    try {
        OCREngine* engine = static_cast<OCREngine*>(handle);
        
        // 创建OpenCV图像
        cv::Mat image(height, width, CV_8UC3, const_cast<unsigned char*>(image_data));
        
        // 执行OCR识别
        std::vector<PaddleOCR::OCRPredictResult> ocr_results = engine->ocr->recognize(image);
        
        // 分配结果数组
        *count = static_cast<int>(ocr_results.size());
        *results = new OCRResult[*count];
        
        // 填充结果
        for (int i = 0; i < *count; i++) {
            const auto& result = ocr_results[i];
            
            // 复制文本
            (*results)[i].text = strdup_safe(result.text);
            
            // 复制边界框
            int box_size = 0;
            for (const auto& point : result.box) {
                if (point.size() >= 2) {
                    box_size += 2;
                }
            }
            
            (*results)[i].box = new int[box_size];
            int idx = 0;
            for (const auto& point : result.box) {
                if (point.size() >= 2) {
                    (*results)[i].box[idx++] = point[0];
                    (*results)[i].box[idx++] = point[1];
                }
            }
            
            // 设置置信度
            (*results)[i].confidence = result.score;
            
            // 计算中心点
            cv::Point center = PaddleOCR::GetCenterPoint(result);
            (*results)[i].center_x = center.x;
            (*results)[i].center_y = center.y;
        }
        
        return 1;
    } catch (const std::exception& e) {
        // OCR识别错误
        return 0;
    }
}

// 查找图像中的特定文本
PADDLEOCR_API int OCR_FindText(
    void* handle,
    const unsigned char* image_data,
    int width,
    int height,
    int channels,
    const char* text,
    int* center_x,
    int* center_y,
    float* confidence
) {
    if (!handle || !image_data || !text || !center_x || !center_y || !confidence || 
        width <= 0 || height <= 0 || channels != 3) {
        return 0;
    }

    try {
        OCREngine* engine = static_cast<OCREngine*>(handle);
        
        // 创建OpenCV图像
        cv::Mat image(height, width, CV_8UC3, const_cast<unsigned char*>(image_data));
        
        // 查找文本
        auto [center, conf] = engine->ocr->findText(image, text);
        
        // 设置结果
        *center_x = center.x;
        *center_y = center.y;
        *confidence = conf;
        
        return 1;
    } catch (const std::exception& e) {
        // OCR查找文本错误
        return 0;
    }
}

// 释放OCR结果
PADDLEOCR_API void OCR_FreeResults(OCRResult* results, int count) {
    if (!results) {
        return;
    }
    
    for (int i = 0; i < count; i++) {
        delete[] results[i].text;
        delete[] results[i].box;
    }
    
    delete[] results;
}
