// 脚本解析器命令完整性测试脚本
//地名与坐标1110,30,170,25
//任务详情栏35,270,365,470
//中间任务执行栏470,270,350,480
//任务栏1050,400,230,200
slice(1) {
	# 移动鼠标到指定坐标
	# 参数: (x坐标, y坐标)
	MOUSE_MOVE(100, 200)

	# 在当前鼠标位置左键单击 (无参数)
	MOUSE_LEFT

	# 在当前鼠标位置右键单击 (无参数)
	MOUSE_RIGHT

	# 从当前位置拖动鼠标到目标坐标
	# 参数: (目标x坐标, 目标y坐标)
	MOUSE_DRAG(3000, 400)

	# 移动并左键单击
	# 参数: (x坐标, y坐标)
	MOUSE_MOVE_CLICK(100, 100)

	# 移动并双击
	# 参数: (x坐标, y坐标)
	MOUSE_MOVE_DOUBLE_CLICK(200, 200)

	# 移动并右键单击
	# 参数: (x坐标, y坐标)
	MOUSE_MOVE_RIGHT_CLICK(300, 300)

    	# 键盘输入组合键或字符序列
	# 参数: (按键1, 按键2, ...)
	KEYBOARD_INPUT(CTRL,S)
    	KEYBOARD_INPUT(Q, W, E, R)  //多键序列输入测试
    	KEYBOARD_INPUT(A, 1, F1, SPACE)  // 混合按键测试
    	KEYBOARD_INPUT(Hello, World)  // 大小写字母测试
   	KEYBOARD_INPUT(q, w, e, r)  // 小写字母测试
    
	# 延迟等待
	# 参数: (延迟毫秒数)
	DELAY(500)

}

slice(2) {

	# [文字匹配] 在全屏或指定区域内查找文字，并执行指定点击操作，4个区域参数省略即是全屏
	# 参数: (文字内容, 点击类型: left/right/double, 区域x, 区域y, 区域宽, 区域高)
	TEXT_MATCH(开始游戏, left, 300, 400, 200, 50)
	
	# [图片匹配] 在全屏或指定区域内查找与模板图片最匹配的位置，并执行指定点击操作，4个区域参数省略即是全屏
	# 参数: (模板图片名, 点击类型: left/right/double, 区域x, 区域y, 区域宽, 区域高, 精度: 0.0~1.0)
	VISUAL_MATCH(template_name, left, 0, 0, 1920, 1080, 0.8)

	# 等待某个画面（图片）是否出现，比如画面本没有登录界面，等待几秒后出现登录，该任务就能识别到，才会进行后续代码
	# 参数: (模板图片名, 检查间隔秒, 最大超时秒, 匹配精度阈值)
	WAIT_FOR_VISUAL_MATCH(password_field_active,3,30,0.85)

	# 等待固定坐标矩形区域，两次ocr文字识别出来的结果是否相同，不同则会一直阻塞，直到相同才会往后执行，用于检测画面固定区域是否移动
	# 参数: (x, y, 宽, 高, 检查间隔秒, 最大超时秒)
	WAIT_FOR_SCREEN_STILL(350,630,300,50,5,30)

}