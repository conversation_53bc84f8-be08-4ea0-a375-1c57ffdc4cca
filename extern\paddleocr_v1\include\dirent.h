#ifndef _DIRENT_H_
#define _DIRENT_H_


#ifdef _WIN32
#include <windows.h>
#include <tchar.h>
#include <stdio.h>
#include <stdbool.h>

#define DT_UNKNOWN 0
#define DT_DIR 4
#define DT_REG 8

struct dirent {
    ino_t d_ino;                /* 文件序列号 */
    unsigned char d_type;       /* 文件类型 */
    char d_name[MAX_PATH];      /* 文件名 */
};

typedef struct DIR DIR;

struct DIR {
    HANDLE hFind;
    WIN32_FIND_DATA findFileData;
    struct dirent dir_ent;
    bool first;
};

static DIR *opendir(const char *name) {
    DIR *dir = (DIR *)malloc(sizeof(DIR));
    if (!dir) return NULL;
    
    char path[MAX_PATH];
    _snprintf_s(path, MAX_PATH, _TRUNCATE, "%s\\*", name);
    
    dir->hFind = FindFirstFileA(path, &dir->findFileData);
    if (dir->hFind == INVALID_HANDLE_VALUE) {
        free(dir);
        return NULL;
    }
    
    dir->first = true;
    return dir;
}

static struct dirent *readdir(DIR *dir) {
    if (!dir) return NULL;
    
    if (dir->first) {
        dir->first = false;
    } else {
        if (!FindNextFileA(dir->hFind, &dir->findFileData)) {
            return NULL;
        }
    }
    
    strncpy_s(dir->dir_ent.d_name, MAX_PATH, dir->findFileData.cFileName, _TRUNCATE);
    dir->dir_ent.d_ino = 0;
    dir->dir_ent.d_type = (dir->findFileData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) ? DT_DIR : DT_REG;
    
    return &dir->dir_ent;
}

static int closedir(DIR *dir) {
    if (!dir) return -1;
    
    BOOL result = FindClose(dir->hFind);
    free(dir);
    
    return result ? 0 : -1;
}

#else
#include <dirent.h>
#endif /* _WIN32 */

#endif /* _DIRENT_H_ */
