﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{59168418-E8DF-376A-95DC-F861C6336062}"
	ProjectSection(ProjectDependencies) = postProject
		{5D28FE65-2C62-3B1E-904B-B52BE717B658} = {5D28FE65-2C62-3B1E-904B-B52BE717B658}
		{73D0C064-B0FF-3617-802D-B47B0AAF6715} = {73D0C064-B0FF-3617-802D-B47B0AAF6715}
		{267E8E2B-EAF6-3358-90DC-FB512B84A7E8} = {267E8E2B-EAF6-3358-90DC-FB512B84A7E8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{5D28FE65-2C62-3B1E-904B-B52BE717B658}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "imgui", "imgui.vcxproj", "{73D0C064-B0FF-3617-802D-B47B0AAF6715}"
	ProjectSection(ProjectDependencies) = postProject
		{5D28FE65-2C62-3B1E-904B-B52BE717B658} = {5D28FE65-2C62-3B1E-904B-B52BE717B658}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "qnyhMain2", "src\qnyhMain2.vcxproj", "{267E8E2B-EAF6-3358-90DC-FB512B84A7E8}"
	ProjectSection(ProjectDependencies) = postProject
		{5D28FE65-2C62-3B1E-904B-B52BE717B658} = {5D28FE65-2C62-3B1E-904B-B52BE717B658}
		{73D0C064-B0FF-3617-802D-B47B0AAF6715} = {73D0C064-B0FF-3617-802D-B47B0AAF6715}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{59168418-E8DF-376A-95DC-F861C6336062}.Debug|x64.ActiveCfg = Debug|x64
		{59168418-E8DF-376A-95DC-F861C6336062}.Release|x64.ActiveCfg = Release|x64
		{59168418-E8DF-376A-95DC-F861C6336062}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{59168418-E8DF-376A-95DC-F861C6336062}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{5D28FE65-2C62-3B1E-904B-B52BE717B658}.Debug|x64.ActiveCfg = Debug|x64
		{5D28FE65-2C62-3B1E-904B-B52BE717B658}.Debug|x64.Build.0 = Debug|x64
		{5D28FE65-2C62-3B1E-904B-B52BE717B658}.Release|x64.ActiveCfg = Release|x64
		{5D28FE65-2C62-3B1E-904B-B52BE717B658}.Release|x64.Build.0 = Release|x64
		{5D28FE65-2C62-3B1E-904B-B52BE717B658}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{5D28FE65-2C62-3B1E-904B-B52BE717B658}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{5D28FE65-2C62-3B1E-904B-B52BE717B658}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{5D28FE65-2C62-3B1E-904B-B52BE717B658}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{73D0C064-B0FF-3617-802D-B47B0AAF6715}.Debug|x64.ActiveCfg = Debug|x64
		{73D0C064-B0FF-3617-802D-B47B0AAF6715}.Debug|x64.Build.0 = Debug|x64
		{73D0C064-B0FF-3617-802D-B47B0AAF6715}.Release|x64.ActiveCfg = Release|x64
		{73D0C064-B0FF-3617-802D-B47B0AAF6715}.Release|x64.Build.0 = Release|x64
		{73D0C064-B0FF-3617-802D-B47B0AAF6715}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{73D0C064-B0FF-3617-802D-B47B0AAF6715}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{73D0C064-B0FF-3617-802D-B47B0AAF6715}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{73D0C064-B0FF-3617-802D-B47B0AAF6715}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{267E8E2B-EAF6-3358-90DC-FB512B84A7E8}.Debug|x64.ActiveCfg = Debug|x64
		{267E8E2B-EAF6-3358-90DC-FB512B84A7E8}.Debug|x64.Build.0 = Debug|x64
		{267E8E2B-EAF6-3358-90DC-FB512B84A7E8}.Release|x64.ActiveCfg = Release|x64
		{267E8E2B-EAF6-3358-90DC-FB512B84A7E8}.Release|x64.Build.0 = Release|x64
		{267E8E2B-EAF6-3358-90DC-FB512B84A7E8}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{267E8E2B-EAF6-3358-90DC-FB512B84A7E8}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{267E8E2B-EAF6-3358-90DC-FB512B84A7E8}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{267E8E2B-EAF6-3358-90DC-FB512B84A7E8}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {2C070436-8928-38F4-B74C-B0B2A3611C9F}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
