#include <windows.h>
#include <string>
#include <stdexcept>
#include <vector> 
#include <future> 
#include <Vnc/MouseBoardCode.h>
#include <GUI/consolelog/consolelog.h>
#include <Vnc/VMVision.h>// For cv::Rect if not transitively included
#include <Vnc/DispatchCenter.h>
#include <Vnc/VMTasks.h>  // For LabelLocateResult
#include <Vnc/VNCControl.h>  // For InputTask
#include <opencv2/core/types.hpp> // Explicitly include for cv::Rect
#include "Logic/common.h"
#include <thread>               // For std::this_thread::sleep_for
#include <chrono>
#include <GUI/tasklist/tasklist.h>
#include <filesystem>
#include <regex>          // 正则表达式支持
#include <sstream>        // 字符串流处理
#include <Core/ConfigManager.h> // 配置管理器
#include <Core/UnifiedThreadManager.h> // 统一线程管理器
#include <Vnc/VMStateManager.h> // VM状态管理器
#include <type_traits>    // For std::is_same_v and if constexpr support
#include <random>         // 随机数生成支持
#include <fstream>        // 文件流支持
#include <algorithm>      // 算法支持
#include <map>            // Map支持

// 线程本地存储的虚拟机名称
thread_local std::string threadLocalVmName;

// 获取统一线程管理器引用
static UnifiedThreadManager& getThreadManager() {
    return UnifiedThreadManager::getInstance();
}

// 设置线程本地存储的虚拟机名称
void SetThreadLocalVmName(const std::string& vmName) {
    threadLocalVmName = vmName;
}

// 获取线程本地存储的虚拟机名称
std::string GetThreadLocalVmName() {
    return threadLocalVmName.empty() ? DefaultVmName : threadLocalVmName;
}

// 图像匹配实现 - 增强版，确保线程安全
cv::Point performVisualMatch(const std::string& vmName, const std::string& templateName, int sliceNumber) {
    try {
        // 确保使用正确的虚拟机名称 - 优先使用传入的参数，其次使用线程本地存储
        std::string currentVmName = vmName.empty() ? GetThreadLocalVmName() : vmName;

        // 安全获取DispatchCenter实例
        auto* dispatchCenter = DispatchCenter::getInstance();
        if (!dispatchCenter) {
            AddLogInfo(LogLevel::Error, "DispatchCenter实例不可用，无法执行视觉匹配");
            return cv::Point(-1, -1);
        }

        // 提前检查虚拟机控制器是否可用
        VMControllerInfo* controllerInfo = dispatchCenter->getVMControllerInfo(currentVmName);
        if (!controllerInfo) {
            AddLogInfo(LogLevel::Error, "找不到虚拟机控制器 [" + currentVmName + "]，无法执行视觉匹配");
            return cv::Point(-1, -1);
        }

        // 使用新的异步任务接口
        // Signature: addImageMatchTask(vmName, templateName, threshold, imagePath, roi)
        std::future<VisionMatchResult> futureResult = dispatchCenter->addImageMatchTask(
            currentVmName,
            templateName,
            0.6, // Correct position for threshold (double)
            "",        // imagePath - assuming templateName is key enough
            cv::Rect(0, 0, 0, 0), // roi - default
            sliceNumber
        );

        VisionMatchResult result = futureResult.get(); // 等待匹配结果

        if (result.success) { // Check success (implies found)
            AddLogInfo(LogLevel::Info, currentVmName + " 使用阈值 " + std::to_string(0.6) + " 匹配成功. 位置: (" + std::to_string(result.matchLocation.x) + ", " + std::to_string(result.matchLocation.y) + ")");
            return result.matchLocation;
        }
        AddLogInfo(LogLevel::Error, currentVmName + " 模板匹配最终失败: " + templateName);
        return cv::Point(-1, -1); // 返回特殊值表示匹配失败
    }
    catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "视觉匹配异常: " + std::string(e.what()));
        return cv::Point(-1, -1);
    }
    catch (...) {
        AddLogInfo(LogLevel::Error, "视觉匹配发生未知异常");
        return cv::Point(-1, -1);
    }
}

// 文字识别功能 - 增强版，确保线程安全
bool wordsVisualMatch(const std::string& vmName, const std::string& matchWords, int sliceNumber) {
    try {
        // 确保使用正确的虚拟机名称
        std::string currentVmName = vmName.empty() ? GetThreadLocalVmName() : vmName;

        // 安全获取DispatchCenter实例
        auto* dispatchCenter = DispatchCenter::getInstance();
        if (!dispatchCenter) {
            AddLogInfo(LogLevel::Error, "DispatchCenter实例不可用，无法执行文字匹配");
            return false;
        }

        // 提前检查虚拟机控制器是否可用
        VMControllerInfo* controllerInfo = dispatchCenter->getVMControllerInfo(currentVmName);
        if (!controllerInfo) {
            AddLogInfo(LogLevel::Error, "找不到虚拟机控制器 [" + currentVmName + "]，无法执行文字匹配");
            return false;
        }

        // 使用新的异步任务接口
        std::future<WordsMatchResult> futureResult = dispatchCenter->addWordsMatchTask(
            currentVmName,
            { matchWords }, // wordsToMatch as std::vector<std::string>
            0.7,          // threshold - (double)
            cv::Rect{},// roi - empty Rect means whole screen
            sliceNumber
        );

        WordsMatchResult result = futureResult.get(); // Wait for the result

        if (result.success) { // Check success (implies found)
            AddLogInfo(LogLevel::Info, currentVmName + " 使用 '" + matchWords + "' 匹配成功. 位置: (" + std::to_string(result.matchLocation.x) + ", " + std::to_string(result.matchLocation.y) + ")");
                return true;
            }
        AddLogInfo(LogLevel::Error, currentVmName + " 文字匹配失败 for '" + matchWords + "'. " + result.errorMessage);
        return false;
    }
    catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "文字识别测试异常: " + std::string(e.what()));
        return false;
    }
    catch (...) {
        AddLogInfo(LogLevel::Error, "文字识别测试未知异常");
        return false;
    }
}

// 文本识别功能
TextRecognizeResult textRecognize(const std::string& vmName, cv::Rect roi, int sliceNumber) {
    try {
        // 确保使用正确的虚拟机名称
        std::string currentVmName = vmName.empty() ? GetThreadLocalVmName() : vmName;

        // 安全获取DispatchCenter实例
        auto* dispatchCenter = DispatchCenter::getInstance();
        if (!dispatchCenter) {
            AddLogInfo(LogLevel::Error, "DispatchCenter实例不可用，无法执行文本识别");
            TextRecognizeResult res;
            res.success = false;
            res.errorMessage = "DispatchCenter实例不可用";
            return res;
        }

        // 提前检查虚拟机控制器是否可用
        VMControllerInfo* controllerInfo = dispatchCenter->getVMControllerInfo(currentVmName);
        if (!controllerInfo) {
            AddLogInfo(LogLevel::Error, "找不到虚拟机控制器 [" + currentVmName + "]，无法执行文本识别");
            TextRecognizeResult res;
            res.success = false;
            res.errorMessage = "找不到虚拟机控制器";
            return res;
        }

        // 使用新的异步任务接口
        std::future<TextRecognizeResult> futureResult = dispatchCenter->addTextRecognizeTask(
            currentVmName,
            roi,
            sliceNumber
        );

        TextRecognizeResult result = futureResult.get(); // 等待识别结果

        if (result.success) {
            AddLogInfo(LogLevel::Info, currentVmName + " 文本识别成功，识别到 " + std::to_string(result.recognizedTexts.size()) + " 个文本");
            for (size_t i = 0; i < result.recognizedTexts.size(); ++i) {
                AddLogInfo(LogLevel::Info, currentVmName + " 识别文本 [" + std::to_string(i) + "]: '" + result.recognizedTexts[i] + "'");
            }
        } else {
            AddLogInfo(LogLevel::Error, currentVmName + " 文本识别失败: " + result.errorMessage);
        }
        
        return result;
    }
    catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "文本识别异常: " + std::string(e.what()));
        TextRecognizeResult res;
        res.success = false;
        res.errorMessage = "异常: " + std::string(e.what());
        return res;
    }
    catch (...) {
        AddLogInfo(LogLevel::Error, "文本识别发生未知异常");
        TextRecognizeResult res;
        res.success = false;
        res.errorMessage = "未知异常";
        return res;
    }
}

// 默认虚拟机名称，可以在 UI 中设置
std::string DefaultVmName = "VM1";

// ==========================================================================================
// ========================== 脚本解析和任务执行功能==========================
// ==========================================================================================

// 执行选定的任务
void ExecuteSelectedTasks(const std::string& vmName) {
    try {
        AddLogInfo(LogLevel::Info, "[ScriptExecutor] " + vmName + ": 开始执行选定的任务");
        
        // 获取选定的任务列表
        const auto& selectedTasks = GetSelectedTasks();
        if (selectedTasks.empty()) {
            AddLogInfo(LogLevel::Warning, "[ScriptExecutor] " + vmName + ": 没有选定的任务");
            return;
        }
        
        // 逐个执行每个选定的脚本
        for (const std::string& taskName : selectedTasks) {
            AddLogInfo(LogLevel::Info, "[ScriptExecutor] " + vmName + ": 开始执行任务 - " + taskName);
            
            // 构建脚本文件路径（这个函数在tasklist.cpp中定义）
            std::string scriptPath = GetScriptFilePath("主线", taskName); // 假设主要在"主线"分类
            
            // 读取脚本文件内容
            std::ifstream file(scriptPath);
            if (!file.is_open()) {
                AddLogInfo(LogLevel::Error, "[ScriptExecutor] " + vmName + ": 无法打开脚本文件: " + scriptPath);
                continue;
            }
            
            std::string scriptContent((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
            file.close();
            
            if (scriptContent.empty()) {
                AddLogInfo(LogLevel::Warning, "[ScriptExecutor] " + vmName + ": 脚本文件为空: " + scriptPath);
                continue;
            }
            
            // 加载并运行脚本
            loadAndRunScriptFromString(vmName, scriptContent);
        }
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptExecutor] " + vmName + ": 执行任务时发生异常: " + std::string(e.what()));
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[ScriptExecutor] " + vmName + ": 执行任务时发生未知异常");
    }
}

// 从字符串加载并运行脚本（恢复此功能）
void loadAndRunScriptFromString(const std::string& vmName, const std::string& scriptContent) {
    try {
        AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 开始解析脚本内容");

        auto* dispatchCenter = DispatchCenter::getInstance();
        if (!dispatchCenter) {
            AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": DispatchCenter不可用");
            return;
        }

        VMControllerInfo* controllerInfo = dispatchCenter->getVMControllerInfo(vmName);
        if (!controllerInfo) {
            AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": 找不到虚拟机控制器");
            return;
        }

        // ✅ 设置脚本解析状态为运行中
        auto* stateManager = VMStateManager::getInstance();
        if (stateManager) {
            stateManager->setScriptRunning(vmName, true);
        }

        // ========================== 新架构：解析与执行分离 ==========================
        
        // 阶段1：纯解析阶段 - 将脚本解析为结构化数据，不执行任何任务
        AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 阶段1 - 开始解析脚本结构");
        ScriptParseResult parseResult = parseScriptToResult(vmName, scriptContent, controllerInfo);
        
        if (!parseResult.parseSuccess) {
            AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": 脚本解析失败: " + parseResult.errorMessage);
            if (stateManager) {
                stateManager->setScriptRunning(vmName, false);
            }
            return;
        }
        
        AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 解析完成 - 共 " + 
                  std::to_string(parseResult.sliceResults.size()) + " 个切片，" + 
                  std::to_string(parseResult.totalTasks) + " 个任务");
        
        // 阶段2：清空现有队列
        for (auto& slicePair : controllerInfo->sliceQueues) {
            if (slicePair.second && slicePair.second->taskQueue) {
                slicePair.second->taskQueue->clear();
            }
        }
        
        // 阶段3：装载任务到队列
        bool loadSuccess = loadParsedTasksToQueues(vmName, parseResult, controllerInfo);
        
        if (!loadSuccess) {
            AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": 任务装载失败");
            if (stateManager) {
                stateManager->setScriptRunning(vmName, false);
            }
            return;
        }
        
        // ✅ 脚本解析完成，设置状态为非运行中
        if (stateManager) {
            stateManager->setScriptRunning(vmName, false);
        }

        // 阶段4：启动轮询器开始执行
        AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 阶段4 - 启动任务轮询器");
        dispatchCenter->startTaskPoller(vmName);

        AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 脚本解析和装载完成，轮询器已启动");

    } catch (const std::exception& e) {
        // ✅ 异常时也要清除脚本运行状态
        auto* stateManager = VMStateManager::getInstance();
        if (stateManager) {
            stateManager->setScriptRunning(vmName, false);
        }
        AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": 脚本解析异常: " + std::string(e.what()));
    } catch (...) {
        // ✅ 异常时也要清除脚本运行状态
        auto* stateManager = VMStateManager::getInstance();
        if (stateManager) {
            stateManager->setScriptRunning(vmName, false);
        }
        AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": 脚本解析发生未知异常");
    }
}

// ========================== 新增：解析与执行分离的实现函数 ==========================

// 纯解析函数：将脚本内容解析为结构化数据，不执行任何任务
ScriptParseResult parseScriptToResult(const std::string& vmName, const std::string& scriptContent, VMControllerInfo* controllerInfo) {
    ScriptParseResult result;
    
    if (!controllerInfo) {
        result.parseSuccess = false;
        result.errorMessage = "VMControllerInfo为空";
        AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": " + result.errorMessage);
        return result;
    }
    
    try {
        // 按行分割脚本内容
        std::vector<std::string> lines;
        std::stringstream ss(scriptContent);
        std::string line;
        while (std::getline(ss, line)) {
            lines.push_back(line);
        }
        
        // 获取可用的切片号
        std::vector<int> availableSlices;
        for (const auto& slicePair : controllerInfo->sliceQueues) {
            availableSlices.push_back(slicePair.first);
        }
        std::sort(availableSlices.begin(), availableSlices.end());
        
        if (availableSlices.empty()) {
            result.parseSuccess = false;
            result.errorMessage = "没有可用的切片";
            AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": " + result.errorMessage);
            return result;
        }
        
        // 输出可用切片信息
        std::string sliceInfo = "";
        for (int slice : availableSlices) {
            sliceInfo += std::to_string(slice) + " ";
        }
        AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 可用切片号: " + sliceInfo);
        
        // 解析每个切片的任务
        int currentSlice = -1;
        SliceParseResult* currentSliceResult = nullptr;
        
        // 第一遍扫描：收集所有标签
        for (size_t i = 0; i < lines.size(); ++i) {
            std::string trimmedLine = trim(lines[i]);
            if (trimmedLine.find("LABEL:") == 0) {
                std::string labelName = trimmedLine.substr(6); // 去掉"LABEL:"前缀
                labelName = trim(labelName);
                result.labelMap[labelName] = static_cast<int>(i);
            }
        }
        
        // 第二遍扫描：解析命令结构
        for (size_t i = 0; i < lines.size(); ++i) {
            std::string trimmedLine = trim(lines[i]);
            
            // 跳过空行和注释
            if (trimmedLine.empty() || trimmedLine[0] == '/' || trimmedLine[0] == '#') {
                continue;
            }
            
            // 检查切片开始标记
            if (trimmedLine.find("slice(") == 0) {
                size_t start = trimmedLine.find('(') + 1;
                size_t end = trimmedLine.find(')', start);
                if (start != std::string::npos && end != std::string::npos) {
                    try {
                        int sliceNumber = std::stoi(trimmedLine.substr(start, end - start));
                        if (std::find(availableSlices.begin(), availableSlices.end(), sliceNumber) != availableSlices.end()) {
                            currentSlice = sliceNumber;
                            
                            // 创建新的切片解析结果
                            result.sliceResults[sliceNumber] = SliceParseResult(sliceNumber);
                            currentSliceResult = &result.sliceResults[sliceNumber];
                            
                            AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 开始解析切片 " + std::to_string(currentSlice));
                        } else {
                            AddLogInfo(LogLevel::Warning, "[ScriptParser] " + vmName + ": 切片号 " + std::to_string(sliceNumber) + " 不在可用切片列表中，跳过");
                            currentSlice = -1;
                            currentSliceResult = nullptr;
                        }
                    } catch (const std::exception& e) {
                        AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": 解析切片号失败: " + std::string(e.what()));
                        currentSlice = -1;
                        currentSliceResult = nullptr;
                    }
                }
                continue;
            }
            
            // 检查切片结束标记
            if (trimmedLine == "}") {
                if (currentSlice != -1 && currentSliceResult) {
                    AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 切片 " + std::to_string(currentSlice) + 
                              " 解析完成，共 " + std::to_string(currentSliceResult->taskCount) + " 个任务");
                }
                currentSlice = -1;
                currentSliceResult = nullptr;
                continue;
            }
            
            // 在有效切片内解析命令
            if (currentSlice != -1 && currentSliceResult) {
                // 跳过标签定义行
                if (trimmedLine.find("LABEL:") == 0) {
                    continue;
                }
                
                // 解析命令
                ParsedTask parsedTask = parseCommandLine(trimmedLine, static_cast<int>(i + 1), lines[i]);
                if (!parsedTask.command.empty()) {
                    currentSliceResult->addTask(parsedTask);
                }
            }
        }
        
        // 统计总任务数
        for (const auto& slicePair : result.sliceResults) {
            result.totalTasks += slicePair.second.taskCount;
        }
        
        
        result.parseSuccess = true;
        
    } catch (const std::exception& e) {
        result.parseSuccess = false;
        result.errorMessage = "解析异常: " + std::string(e.what());
        AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": " + result.errorMessage);
    } catch (...) {
        result.parseSuccess = false;
        result.errorMessage = "解析发生未知异常";
        AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": " + result.errorMessage);
    }
    
    return result;
}

// 辅助函数：解析单行命令
ParsedTask parseCommandLine(const std::string& line, int lineNumber, const std::string& originalLine) {
    ParsedTask result("", {}, lineNumber, originalLine);
    
    try {
        std::string trimmedLine = trim(line);
        
        // 解析命令名称和参数
        size_t parenPos = trimmedLine.find('(');
        if (parenPos == std::string::npos) {
            // 不是有效的命令格式
            return result;
        }
        
        std::string commandName = trimmedLine.substr(0, parenPos);
        std::string argsStr = trimmedLine.substr(parenPos + 1, trimmedLine.length() - parenPos - 2);
        
        // 解析参数
        std::vector<std::string> args = parseArguments(argsStr);
        
        result.command = commandName;
        result.args = args;
        
    } catch (const std::exception& e) {
        // 解析失败，返回空命令
        result.command = "";
    }
    
    return result;
}

// 将解析结果装载到切片队列
bool loadParsedTasksToQueues(const std::string& vmName, const ScriptParseResult& parseResult, VMControllerInfo* controllerInfo) {
    if (!controllerInfo) {
        AddLogInfo(LogLevel::Error, "[ScriptLoader] " + vmName + ": VMControllerInfo为空");
        return false;
    }
    
    auto* dispatchCenter = DispatchCenter::getInstance();
    if (!dispatchCenter) {
        AddLogInfo(LogLevel::Error, "[ScriptLoader] " + vmName + ": DispatchCenter不可用");
        return false;
    }
    
    try {
        int totalLoadedTasks = 0;
        
        // 逐个切片装载任务
        for (const auto& slicePair : parseResult.sliceResults) {
            int sliceNumber = slicePair.first;
            const SliceParseResult& sliceResult = slicePair.second;
            
            AddLogInfo(LogLevel::Info, "[ScriptLoader] " + vmName + ": 开始装载切片 " + 
                      std::to_string(sliceNumber) + " 的 " + std::to_string(sliceResult.taskCount) + " 个任务");
            
            // 检查切片队列是否存在
            auto sliceIt = controllerInfo->sliceQueues.find(sliceNumber);
            if (sliceIt == controllerInfo->sliceQueues.end() || !sliceIt->second || !sliceIt->second->taskQueue) {
                AddLogInfo(LogLevel::Error, "[ScriptLoader] " + vmName + ": 切片 " + std::to_string(sliceNumber) + " 的队列不存在");
                continue;
            }
            
            int sliceLoadedTasks = 0;
            // 逐个任务装载
            for (const ParsedTask& parsedTask : sliceResult.tasks) {
                // 直接执行任务创建逻辑
                bool taskCreated = executeTaskCreation(vmName, parsedTask, sliceNumber, "", parseResult.labelMap, dispatchCenter);
                
                if (taskCreated) {
                    sliceLoadedTasks++;
                    totalLoadedTasks++;
                    
                    AddLogInfo(LogLevel::Debug, "[ScriptLoader] " + vmName + ": 切片 " + std::to_string(sliceNumber) + 
                              " 装载任务: " + parsedTask.command + " (第 " + std::to_string(parsedTask.lineNumber) + " 行)");
                } else {
                    AddLogInfo(LogLevel::Warning, "[ScriptLoader] " + vmName + ": 切片 " + std::to_string(sliceNumber) + 
                              " 任务创建失败: " + parsedTask.command + " (第 " + std::to_string(parsedTask.lineNumber) + " 行)");
                }
            }
            
            AddLogInfo(LogLevel::Info, "[ScriptLoader] " + vmName + ": 切片 " + std::to_string(sliceNumber) + 
                      " 装载完成 - " + std::to_string(sliceLoadedTasks) + "/" + std::to_string(sliceResult.taskCount) + " 个任务");
        }
        
        AddLogInfo(LogLevel::Info, "[ScriptLoader] " + vmName + ": 所有任务装载完成 - 共 " + std::to_string(totalLoadedTasks) + " 个任务");
        return true;
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptLoader] " + vmName + ": 任务装载异常: " + std::string(e.what()));
        return false;
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[ScriptLoader] " + vmName + ": 任务装载发生未知异常");
        return false;
    }
}

// 工具函数：去除字符串首尾空白
std::string trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\n\r");
    if (start == std::string::npos) return "";
    size_t end = str.find_last_not_of(" \t\n\r");
    return str.substr(start, end - start + 1);
}

// 工具函数：解析命令参数
std::vector<std::string> parseArguments(const std::string& argsStr) {
    std::vector<std::string> args;
    if (argsStr.empty()) return args;
    
    std::stringstream ss(argsStr);
    std::string arg;
    while (std::getline(ss, arg, ',')) {
        args.push_back(trim(arg));
    }
    return args;
}

// ==========================================================================================
// ======================= 任务创建函数（将命令转换为任务对象） ========================
// ==========================================================================================

// 创建文字匹配任务
void createTextMatchTask(const std::string& vmName, const std::vector<std::string>& args, 
                        int sliceNumber, DispatchCenter* dispatchCenter) {
    if (args.size() < 2) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] TEXT_MATCH命令参数不足");
        return;
    }
    
    try {
        std::string text = args[0];
        std::string clickType = args[1];
        
        // 默认ROI和阈值
        cv::Rect roi(0, 0, 0, 0);  // 全屏
        double threshold = 0.7;
        
        // 解析可选的ROI参数
        if (args.size() >= 6) {
            int x = std::stoi(args[2]);
            int y = std::stoi(args[3]);
            int width = std::stoi(args[4]);
            int height = std::stoi(args[5]);
            roi = cv::Rect(x, y, width, height);
        }
        
        // 解析可选的阈值参数
        if (args.size() >= 7) {
            threshold = std::stod(args[6]);
        }
        
        // 使用DispatchCenter的API创建文字匹配任务
        auto future = dispatchCenter->addWordsMatchTask(
            vmName, 
            std::vector<std::string>{text}, 
            threshold, 
            roi, 
            sliceNumber, 
            clickType  // 直接使用字符串类型
        );
        
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建TEXT_MATCH任务失败: " + std::string(e.what()));
    }
}

// 创建图像匹配任务
void createVisualMatchTask(const std::string& vmName, const std::vector<std::string>& args, 
                          int sliceNumber, DispatchCenter* dispatchCenter) {
    if (args.size() < 2) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] VISUAL_MATCH命令参数不足");
        return;
    }
    
    try {
        std::string templateName = args[0];
        std::string clickType = args[1];
        
        // 默认ROI和阈值
        cv::Rect roi(0, 0, 0, 0);  // 全屏
        double threshold = 0.8;
        
        // 解析可选的ROI参数
        if (args.size() >= 6) {
            int x = std::stoi(args[2]);
            int y = std::stoi(args[3]);
            int width = std::stoi(args[4]);
            int height = std::stoi(args[5]);
            roi = cv::Rect(x, y, width, height);
        }
        
        // 解析可选的阈值参数
        if (args.size() >= 7) {
            threshold = std::stod(args[6]);
        }
        
        // 使用DispatchCenter的API创建图像匹配任务
        auto future = dispatchCenter->addImageMatchTask(
            vmName, 
            templateName, 
            threshold, 
            "", // imagePath
            roi, 
            sliceNumber, 
            clickType  // 直接使用字符串类型
        );
        
        AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 已创建VISUAL_MATCH任务 - " + templateName);
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建VISUAL_MATCH任务失败: " + std::string(e.what()));
    }
}

// 创建鼠标移动任务
void createMouseMoveTask(const std::string& vmName, const std::vector<std::string>& args, 
                        int sliceNumber, DispatchCenter* dispatchCenter) {
    if (args.size() < 2) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] MOUSE_MOVE命令参数不足");
        return;
    }
    
    try {
        int x = std::stoi(args[0]);
        int y = std::stoi(args[1]);
        
        // 使用DispatchCenter的API创建鼠标移动任务
        std::vector<InputTask> actions;
        InputTask moveAction;
        moveAction.type = InputType::MOUSE_MOVE;
        moveAction.x = x;
        moveAction.y = y;
        actions.push_back(moveAction);
        
        auto future = dispatchCenter->addDirectKeyboardMouseActionTask(vmName, actions, sliceNumber);
        
        AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 已创建MOUSE_MOVE任务 (" + std::to_string(x) + ", " + std::to_string(y) + ")");
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建MOUSE_MOVE任务失败: " + std::string(e.what()));
    }
}

// 创建鼠标点击任务
void createMouseClickTask(const std::string& vmName, const std::vector<std::string>& args, 
                         int sliceNumber, const std::string& clickType, DispatchCenter* dispatchCenter) {
    try {
        InputType inputType = InputType::MOUSE_LEFT_CLICK;
        
        if (clickType == "right") {
            inputType = InputType::MOUSE_RIGHT_CLICK;
        } else if (clickType == "double") {
            inputType = InputType::MOUSE_LEFT_CLICK; // 双击会需要特殊处理
        }
        
        std::vector<InputTask> actions;
        
        // 如果有坐标参数，先移动再点击
        if (args.size() >= 2) {
            int x = std::stoi(args[0]);
            int y = std::stoi(args[1]);
            
            // 先创建移动任务
            InputTask moveAction;
            moveAction.type = InputType::MOUSE_MOVE;
            moveAction.x = x;
            moveAction.y = y;
            actions.push_back(moveAction);
            
            // 再创建点击任务
            InputTask clickAction;
            clickAction.type = inputType;
            clickAction.x = x;
            clickAction.y = y;
            actions.push_back(clickAction);
            
            // 双击需要两次点击
            if (clickType == "double") {
                actions.push_back(clickAction);
            }
            
            AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 已创建MOUSE_" + clickType + "任务 (" + std::to_string(x) + ", " + std::to_string(y) + ")");
        } else {
            // 在当前位置点击
            InputTask clickAction;
            clickAction.type = inputType;
            clickAction.x = 0;
            clickAction.y = 0;
            actions.push_back(clickAction);
            
            // 双击需要两次点击
            if (clickType == "double") {
                actions.push_back(clickAction);
            }
            
            AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 已创建MOUSE_" + clickType + "任务（当前位置）");
        }
        
        auto future = dispatchCenter->addDirectKeyboardMouseActionTask(vmName, actions, sliceNumber);
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建MOUSE_" + clickType + "任务失败: " + std::string(e.what()));
    }
}

// 创建键盘输入任务
void createKeyboardInputTask(const std::string& vmName, const std::vector<std::string>& args, 
                           int sliceNumber, bool isTitleBar, DispatchCenter* dispatchCenter) {
    if (args.empty()) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] KEYBOARD_INPUT命令参数不足");
        return;
    }
    
    try {
        // 创建键盘输入任务序列
        std::vector<InputTask> actions;
        
        // 如果是标题栏输入，先点击标题栏激活窗口
        if (isTitleBar) {
            // 点击标题栏中央位置激活窗口 (假设1920x1080分辨率，标题栏在顶部中央)
            InputTask titleBarClick;
            titleBarClick.type = InputType::MOUSE_LEFT_CLICK;
            titleBarClick.x = 960;  // 屏幕中央
            titleBarClick.y = 15;   // 标题栏高度
            actions.push_back(titleBarClick);
            
            // 等待窗口激活
            InputTask activationDelay;
            activationDelay.type = InputType::DELAY;
            activationDelay.delayMs = 200;
            actions.push_back(activationDelay);
        }
        
        // 添加键盘输入序列
        for (const auto& key : args) {
            // 使用createKeyboardTask函数正确创建键盘任务
            InputTask keyAction = createKeyboardTask(key);
            actions.push_back(keyAction);
            
            // 键盘输入之间添加随机延迟 (200ms-500ms)
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<> dis(200, 500);
            int randomDelay = dis(gen);
            
            InputTask keyDelay;
            keyDelay.type = InputType::DELAY;
            keyDelay.delayMs = randomDelay;
            actions.push_back(keyDelay);
        }
        
        auto future = dispatchCenter->addDirectKeyboardMouseActionTask(vmName, actions, sliceNumber);
        
        std::string taskType = isTitleBar ? "TITLEBAR_KEYBOARD_INPUT" : "KEYBOARD_INPUT";
        std::string keySequence = "";
        for (size_t i = 0; i < args.size(); ++i) {
            if (i > 0) keySequence += ",";
            keySequence += args[i];
        }
        AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 已创建" + taskType + "任务 - " + keySequence);
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建KEYBOARD_INPUT任务失败: " + std::string(e.what()));
    }
}

// 创建延时任务
void createDelayTask(const std::string& vmName, const std::vector<std::string>& args,
                    int sliceNumber, DispatchCenter* dispatchCenter) {
    if (args.empty()) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] DELAY命令参数不足");
        return;
    }

    try {
        int delayMs = std::stoi(args[0]);

        // ✅ 修复：创建延时任务而不是直接执行
        // 使用特殊的InputTask来表示延时操作
        std::vector<InputTask> actions;
        InputTask delayAction;
        delayAction.type = InputType::DELAY;  // 需要在InputType枚举中添加DELAY
        delayAction.delayMs = delayMs;        // 需要在InputTask结构中添加delayMs字段
        actions.push_back(delayAction);

        auto future = dispatchCenter->addDirectKeyboardMouseActionTask(vmName, actions, sliceNumber);

        AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 已创建DELAY任务 - " + std::to_string(delayMs) + "ms");

    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建DELAY任务失败: " + std::string(e.what()));
    }
}

// 创建标签定位任务
void createLabelLocateTask(const std::string& vmName, const std::vector<std::string>& args, 
                          int sliceNumber, const std::string& scriptContent, DispatchCenter* dispatchCenter) {
    if (args.empty()) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] LABEL_LOCATE命令参数不足");
        return;
    }
    
    try {
        // 检查是ROI模式还是文本模式
        if (args.size() >= 4) {
            // ROI模式：LABEL_LOCATE(x, y, width, height)
            int x = std::stoi(args[0]);
            int y = std::stoi(args[1]);
            int width = std::stoi(args[2]);
            int height = std::stoi(args[3]);
            cv::Rect roi(x, y, width, height);
            
            // 使用DispatchCenter的API创建标签定位任务（ROI模式）
            auto future = dispatchCenter->addLabelLocateTask(vmName, roi, sliceNumber, scriptContent);
            
            AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 已创建LABEL_LOCATE任务（ROI模式）");
            
        } else {
            // 文本模式：LABEL_LOCATE(targetText)
            std::string targetText = args[0];
            
            // 使用DispatchCenter的API创建标签定位任务（文本模式）
            auto future = dispatchCenter->addLabelLocateTask(vmName, targetText, sliceNumber, scriptContent);
            
            AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 已创建LABEL_LOCATE任务（文本模式） - " + targetText);
        }
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建LABEL_LOCATE任务失败: " + std::string(e.what()));
    }
}

// 创建等待画面静止任务
void createWaitForScreenStillTask(const std::string& vmName, const std::vector<std::string>& args, 
                                 int sliceNumber, DispatchCenter* dispatchCenter) {
    if (args.size() < 6) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] WAIT_FOR_SCREEN_STILL命令参数不足");
        return;
    }
    
    try {
        int x = std::stoi(args[0]);
        int y = std::stoi(args[1]);
        int width = std::stoi(args[2]);
        int height = std::stoi(args[3]);
        int checkInterval = std::stoi(args[4]);
        int maxTimeout = std::stoi(args[5]);
        
        cv::Rect roi(x, y, width, height);
        
        // 使用DispatchCenter的API创建等待画面静止任务
        auto future = dispatchCenter->addWaitForScreenStillTask(
            vmName, 
            sliceNumber, 
            x, y, width, height, 
            checkInterval, 
            maxTimeout
        );
        
        AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 已创建WAIT_FOR_SCREEN_STILL任务");
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建WAIT_FOR_SCREEN_STILL任务失败: " + std::string(e.what()));
    }
}

// 创建文本识别任务
void createTextRecognizeTask(const std::string& vmName, const std::vector<std::string>& args, 
                           int sliceNumber, DispatchCenter* dispatchCenter) {
    if (args.size() < 4) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] TEXT_RECOGNIZE命令参数不足");
        return;
    }
    
    try {
        int x = std::stoi(args[0]);
        int y = std::stoi(args[1]);
        int width = std::stoi(args[2]);
        int height = std::stoi(args[3]);
        
        cv::Rect roi(x, y, width, height);
        
        // 使用DispatchCenter的API创建文本识别任务
        auto future = dispatchCenter->addTextRecognizeTask(vmName, roi, sliceNumber);
        
        AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 已创建TEXT_RECOGNIZE任务");
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建TEXT_RECOGNIZE任务失败: " + std::string(e.what()));
    }
}

// 创建视觉鼠标拖动任务
void createVisionMouseDragTask(const std::string& vmName, const std::vector<std::string>& args, 
                              int sliceNumber, DispatchCenter* dispatchCenter) {
    if (args.size() < 8) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] VISION_MOUSE_DRAG命令参数不足");
        return;
    }
    
    try {
        std::string templateName = args[0];
        int roiX = std::stoi(args[1]);
        int roiY = std::stoi(args[2]);
        int roiWidth = std::stoi(args[3]);
        int roiHeight = std::stoi(args[4]);
        int targetX = std::stoi(args[5]);
        int targetY = std::stoi(args[6]);
        double threshold = std::stod(args[7]);
        
        cv::Rect roi(roiX, roiY, roiWidth, roiHeight);
        
        // 先进行图像匹配找到目标位置
        auto matchFuture = dispatchCenter->addImageMatchTask(
            vmName, 
            templateName, 
            threshold, 
            "", // imagePath
            roi, 
            sliceNumber, 
            "none" // 不点击，只匹配
        );
        
        // 等待匹配结果
        auto matchResult = matchFuture.get();
        
        if (matchResult.success) {
            // 匹配成功，执行拖动操作
            std::vector<InputTask> actions;
            
            // 移动到匹配位置
            InputTask moveAction;
            moveAction.type = InputType::MOUSE_MOVE;
            moveAction.x = matchResult.matchLocation.x;
            moveAction.y = matchResult.matchLocation.y;
            actions.push_back(moveAction);
            
            // 按下鼠标左键
            InputTask downAction;
            downAction.type = InputType::MOUSE_LEFT_CLICK;
            actions.push_back(downAction);
            
            // 移动到目标位置
            InputTask dragAction;
            dragAction.type = InputType::DRAGMOUSE;
            dragAction.x = targetX;
            dragAction.y = targetY;
            actions.push_back(dragAction);
            
            // 释放鼠标左键
            InputTask upAction;
            upAction.type = InputType::MOUSE_LEFT_CLICK;
            actions.push_back(upAction);
            
            auto dragFuture = dispatchCenter->addDirectKeyboardMouseActionTask(vmName, actions, sliceNumber);
            
            AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 已创建VISION_MOUSE_DRAG任务 - 从(" + 
                       std::to_string(matchResult.matchLocation.x) + "," + std::to_string(matchResult.matchLocation.y) + 
                       ") 拖动到 (" + std::to_string(targetX) + "," + std::to_string(targetY) + ")");
        } else {
            AddLogInfo(LogLevel::Error, "[ScriptParser] " + vmName + ": VISION_MOUSE_DRAG匹配失败，无法执行拖动");
        }
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建VISION_MOUSE_DRAG任务失败: " + std::string(e.what()));
    }
}

// 创建等待视觉匹配任务
void createWaitForVisualMatchTask(const std::string& vmName, const std::vector<std::string>& args, 
                                 int sliceNumber, DispatchCenter* dispatchCenter) {
    if (args.size() < 4) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] WAIT_FOR_VISUAL_MATCH命令参数不足");
        return;
    }
    
    try {
        std::string templateName = args[0];
        int checkIntervalSeconds = std::stoi(args[1]);
        int maxTimeoutSeconds = std::stoi(args[2]);
        double threshold = std::stod(args[3]);
        
        // 使用DispatchCenter的API创建等待视觉匹配任务
        auto future = dispatchCenter->addWaitForVisualMatchTask(
            vmName, 
            sliceNumber, 
            templateName, 
            checkIntervalSeconds, 
            maxTimeoutSeconds, 
            threshold
        );
        
        AddLogInfo(LogLevel::Info, "[ScriptParser] " + vmName + ": 已创建WAIT_FOR_VISUAL_MATCH任务 - " + templateName);
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptParser] 创建WAIT_FOR_VISUAL_MATCH任务失败: " + std::string(e.what()));
    }
}

// ==========================================================================================
// ======================= 核心任务创建函数（解析与执行分离架构） =======================
// ==========================================================================================

// 执行任务创建逻辑（解析与执行分离架构）
bool executeTaskCreation(const std::string& vmName, const ParsedTask& parsedTask, int sliceNumber,
                        const std::string& scriptContent, const std::map<std::string, int>& labelMap, 
                        DispatchCenter* dispatchCenter) {
    if (!dispatchCenter) {
        AddLogInfo(LogLevel::Error, "[ScriptLoader] " + vmName + ": DispatchCenter不可用");
        return false;
    }
    
    try {
        const std::string& command = parsedTask.command;
        const std::vector<std::string>& args = parsedTask.args;
        
        // ========== 基础命令（按命令教程顺序） ==========
        if (command == "MOUSE_MOVE") {
            createMouseMoveTask(vmName, args, sliceNumber, dispatchCenter);
            return true;
            
        } else if (command == "MOUSE_LEFT") {
            createMouseClickTask(vmName, args, sliceNumber, "left", dispatchCenter);
            return true;
            
        } else if (command == "MOUSE_RIGHT") {
            createMouseClickTask(vmName, args, sliceNumber, "right", dispatchCenter);
            return true;
            
        } else if (command == "MOUSE_DOUBLE") {
            createMouseClickTask(vmName, args, sliceNumber, "double", dispatchCenter);
            return true;
            
        } else if (command == "KEYBOARD_INPUT") {
            createKeyboardInputTask(vmName, args, sliceNumber, false, dispatchCenter);
            return true;
            
        } else if (command == "TITLEBAR_KEYBOARD_INPUT") {
            createKeyboardInputTask(vmName, args, sliceNumber, true, dispatchCenter);
            return true;
            
        } else if (command == "LABEL_LOCATE") {
            createLabelLocateTask(vmName, args, sliceNumber, scriptContent, dispatchCenter);
            return true;
            
        } else if (command == "DELAY") {
            createDelayTask(vmName, args, sliceNumber, dispatchCenter);
            return true;
            
        } else if (command == "TEXT_RECOGNIZE") {
            createTextRecognizeTask(vmName, args, sliceNumber, dispatchCenter);
            return true;
            
        } else if (command == "TEXT_MATCH") {
            createTextMatchTask(vmName, args, sliceNumber, dispatchCenter);
            return true;
            
        } else if (command == "VISUAL_MATCH") {
            createVisualMatchTask(vmName, args, sliceNumber, dispatchCenter);
            return true;
            
        } else if (command == "WAIT_FOR_VISUAL_MATCH") {
            createWaitForVisualMatchTask(vmName, args, sliceNumber, dispatchCenter);
            return true;
            
        } else if (command == "WAIT_FOR_SCREEN_STILL") {
            createWaitForScreenStillTask(vmName, args, sliceNumber, dispatchCenter);
            return true;
            
        } else if (command == "VISION_MOUSE_DRAG") {
            createVisionMouseDragTask(vmName, args, sliceNumber, dispatchCenter);
            return true;
            
        } else {
            AddLogInfo(LogLevel::Warning, "[ScriptLoader] " + vmName + ": 未知命令类型: " + command + " (第 " + std::to_string(parsedTask.lineNumber) + " 行)");
            return false;
        }
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptLoader] " + vmName + ": 创建任务失败: " + std::string(e.what()) + " (第 " + std::to_string(parsedTask.lineNumber) + " 行)");
        return false;
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[ScriptLoader] " + vmName + ": 创建任务发生未知异常 (第 " + std::to_string(parsedTask.lineNumber) + " 行)");
        return false;
    }
}

// 从解析结果创建任务对象（向后兼容性，已废弃）
std::shared_ptr<IVMTask> createTaskFromParsedTask(const std::string& vmName, const ParsedTask& parsedTask, 
                                                  const std::string& scriptContent, const std::map<std::string, int>& labelMap, 
                                                  DispatchCenter* dispatchCenter) {
    if (!dispatchCenter) {
        AddLogInfo(LogLevel::Error, "[ScriptLoader] " + vmName + ": DispatchCenter不可用");
        return nullptr;
    }
    
    try {
        const std::string& command = parsedTask.command;
        const std::vector<std::string>& args = parsedTask.args;
        
        AddLogInfo(LogLevel::Debug, "[ScriptLoader] " + vmName + ": 创建任务 " + command + " (第 " + std::to_string(parsedTask.lineNumber) + " 行)");
        
        // 注意：由于当前create*Task函数是直接添加到队列的设计，
        // 这里我们调用这些函数后返回一个虚拟的任务对象
        // TODO: 将来可以重构create*Task函数让它们返回实际的任务对象
        
        // ========== 废弃函数中的命令映射（保持与新架构一致） ==========
        if (command == "MOUSE_MOVE") {
            createMouseMoveTask(vmName, args, -1, dispatchCenter);
            return nullptr;  // 已废弃，统一返回nullptr
            
        } else if (command == "MOUSE_LEFT") {
            createMouseClickTask(vmName, args, -1, "left", dispatchCenter);
            return nullptr;  // 已废弃，统一返回nullptr
            
        } else if (command == "MOUSE_RIGHT") {
            createMouseClickTask(vmName, args, -1, "right", dispatchCenter);
            return nullptr;  // 已废弃，统一返回nullptr
            
        } else if (command == "MOUSE_DOUBLE") {
            createMouseClickTask(vmName, args, -1, "double", dispatchCenter);
            return nullptr;  // 已废弃，统一返回nullptr
            
        } else if (command == "KEYBOARD_INPUT") {
            createKeyboardInputTask(vmName, args, -1, false, dispatchCenter);
            return nullptr;  // 已废弃，统一返回nullptr
            
        } else if (command == "TITLEBAR_KEYBOARD_INPUT") {
            createKeyboardInputTask(vmName, args, -1, true, dispatchCenter);
            return nullptr;  // 已废弃，统一返回nullptr
            
        } else if (command == "LABEL_LOCATE") {
            createLabelLocateTask(vmName, args, -1, scriptContent, dispatchCenter);
            return nullptr;  // 已废弃，统一返回nullptr
            
        } else if (command == "DELAY") {
            createDelayTask(vmName, args, -1, dispatchCenter);
            return nullptr;  // 已废弃，统一返回nullptr
            
        } else if (command == "TEXT_RECOGNIZE") {
            createTextRecognizeTask(vmName, args, -1, dispatchCenter);
            return nullptr;  // 已废弃，统一返回nullptr
            
        } else if (command == "TEXT_MATCH") {
            createTextMatchTask(vmName, args, -1, dispatchCenter);
            return nullptr;  // 已废弃，统一返回nullptr
            
        } else if (command == "VISUAL_MATCH") {
            createVisualMatchTask(vmName, args, -1, dispatchCenter);
            return nullptr;  // 已废弃，统一返回nullptr
            
        } else if (command == "WAIT_FOR_VISUAL_MATCH") {
            createWaitForVisualMatchTask(vmName, args, -1, dispatchCenter);
            return nullptr;  // 已废弃，统一返回nullptr
            
        } else if (command == "WAIT_FOR_SCREEN_STILL") {
            createWaitForScreenStillTask(vmName, args, -1, dispatchCenter);
            return nullptr;  // 已废弃，统一返回nullptr
            
        } else if (command == "VISION_MOUSE_DRAG") {
            createVisionMouseDragTask(vmName, args, -1, dispatchCenter);
            return nullptr;  // 已废弃，统一返回nullptr
            
        } else {
            AddLogInfo(LogLevel::Warning, "[ScriptLoader] " + vmName + ": 未知命令类型: " + command + " (第 " + std::to_string(parsedTask.lineNumber) + " 行)");
            return nullptr;
        }
        
    } catch (const std::exception& e) {
        AddLogInfo(LogLevel::Error, "[ScriptLoader] " + vmName + ": 创建任务失败: " + std::string(e.what()) + " (第 " + std::to_string(parsedTask.lineNumber) + " 行)");
        return nullptr;
    } catch (...) {
        AddLogInfo(LogLevel::Error, "[ScriptLoader] " + vmName + ": 创建任务发生未知异常 (第 " + std::to_string(parsedTask.lineNumber) + " 行)");
        return nullptr;
    }
}

