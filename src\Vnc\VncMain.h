#pragma once

#include <string>
#include <vector>
#include <mutex>

// VNC主入口函数
void vncMain();

// 断开全部虚拟机的连接
void disconnectAllVMs();

// 重置全局连接状态（用于停止后重新连接）
void resetGlobalConnectionState();

// 清理函数
void cleanupVnc();

// 启动视觉处理线程
bool turnOnVisionThread(const std::string& vmName);

// ImGui VM连接状态轮询与进度显示
void PollVMConnectionAndVisionThreads();
void ShowVMConnectionProgressUI();

// 合并的虚拟机状态轮询和显示函数
void PollAndShowVMStatus();

// 启动视觉处理线程
void startVisionProcessingThread();