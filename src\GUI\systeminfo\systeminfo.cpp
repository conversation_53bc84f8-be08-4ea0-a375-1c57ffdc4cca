
#include "systeminfo.h"
#include "imgui.h"
#include <windows.h>
#include <chrono> // 用于计时
#include <thread> // 用于线程休眠
#include <Pdh.h>
#include <PdhMsg.h>
#include <iostream>
#pragma comment(lib, "Pdh.lib")
#include <comdef.h>
#include <Wbemidl.h>
#pragma comment(lib, "wbemuuid.lib")
#include <iphlpapi.h>
#pragma comment(lib, "Iphlpapi.lib")

#define IM_CLAMP(V, MN, MX)     ((V) < (MN) ? (MN) : (V) > (MX) ? (MX) : (V))

//使用 Windows Performance Data Helper(PDH) 获取CPU总利用率
double GetCpuUsage() {
    static PDH_HQUERY query = nullptr;
    static PDH_HCOUNTER counter = nullptr;
    PDH_STATUS status;

    if (!query) {
        status = PdhOpenQuery(nullptr, 0, &query);
        if (status != ERROR_SUCCESS) {
            return 0.0;
        }

        PdhCollectQueryData(query); // 首次采集数据（初始化）
        status = PdhAddEnglishCounter(query, L"\\Processor(_Total)\\% Processor Time", 0, &counter);
        if (status != ERROR_SUCCESS) {
            return 0.0;
        }
    }

    status = PdhCollectQueryData(query);
    if (status != ERROR_SUCCESS) {
        return 0.0;
    }

    PDH_FMT_COUNTERVALUE value;
    status = PdhGetFormattedCounterValue(counter, PDH_FMT_DOUBLE, nullptr, &value);
    if (status != ERROR_SUCCESS) {
        return 0.0;
    }

    return value.doubleValue;
}

//内存利用率
float GetMemoryUsage() {
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);
    GlobalMemoryStatusEx(&memInfo);

    // 计算已用内存百分比
    float usedPercent = 1.0f - (float)memInfo.ullAvailPhys / (float)memInfo.ullTotalPhys;
    return usedPercent * 100.0f; // 返回百分比（0~100）
}


double GetGpuUsage() {
    PDH_HQUERY query;
    PDH_HCOUNTER counter;
    PDH_STATUS status;

    status = PdhOpenQuery(nullptr, 0, &query);
    if (status != ERROR_SUCCESS) return -1.0;

    // 查询 GPU 使用率（通用计数器）
    status = PdhAddEnglishCounter(query, L"\\GPU Engine(*)\\Utilization Percentage", 0, &counter);
    if (status != ERROR_SUCCESS) {
        PdhCloseQuery(query);
        return -1.0;
    }

    PdhCollectQueryData(query);

    PDH_FMT_COUNTERVALUE value;
    status = PdhGetFormattedCounterValue(counter, PDH_FMT_DOUBLE, nullptr, &value);
    if (status != ERROR_SUCCESS) {
        PdhCloseQuery(query);
        return -1.0;
    }

    PdhCloseQuery(query);
    return value.doubleValue;  // 返回 GPU 使用率（0~100%）
}



void ShowSystemInfo()
{
    // 静态变量用于存储上次采集数据的时间点
    static auto lastUpdateTime = std::chrono::steady_clock::now();
    static double cpuUsage = 0.0;
    static double RamUsage = 0.0;
    static double gpuUsage = 0.0;
    static double vramUsage = 55.0; // 示例显存使用率
    
    // 获取当前时间
    auto currentTime = std::chrono::steady_clock::now();
    auto elapsedTime = std::chrono::duration_cast<std::chrono::seconds>(currentTime - lastUpdateTime);

    // 如果时间差大于等于 10 秒，则更新数据
    if (elapsedTime.count() >= 10)
    {
        cpuUsage = GetCpuUsage();
        gpuUsage = GetGpuUsage();
        RamUsage = GetMemoryUsage();
        lastUpdateTime = currentTime;
    }

    // 设置表格布局
    ImGui::Columns(4, "SystemInfoColumns", false);
    ImGui::SetColumnWidth(0, 60.0f);
    
    // 第一列 - 标签
    ImGui::Text("CPU");
    ImGui::Text("内存");
    ImGui::NextColumn();
    
    // CPU使用率
    ImGui::TextColored(ImVec4(0.4f, 0.8f, 0.4f, 1.0f), "%.1f%%", cpuUsage);
    // 内存使用率
    ImGui::TextColored(ImVec4(0.4f, 0.8f, 0.8f, 1.0f), "%.1f%%", RamUsage);
    
    ImGui::SameLine(130);

    // 第三列 - 数值
    ImGui::NextColumn();
    ImGui::Text("GPU");
    ImGui::Text("显存");

    // 第四列 - 数值
    ImGui::NextColumn();
    // GPU使用率
    ImGui::TextColored(ImVec4(0.8f, 0.4f, 0.4f, 1.0f), "%.1f%%", gpuUsage);
    // 显存使用率
    ImGui::TextColored(ImVec4(0.8f, 0.8f, 0.4f, 1.0f), "%.1f%%", vramUsage);
    
    // 重置列布局
    ImGui::Columns(1);
}





