#include <imgui.h>
#include <vector>
#include <string>
#include <algorithm>
#include "tasklist.h" // 假设 tasklist.h 存在且包含任何必要的声明
#include <sstream>
#include <imgui_internal.h>
#include <filesystem> // C++17 标准库，用于文件系统操作
#include <fstream>    // 用于文件读写，尽管此处仅用于获取文件名，但保留以防后续需要

// 结构体，用于存储分类及其包含的任务项
struct Category {
    std::string name;             // 分类名称，对应子文件夹名称
    std::vector<std::string> items; // 任务项列表，对应子文件夹下的 .txt 文件名（不含扩展名）
};

std::vector<Category> categories; // 存储所有动态加载的任务分类
std::vector<std::string> selectedItems; // 存储用户已选择并显示在"执行栏"中的任务
int selectedCategory = 0;           // 当前选中的任务分类索引
int selectedItemIndex = -1;         // 当前选中的"执行栏"中的任务项索引

// 获取已选择的任务列表
const std::vector<std::string>& GetSelectedTasks() {
    return selectedItems;
}

// 获取脚本文件的完整路径
std::string GetScriptFilePath(const std::string& category, const std::string& scriptName) {
    // 构建脚本文件的完整路径
    std::filesystem::path scriptPath = std::filesystem::path("./script") / category / (scriptName + ".txt");
    return scriptPath.string();
}

// 添加一个全局标志，用于控制是否需要刷新数据
static bool needsRefresh = true;

// 从脚本文件夹加载任务分类和任务项的函数
void LoadCategoriesFromScriptFolder() {
    categories.clear(); // 清空现有的数据，确保每次加载都是最新的

    std::string scriptPath = "./script"; // 定义你的脚本文件夹路径，相对于可执行文件

    // 检查脚本文件夹是否存在且是一个目录
    if (!std::filesystem::exists(scriptPath) || !std::filesystem::is_directory(scriptPath)) {
        // 错误处理：脚本文件夹未找到或不是目录
        // 你可以在这里显示一个 ImGui 错误消息或在控制台打印日志
        return;
    }

    // 遍历脚本文件夹中的所有条目
    for (const auto& entry : std::filesystem::directory_iterator(scriptPath)) {
        // 如果当前条目是一个子目录（即一个任务分类）
        if (entry.is_directory()) {
            Category newCategory;
            newCategory.name = entry.path().filename().string(); // 获取子目录的名称作为分类名称

            // 遍历当前子目录中的所有文件
            for (const auto& itemEntry : std::filesystem::directory_iterator(entry.path())) {
                // 如果当前条目是一个常规文件且扩展名为 .txt
                if (itemEntry.is_regular_file() && itemEntry.path().extension() == ".txt") {
                    // 添加文件名（不包含扩展名）作为任务项
                    newCategory.items.push_back(itemEntry.path().stem().string());
                }
            }
            categories.push_back(newCategory); // 将新创建的分类添加到列表中
        }
    }

    // 可选：对分类和任务项进行字母排序，以保持显示顺序一致
    std::sort(categories.begin(), categories.end(), [](const Category& a, const Category& b) {
        return a.name < b.name; // 按分类名称排序
        });
    for (auto& category : categories) {
        std::sort(category.items.begin(), category.items.end()); // 对每个分类下的任务项进行排序
    }

    // 如果之前选中的分类索引超出了当前分类列表的大小，则重置为第一个分类
    if (selectedCategory >= categories.size()) {
        selectedCategory = 0;
    }
}

void ShowThreeListsDemo()
{
    // 在应用程序启动或 ImGui 窗口首次显示时，或者当 needsRefresh 为 true 时，加载任务数据。
    // needsRefresh 会在右键点击刷新时设置为 true。
    if (needsRefresh) {
        LoadCategoriesFromScriptFolder(); // 加载或刷新分类数据
        needsRefresh = false;             // 重置刷新标志
    }

    ImGui::BeginChild("列表联动"); // 创建一个子窗口用于布局整个界面
    ImVec2 avail_size = ImGui::GetContentRegionAvail(); // 获取当前可用区域大小（不包括标题栏、滚动条等）
    const int child_count = 5; // 窗口数量：分类、任务栏、执行栏、操作栏、保存任务
    float min_child_width = 100.0f; // 每个子窗口的最小宽度
    // 计算每个列的宽度，确保至少达到最小宽度
    float columnWidth = ImMax(min_child_width, avail_size.x / child_count);
    // 使用Push/Pop机制临时修改ItemSpacing，只影响当前作用域
    ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(0.0f, ImGui::GetStyle().ItemSpacing.y));

    // 第一列：任务分类
    ImGui::BeginChild("任务分类总1", ImVec2(columnWidth, 500), true); // 固定高度为500
    ImGui::Text("任务分类");                                      // 标题
    // 任务分类列表的内部子窗口，可滚动
    ImGui::BeginChild("任务分类列表", ImVec2(0, 0), true); // 为这个 ImGui::BeginChild 设置一个独特的 ID

    for (int i = 0; i < categories.size(); i++) {
        // 显示可选择的分类项，如果当前选中则高亮
        if (ImGui::Selectable(categories[i].name.c_str(), selectedCategory == i)) {
            selectedCategory = i; // 更新当前选中的分类索引
            selectedItemIndex = -1; // 切换分类时，重置"执行栏"的选中项索引
        }
    }
    ImGui::EndChild(); // 结束内部子窗口 ("任务分类列表")

    // 在"任务分类总1"这个 Begichild 的绘制区域内，检查是否发生了右键点击
    // 这样，即使点击了列表空白处，也能触发刷新。
    if (ImGui::IsItemHovered() && ImGui::IsMouseClicked(ImGuiMouseButton_Right)) {
        needsRefresh = true; // 设置刷新标志
    }
    ImGui::EndChild(); // 结束第一列子窗口 ("任务分类总1")
    ImGui::SameLine(); // 将下一个 BeginChild 放在同一行

    // 第二列：任务栏 (当前分类下的项)
    ImGui::BeginChild("任务栏2", ImVec2(columnWidth, 500), true); // 固定高度为500
    ImGui::Text("任务栏");                                    // 标题
    // 任务栏列表的内部子窗口，可滚动
    ImGui::BeginChild("任务栏列表", ImVec2(0, 0), true); // 为这个 ImGui::BeginChild 设置一个独特的 ID

    // 确保当前分类索引有效
    if (selectedCategory >= 0 && selectedCategory < categories.size()) {
        for (int i = 0; i < categories[selectedCategory].items.size(); i++) {
            const auto& item = categories[selectedCategory].items[i];
            ImGui::PushID(i); // 为每个 selectable 元素推入一个唯一的 ID，避免 ID 冲突
            // 显示任务项，允许双击操作
            if (ImGui::Selectable(item.c_str(), false, ImGuiSelectableFlags_AllowDoubleClick)) {
                // 如果是双击（鼠标左键）
                if (ImGui::IsMouseDoubleClicked(0)) {
                    selectedItems.push_back(item); // 双击添加到已选列表（执行栏）
                }
            }
            ImGui::PopID(); // 弹出 ID
        }
    }
    ImGui::EndChild(); // 结束内部子窗口 ("任务栏列表")

    // 在"任务栏2"这个 Begichild 的绘制区域内，检查是否发生了右键点击
    if (ImGui::IsItemHovered() && ImGui::IsMouseClicked(ImGuiMouseButton_Right)) {
        needsRefresh = true; // 设置刷新标志
    }
    ImGui::EndChild(); // 结束第二列子窗口 ("任务栏2")
    ImGui::SameLine(); // 将下一个 BeginChild 放在同一行

    // 第三列：已选择的项 (执行栏)
    ImGui::BeginChild("执行栏3", ImVec2(columnWidth, 500), true); // 固定高度为500
    ImGui::Text("执行栏");                                    // 标题
    ImGui::BeginChild("执行栏", ImVec2(0, 0), true);          // 内部子窗口，可滚动
    // 使用 while 循环来处理元素的删除，避免跳过元素
    for (int i = 0; i < selectedItems.size();) {
        const auto& item = selectedItems[i];
        ImGui::PushID(i); // 为每个 selectable 元素推入一个唯一的 ID
        // 显示已选任务项，如果当前选中则高亮，允许双击操作
        bool isSelected = ImGui::Selectable(item.c_str(), selectedItemIndex == i,
            ImGuiSelectableFlags_AllowDoubleClick);
        if (isSelected) {
            selectedItemIndex = i; // 更新选中索引
            // 双击删除处理
            if (ImGui::IsMouseDoubleClicked(0)) {
                selectedItems.erase(selectedItems.begin() + i); // 从列表中删除当前项
                if (selectedItemIndex == i)
                    selectedItemIndex = -1; // 如果删除的是当前选中项，则取消选中
                ImGui::PopID(); // 弹出 ID
                continue;       // 跳过 i++，因为已删除了当前项，下一个元素会移动到当前位置
            }
        }
        ImGui::PopID(); // 弹出 ID
        i++;            // 只有未删除时才递增索引
    }
    ImGui::EndChild(); // 结束内部子窗口
    ImGui::EndChild(); // 结束第三列子窗口
    ImGui::SameLine(); // 将下一个 BeginChild 放在同一行

    // 第四列：操作栏
    ImGui::BeginChild("操作栏4", ImVec2(columnWidth, 500), true); // 固定高度为500
    ImGui::Text("操作栏");                                    // 标题
    ImGui::BeginChild("操作栏", ImVec2(0, 0), true);          // 内部子窗口，可滚动
    // 操作按钮区域
    ImGui::Text("已选任务数: %d", (int)selectedItems.size()); // 显示已选任务的数量
    // 移动按钮（仅当有选中项时才启用）
    ImGui::BeginDisabled(selectedItemIndex < 0); // 如果没有选中项，禁用以下按钮
    {
        // 上移按钮
        if (ImGui::Button("上移") && selectedItemIndex > 0) {
            std::swap(selectedItems[selectedItemIndex], selectedItems[selectedItemIndex - 1]); // 交换位置
            selectedItemIndex--;                                                           // 更新选中索引
        }
        // 下移按钮
        if (ImGui::Button("下移") && selectedItemIndex >= 0 && selectedItemIndex < (int)selectedItems.size() - 1) {
            std::swap(selectedItems[selectedItemIndex], selectedItems[selectedItemIndex + 1]); // 交换位置
            selectedItemIndex++;                                                           // 更新选中索引
        }
        // 移至顶部按钮
        if (ImGui::Button("移至顶部") && selectedItemIndex > 0) {
            auto item = selectedItems[selectedItemIndex];                              // 暂存当前选中项
            selectedItems.erase(selectedItems.begin() + selectedItemIndex);            // 删除当前位置的项
            selectedItems.insert(selectedItems.begin(), item);                         // 插入到列表开头
            selectedItemIndex = 0;                                                     // 更新选中索引为 0
        }
        // 移至底部按钮
        if (ImGui::Button("移至底部") && selectedItemIndex < (int)selectedItems.size() - 1) {
            auto item = selectedItems[selectedItemIndex];                       // 暂存当前选中项
            selectedItems.erase(selectedItems.begin() + selectedItemIndex);     // 删除当前位置的项
            selectedItems.push_back(item);                                      // 添加到列表末尾
            selectedItemIndex = selectedItems.size() - 1;                       // 更新选中索引为最后一个
        }
        // 清空任务按钮
        if (ImGui::Button("清空任务")) {
            selectedItems.clear();       // 清空所有已选任务
            selectedItemIndex = -1;      // 取消选中
        }
    }
    ImGui::EndDisabled(); // 结束禁用区域
    ImGui::EndChild();    // 结束内部子窗口
    ImGui::EndChild();    // 结束第四列子窗口
    ImGui::SameLine();    // 将下一个 BeginChild 放在同一行

    // 第五列：保存任务 (占位符)
    ImGui::BeginChild("保存任务5", ImVec2(columnWidth, 500), true); // 固定高度为500
    ImGui::Text("保存任务");                                    // 标题
    ImGui::BeginChild("保存任务", ImVec2(0, 0), true);          // 内部子窗口，可滚动
    ImGui::Text("保存任务"); // 提示保存功能待实现
    ImGui::EndChild();                                        // 结束内部子窗口
    ImGui::EndChild();                                        // 结束第五列子窗口

    // 操作说明
    ImGui::TextColored(ImVec4(1, 1, 0, 1), "操作指南:"); // 黄色文字提示
    ImGui::BulletText("双击中间列表项: 添加到已选列表");
    ImGui::BulletText("点击右侧列表项: 选中该项");
    ImGui::BulletText("双击右侧列表项: 删除该项");
    ImGui::BulletText("使用下方按钮移动选中项的位置");
    ImGui::BulletText("在任务分类或任务栏中右键点击列表空白处或任意项，刷新列表");

    ImGui::PopStyleVar(); // 恢复ItemSpacing设置
    ImGui::EndChild(); // 结束整个"列表联动"子窗口
}