#pragma once
#include <string>
#include <unordered_map>
#include <mutex>
#include "LRUCache.h"  //集成缓存

class TemplatePathManager {
public:
    // 获取单例实例
    static TemplatePathManager& getInstance();

    // 加载json文件（只需调用一次，通常在程序启动时）
    bool loadFromJson(const std::string& jsonPath);

    // 通过模板名获取文件路径
    std::string getFilePath(const std::string& name);

    // 通过模板名获取cv::Mat（带LRU缓存，按需加载）
    cv::Mat getTemplate(const std::string& name);

    // 清空缓存（可选）
    void clearCache();
private:
    TemplatePathManager() = default;
    ~TemplatePathManager() = default;
    TemplatePathManager(const TemplatePathManager&) = delete;
    TemplatePathManager& operator=(const TemplatePathManager&) = delete;

    std::unordered_map<std::string, std::string> nameToFile_;
    std::mutex mutex_;
    LRUCache templateCache_{ 50 }; // 缓存50张常用模板
};
