#pragma once

#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <functional>
#include <atomic>
#include <GUI/consolelog/consolelog.h>
#include "UnifiedThreadManager.h"

// 任务管理器类，用于在后台线程中执行耗时操作
class TaskManager {
private:
    std::string workerThreadName_;                   // 工作线程名称（已迁移到统一线程管理器）
    std::queue<std::function<void()>> taskQueue;     // 任务队列
    std::mutex queueMutex;                           // 队列互斥锁
    std::condition_variable queueCV;                 // 队列条件变量
    std::atomic<bool> running;                       // 运行标志
    std::atomic<bool> initialized;                   // 初始化标志

    // 确保工作线程已初始化
    void ensureInitialized() {
        if (initialized.load()) {
            return;
        }
        
        std::lock_guard<std::mutex> lock(queueMutex);
        if (initialized.load()) {
            return; // 双重检查
        }
        
        try {
            // 使用统一线程管理器创建工作线程
            auto& threadManager = UnifiedThreadManager::getInstance();
            
            // 确保统一线程管理器已初始化
            if (!threadManager.initialize()) {
                AddLogInfo(LogLevel::Error, "[TaskManager] 统一线程管理器初始化失败");
                return;
            }
            
            workerThreadName_ = threadManager.createNamedThread("TaskManager", 
                [this]() { this->processTaskQueue(); });
            
            if (workerThreadName_.empty()) {
                AddLogInfo(LogLevel::Error, "[TaskManager] 工作线程创建失败");
                return;
            }
            
            initialized.store(true);
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "[TaskManager] 初始化异常: " + std::string(e.what()));
        } catch (...) {
            AddLogInfo(LogLevel::Error, "[TaskManager] 初始化时发生未知异常");
        }
    }

public:
    // 构造函数：延迟初始化工作线程
    TaskManager() : running(true), initialized(false) {
        // 不在构造函数中创建线程，避免静态初始化顺序问题
        AddLogInfo(LogLevel::Info, "[TaskManager] 构造函数完成，将延迟初始化工作线程");
    }

    // 析构函数：停止工作线程
    ~TaskManager() {
        shutdown();
    }

    // 关闭任务管理器
    void shutdown() {
        // 设置停止标志
        running.store(false);
        
        // 通知工作线程
        queueCV.notify_one();
        
        // 线程由统一线程管理器管理，无需手动join
    }

    // 添加任务到队列
    template<typename F, typename... Args>
    void addTask(F&& f, Args&&... args) {
        
        // 如果任务管理器已停止，则不添加任务
        if (!running.load()) {
            AddLogInfo(LogLevel::Warning, "[TaskManager] 任务管理器已停止，无法添加新任务");
            return;
        }

        // 确保工作线程已初始化
        ensureInitialized();
        
        if (!initialized.load()) {
            AddLogInfo(LogLevel::Error, "[TaskManager] 工作线程初始化失败，无法添加任务");
            return;
        }

        // 创建任务函数
        std::function<void()> task = std::bind(std::forward<F>(f), std::forward<Args>(args)...);
        
        // 添加到队列
        {
            std::lock_guard<std::mutex> lock(queueMutex);
            taskQueue.push(task);
        }
        
        // 通知工作线程
        queueCV.notify_one();
    }

private:
    // 工作线程函数：处理任务队列
    void processTaskQueue() {
        while (running.load()) {
            std::function<void()> task;
            
            // 等待任务或关闭信号
            {
                std::unique_lock<std::mutex> lock(queueMutex);
                queueCV.wait(lock, [this] { 
                    return !taskQueue.empty() || !running.load(); 
                });
                
                // 如果收到关闭信号且队列为空，退出循环
                if (!running.load() && taskQueue.empty()) {
                    AddLogInfo(LogLevel::Info, "[TaskManager] 收到关闭信号且队列为空，工作线程准备退出");
                    break;
                }
                
                // 如果队列不为空，取出一个任务
                if (!taskQueue.empty()) {
                    task = taskQueue.front();
                    taskQueue.pop();
                }
            }
            
            // 执行任务
            if (task) {
                try {
                    task();
                } catch (const std::exception& e) {
                    AddLogInfo(LogLevel::Error, std::string("[TaskManager] 任务执行异常: ") + e.what());
                } catch (...) {
                    AddLogInfo(LogLevel::Error, "[TaskManager] 任务执行时发生未知异常");
                }
            } else {
                AddLogInfo(LogLevel::Warning, "[TaskManager] 工作线程被唤醒但没有任务");
            }
        }
        
        AddLogInfo(LogLevel::Info, "[TaskManager] 工作线程已停止");
    }
};

// 全局任务管理器实例
extern TaskManager g_taskManager;
