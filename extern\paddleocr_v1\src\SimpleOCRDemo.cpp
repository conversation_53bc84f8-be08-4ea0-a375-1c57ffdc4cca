#include "SimpleOCR.h"
#include <iostream>
#include <string>
#include <opencv2/opencv.hpp>

// Simple command-line demo program for SimpleOCR
int main(int argc, char* argv[]) {
    try {
        // Parse command line arguments
        if (argc < 2) {
            // 使用方法：程序名 <图像路径> [要查找的文本]
            // <图像路径>：图像文件的路径
            // [要查找的文本]：可选的要在图像中查找的文本
            return 1;
        }

        std::string image_path = argv[1];
        std::string text_to_find = (argc > 2) ? argv[2] : "";

        // Load the image
        cv::Mat image = cv::imread(image_path);
        if (image.empty()) {
            // 无法打开或找到图像文件
            return 1;
        }

        // Initialize SimpleOCR with PP-OCRv4 models
        PaddleOCR::SimpleOCR ocr(
            "models/ch_PP-OCRv4_det_infer",
            "models/ch_PP-OCRv4_rec_infer",
            "models/ppocr_keys_v1.txt",
            false,  // use_gpu
            0       // gpu_id
        );

        // If text to find is provided, find it in the image
        if (!text_to_find.empty()) {
            // 在图像中查找文本
            
            auto [center, confidence] = ocr.findText(image, text_to_find);
            
            if (confidence > 0) {
                // 找到文本
                
                // Draw the result on the image
                cv::circle(image, center, 10, cv::Scalar(0, 0, 255), -1);
                cv::putText(image, text_to_find, cv::Point(center.x + 15, center.y), 
                            cv::FONT_HERSHEY_SIMPLEX, 0.8, cv::Scalar(0, 0, 255), 2);
                
                // Save and show the result
                cv::imwrite("result_find.jpg", image);
                cv::imshow("Found Text", image);
                cv::waitKey(0);
            } else {
                // 在图像中未找到文本
            }
        } 
        // Otherwise, recognize all text in the image
        else {

            std::vector<PaddleOCR::OCRPredictResult> results = ocr.recognize(image);

            // Draw results on the image
            cv::Mat result_image = image.clone();
            
            for (const auto& result : results) {
                // Draw bounding box
                std::vector<cv::Point> points;
                for (const auto& point : result.box) {
                    if (point.size() >= 2) {
                        points.push_back(cv::Point(point[0], point[1]));
                    }
                }
                
                if (points.size() >= 4) {
                    cv::polylines(result_image, points, true, cv::Scalar(0, 255, 0), 2);
                }
                
                // Draw text
                cv::Point center = PaddleOCR::GetCenterPoint(result);
                cv::putText(result_image, result.text, 
                            cv::Point(center.x, center.y - 10), 
                            cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 255), 1);
                
                // 处理识别结果
            }
            
            // Save and show the result
            cv::imwrite("result_recognize.jpg", result_image);
            cv::imshow("Recognized Text", result_image);
            cv::waitKey(0);
        }
        
    } catch (const std::exception& e) {
        // 程序执行错误
        return 1;
    }
    
    return 0;
}
