#include "mainui.h"
#include "imgui.h"
#include "imgui_impl_dx11.h"
#include "imgui_impl_win32.h"
#include <d3d11.h>
#include <thread>
#include <fstream>
#include <string>
#include <filesystem>
#include <GUI/consolelog/consolelog.h>
#include <tchar.h>
#include <iostream>
#include <GUI/tasklist/tasklist.h>
#include <vector>
#include <functional>
#include "systeminfo/systeminfo.h"
#include "centerpanel/centerconsole.h"
#include <Vnc/VncMain.h>
#include <Core/TaskManager.h>
#include <Vnc/DispatchCenter.h>
#include <Vnc/VMStateManager.h>  // 添加VM状态管理器
#include <Vnc/VMVision.h>  // 添加VMVision头文件
#include <Vnc/ScreenFrameDistributor.h>  // 添加切片分发器头文件
#include "scripteditor/scripteditor.h"
#include <Logic/common.h>
#include <map> // 用于切片配置UI的静态变量
#include <Core/VersionChecker.h>
#include "visionconfig/visionconfig.h"  // 添加匹配设置头文件
#include "../Core/StringObfuscation.h"
#include "../Core/ConfigManager.h"
#include "../Vnc/VMStateManager.h"


// 全局或类成员变量
static int selected_menu_index = 0;
// Updated menu_items to include "脚本编辑器" and "匹配设置"
const char* menu_items[] = { "主页看板", "任务设置", "匹配设置", "脚本编辑器", "关于游戏" };
// 声明全局变量
extern std::string g_hardware_OS;
extern std::string g_hardware_CPU;
extern std::string g_hardware_MOTHER;
extern std::string g_hardware_GPU;
extern std::string g_hardware_MEM;


// Static flags for Script Editor window
static bool show_script_editor_window = true;
static bool script_editor_scripts_loaded_first_time = false;
// 用于存储当前选中项的索引，下拉选择器
static int current_selected_item = 0;

// 添加连接状态跟踪变量
static bool is_connecting = false;

// 这些变量已经不再需要，因为轮询已由PollVMConnectionAndVisionThreads()处理
// static bool need_check_vm_status = false;
// static double last_check_time = 0.0;

// 重置UI状态的函数
void resetUIState() {
    current_selected_item = 0;
}

struct ButtonState {
    bool enableConnect;
    bool enableDisconnect;
    bool enableStartTask;
    bool enablePause;
    bool enableResume;
    bool enableStopTask;
};

static ButtonState getButtonState(VMUIState state) {
    switch(state) {
        case VMUIState::DISCONNECTED:
            // 启动APP/连接失败阶段
            return {true, false, false, false, false, false};
        case VMUIState::CONNECTING:
            // 点击开始连接瞬间 - 所有按钮都禁用
            return {false, false, false, false, false, false};
        case VMUIState::CONNECTED_IDLE:
            // 连接成功/连接停止任务/所有任务执行完成阶段
            return {false, true, true, false, false, false};
        case VMUIState::TASK_RUNNING:
            // 开始任务执行期间
            return {false, true, false, true, false, true};
        case VMUIState::TASK_PAUSED:
            // 点击暂停当前
            return {false, true, false, false, true, true};
        case VMUIState::TASK_STOPPED:
            // 连接停止任务
            return {false, true, true, false, false, false};
        default:
            return {true, false, false, false, false, false};
    }
}

void RenderUI()
{
    // 设置主窗口覆盖整个区域 (可选, 或者直接绘制)
    ImGuiViewport* viewport = ImGui::GetMainViewport();
    ImGui::SetNextWindowPos(viewport->Pos);
    ImGui::SetNextWindowSize(viewport->Size);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 0.1f); //窗口圆角半径
    ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 0.0f);//窗口边框大小
    // 禁用装饰，包括禁用标题栏、禁用用户通过双击窗口来折叠窗口、右下角调整窗口大小、禁用用户移动窗口、禁用获取焦点时将窗口置于前面、禁用游戏手柄/键盘导航聚焦到此窗口
    ImGuiWindowFlags main_flags = ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoBringToFrontOnFocus | ImGuiWindowFlags_NoNavFocus;
    // 推入主窗口背景颜色，例如淡蓝色(#EDF3F9), 可根据需求修改颜色值
    //ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0.929f, 0.953f, 0.976f, 1.0f));
    ImGui::Begin("MainBackground", nullptr, main_flags);
    ImGui::PopStyleVar(2);
    // --- 应用深色主题 ---
    ImGuiStyle& style = ImGui::GetStyle();
    // style.Colors[ImGuiCol_WindowBg] = ...; // 设置颜色
    // style.Colors[ImGuiCol_ChildBg] = ...;
    // ... 其他样式设置 ...

    // --- 左侧菜单栏 ---
    float menu_width = 150.0f; // 菜单宽度
    // 在绘制子窗口前推入自定义边框样式
    ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 0.0f);           // 边框厚度设置为 1.0f
    /*ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(5.0f, 10.0f));  */         //  内边距
    ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 1.0f);           // 圆角半径
    ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(1.0f, 1.0f, 1.0f, 1.0f));             //边框颜色
    ImGui::PushStyleColor(ImGuiCol_BorderShadow, ImVec4(1.0f, 1.0f, 1.0f, 1.0f));   //边框阴影
    ImGui::BeginChild("LeftMenu", ImVec2(menu_width, 0), true); // 高度为0表示填充父窗口

    ImGui::TextDisabled("logo...");
    for (int i = 0; i < IM_ARRAYSIZE(menu_items); ++i) {
        // 结合图标和文字 (需要加载图标字体)
        // std::string label = std::string(menu_icons[i]) + " " + menu_items[i];
        std::string label = menu_items[i]; // 简化版
        bool is_selected = (selected_menu_index == i);
        //#E2E7EA
        if (is_selected) {
            ImGui::SetWindowFontScale(1.1f); // 放大字体
        }
        // 设置文本居中对齐
        ImGui::PushStyleVar(ImGuiStyleVar_SelectableTextAlign, ImVec2(0.5f, 0.5f)); // 水平和垂直居中

        if (ImGui::Selectable(label.c_str(), is_selected, ImGuiSelectableFlags_None, ImVec2(0, 50))) {
            selected_menu_index = i;
        }
        if (is_selected) {
            ImGui::SetItemDefaultFocus(); // 保持选中项焦点
            ImGui::SetWindowFontScale(1.0f); // 恢复默认字体大小
        }
        // (可选) 在 Selectable 下方添加描述文字
        //ImGui::TextDisabled("描述文字...");
        ImGui::PopStyleVar();
        ImGui::Spacing();
    }
    // (可选) 底部用户信息
    ImGui::SetCursorPosY(ImGui::GetWindowHeight() - 60); // 定位到底部
    ImGui::Separator();
    std::string version = VersionChecker::getVersion();
    ImGui::Text("版本：%s", version.c_str());
    ImGui::Text("Profane");

    // 弹出样式设置
    ImGui::EndChild(); // LeftMenu
    ImGui::PopStyleColor(2); // 弹出 Border 颜色设置
    ImGui::PopStyleVar(2);  // 弹出 WindowBorderSize 设置


    // --- 右侧内容区 ---
    ImGui::SameLine();
    ImGui::BeginChild("RightArea", ImVec2(0, 0)); // 填充剩余空间

    // 右侧顶部栏 (简化示例)
    ImGui::BeginChild("TopBar", ImVec2(0, 50), false);

    ImGui::Text("系统:%s  CPU:%s", g_hardware_OS.c_str(), g_hardware_CPU.c_str());
    ImGui::Text("主板:%s  显卡:%s", g_hardware_MOTHER.c_str(), g_hardware_GPU.c_str());

    ImGui::SameLine(ImGui::GetWindowWidth() - 150); // 右对齐控件
    ImGui::Button("?"); ImGui::SameLine();
    ImGui::Button("搜索");
    ImGui::EndChild(); // TopBar
    ImGui::Separator();

    ImGui::BeginChild("bottomArea", ImVec2(0, 0));
    // 根据选择显示内容
    ImGui::BeginChild("MainContent", ImVec2(ImGui::GetWindowWidth() - 300, 0));
    switch (selected_menu_index) {
    case 0:
        MainPanel();
        break;
    case 1:
        ShowThreeListsDemo();
        break;
    case 2: // Index for "匹配设置"
        ShowVisionConfigEditor();
        break;
    case 3: // Index for "脚本编辑器" (adjusted index)
        if (!script_editor_scripts_loaded_first_time) {
            MyScriptEditor::LoadScripts();
            script_editor_scripts_loaded_first_time = true;
        }
        MyScriptEditor::ShowScriptEditorWindow(&show_script_editor_window);
        break;
        // ... 其他 case ...
    default:
        // 获取可用区域尺寸
        ImVec2 content_avail = ImGui::GetContentRegionAvail();
        // 计算文本尺寸
        ImVec2 text_size = ImGui::CalcTextSize("功能");
        //设置文本的xy坐标
        ImGui::SetCursorPosX((content_avail.x - text_size.x) * 0.5f);
        ImGui::SetCursorPosY((content_avail.y - text_size.y) * 0.5f);
        ImGui::Text("功能"); // 默认内容
        break;
    }
    ImGui::EndChild(); // MainContent

    ImGui::SameLine();
    ImGui::PushStyleColor(ImGuiCol_BorderShadow, ImVec4(1.0f, 1.0f, 1.0f, 1.0f));   //边框阴影
    ImGui::BeginChild("rightContent", ImVec2(0, 0));// right


    //右边组件
    ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 1.0f);           // 圆角半径
    ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(1.0f, 1.0f, 1.0f, 1.0f));             //边框颜色
    ImGui::BeginChild("rightfirst", ImVec2(300.0f, 60.0f), true);// rightfirst
    ShowSystemInfo(); //调用系统信息框
    ImGui::EndChild(); // rightfirst
    ImGui::PopStyleColor(1);
    ImGui::PopStyleVar();

    ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 1.0f);           // 圆角半径
    ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(1.0f, 1.0f, 1.0f, 1.0f));             //边框颜色
    ImGui::BeginChild("rightsecond", ImVec2(300.0f, 400.0f));// rightthird

    // 在元素前添加垂直间距
    ImGui::Dummy(ImVec2(0, 10));
    // 轮询虚拟机连接状态并显示简化状态信息
    PollAndShowVMStatus();
    
    // 显示虚拟机选择器
    // 创建局部副本以避免长时间持有锁
    bool local_vms_connected = false;
    std::vector<std::string> local_available_vms;
    
    // 从 VMStateManager 获取状态，不再需要 mutex 锁
    local_vms_connected = VMStateManager::getInstance()->isConnected();
    local_available_vms = VMStateManager::getInstance()->getAvailableVMs();
    
    // 状态变化检测和合理的日志输出
    static bool last_vms_connected = false;
    static size_t last_vm_count = 0;
    static double last_log_time = 0.0;
    double current_time = ImGui::GetTime();
    
    // 只在状态变化时或每10秒输出一次日志（避免高频输出）
    bool state_changed = (last_vms_connected != local_vms_connected) || 
                        (last_vm_count != local_available_vms.size());
    bool time_for_periodic_log = (current_time - last_log_time) > 10.0; // 10秒间隔
    
    if (state_changed || (time_for_periodic_log && local_vms_connected)) {
        // 删除冗余的虚拟机状态变化日志
        last_vms_connected = local_vms_connected;
        last_vm_count = local_available_vms.size();
        last_log_time = current_time;
    }
    
    // 声明变量在外层作用域，以便后面的按钮逻辑使用
    std::string selectedVmName;
    bool vmConnected = false;
    
    if (local_vms_connected && !local_available_vms.empty()) {

        // 创建一个字符串指针数组，用于 ImGui::Combo
        std::vector<const char*> vm_items;
        for (const auto& vm : local_available_vms) {
            vm_items.push_back(vm.c_str());
        }
        // 确保 current_selected_item 在有效范围内
        if (current_selected_item >= vm_items.size()) {
            current_selected_item = 0;
        }
        // 显示下拉选择器
        int prev_selected_item = current_selected_item;
        if (ImGui::Combo("虚拟机", &current_selected_item, vm_items.data(), static_cast<int>(vm_items.size()))) {
            // 当选择变化时，更新虚拟机名称
            if (current_selected_item != prev_selected_item) {
                extern std::string DefaultVmName;
                std::string currentVmName = local_available_vms[current_selected_item];
                DefaultVmName = currentVmName;
                SetThreadLocalVmName(currentVmName);
                AddLogInfo(LogLevel::Info, "当前选中虚拟机: " + currentVmName);
                prev_selected_item = current_selected_item;
            }
        }
        
        // 设置选中的虚拟机名称
        if (current_selected_item < local_available_vms.size()) {
            selectedVmName = local_available_vms[current_selected_item];
            vmConnected = true;
        } else if (!local_available_vms.empty()) {
            selectedVmName = local_available_vms[0]; // 使用第一个虚拟机作为默认
            vmConnected = true;
        } else {
            selectedVmName = "未连接"; // 显示占位符
            vmConnected = false;
        }
        ShowSliceConfigurationUI(selectedVmName, vmConnected);
    } else {
        // 如果虚拟机未连接，显示禁用的下拉选择器
        ImGui::BeginDisabled();
        const char* no_vms[] = { "未连接虚拟机" };
        ImGui::Combo("虚拟机", &current_selected_item, no_vms, 1);
        ImGui::EndDisabled();
        
        // 设置未连接状态
        selectedVmName = "未连接";
        vmConnected = false;
        
        // 即使未连接也显示切片配置UI，但处于禁用状态
        ShowSliceConfigurationUI(selectedVmName, vmConnected);
    }

    // 根据连接状态决定是否禁用开始连接按钮
    // 修复：使用已连接的VM列表中的实际VM名称，而不是下拉框选择
    std::string actualVmName = selectedVmName;
    VMUIState uiState = VMUIState::DISCONNECTED; // 默认为断开状态
    
    // 检查当前是否有已连接的VM，如果有则使用第一个已连接的VM状态
    try {
        std::vector<std::string> connectedVMs = DispatchCenter::getInstance()->getConnectedVMNames();
        if (!connectedVMs.empty()) {
            // 有已连接的VM，使用第一个VM的状态
            actualVmName = connectedVMs[0];
            uiState = VMStateManager::getInstance()->getUIState(actualVmName);
        } else {
            // 没有已连接的VM，检查是否正在连接
            if (is_connecting) {
                uiState = VMUIState::CONNECTING;
            } else {
                uiState = VMUIState::DISCONNECTED;
            }
            // 确保任何之前的VM状态都被重置为断开状态
            if (!selectedVmName.empty() && selectedVmName != "未连接") {
                VMStateManager::getInstance()->setUIState(selectedVmName, VMUIState::DISCONNECTED);
            }
        }
    } catch (...) {
        // 获取连接VM列表失败，使用断开连接状态
        if (is_connecting) {
            uiState = VMUIState::CONNECTING;
        } else {
            uiState = VMUIState::DISCONNECTED;
        }
    }
    
    ButtonState btnState = getButtonState(uiState);

    // ==================== 按钮布局：3行2列 ====================
    // 第1行：连接控制按钮
    ImGui::BeginDisabled(!btnState.enableConnect);
    // --- 开始连接按钮绿色美化 ---
    ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.0f, 0.7f, 0.0f, 1.0f));        // 绿色
    ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.0f, 0.85f, 0.0f, 1.0f)); // 悬停更亮
    ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.0f, 0.6f, 0.0f, 1.0f));   // 按下更暗
    if (ImGui::Button("开始连接", ImVec2(100, 35))) {
        // 立即禁用按钮，防止二次点击
        is_connecting = true;
        // 立即设置UI状态为连接中，让UI立即响应
        VMStateManager::getInstance()->setUIState(selectedVmName, VMUIState::CONNECTING);
        g_taskManager.addTask([=]() {
            try {
                vncMain();
                is_connecting = false;
                // 修复：连接成功后立即更新状态，无需延迟
                // 获取最新的已连接VM列表并更新VMStateManager
                try {
                    std::vector<std::string> connectedVMs = DispatchCenter::getInstance()->getConnectedVMNames();
                    VMStateManager::getInstance()->setAvailableVMs(connectedVMs);
                    if (!connectedVMs.empty()) {
                        // 为所有已连接的VM设置正确的状态
                        for (const auto& vmName : connectedVMs) {
                            VMStateManager::getInstance()->setUIState(vmName, VMUIState::CONNECTED_IDLE);
                        }
                        AddLogInfo(LogLevel::Info, "[主程序] 连接成功，已更新 " + std::to_string(connectedVMs.size()) + " 个VM的状态");
                    }
                } catch (...) {
                    AddLogInfo(LogLevel::Warning, "[主程序] 更新连接状态时发生异常");
                }
            } catch (...) {
                is_connecting = false;
                VMStateManager::getInstance()->setUIState(selectedVmName, VMUIState::DISCONNECTED);
                VMStateManager::getInstance()->setAvailableVMs({}); // 连接失败时清空已连接VM列表
            }
        });
    }
    ImGui::PopStyleColor(3);
    ImGui::EndDisabled();

    ImGui::SameLine();
    // 设置断开连接按钮为红色背景（已美化）
    ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.8f, 0.0f, 0.0f, 1.0f));        // 红色背景
    ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.9f, 0.0f, 0.0f, 1.0f)); // 悬停时更亮的红色
    ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.7f, 0.0f, 0.0f, 1.0f));  // 按下时暗一点的红色
    ImGui::BeginDisabled(!btnState.enableDisconnect);
    if (ImGui::Button("断开连接", ImVec2(100, 35))) {
        // 安全断开连接：分步骤执行，每步都有异常保护
        // 删除：开始安全断开连接流程日志
        
        // 第1步：设置本地状态标志，防止新操作
        try {
            is_connecting = false;
            // 删除冗余的步骤1本地状态重置日志
        } catch (...) {
            AddLogInfo(LogLevel::Warning, "[主程序] 步骤1：本地状态重置时发生异常，继续执行");
        }
        
        // 第2步：获取当前连接的VM列表（在停止前备份）
        std::vector<std::string> connectedVMs;
        try {
            auto* dispatchCenter = DispatchCenter::getInstance();
            if (dispatchCenter) {
                connectedVMs = dispatchCenter->getConnectedVMNames();
                // 删除冗余的步骤2获取VM列表日志
            }
        } catch (...) {
            AddLogInfo(LogLevel::Warning, "[主程序] 步骤2：获取VM列表时发生异常，继续执行");
        }
        
        // 第3步：在工作线程中执行断开操作，避免阻塞UI线程
        g_taskManager.addTask([connectedVMs]() {
            try {
                // 删除冗余的工作线程开始断开连接日志
                
                auto* dispatchCenter = DispatchCenter::getInstance();
                if (!dispatchCenter) {
                    AddLogInfo(LogLevel::Error, "[断开连接] DispatchCenter实例不可用");
                    return;
                }
                
                // 第3.1步：停止所有任务轮询器
                for (const auto& vmName : connectedVMs) {
                    try {
                        // 删除冗余的停止VM任务轮询器日志
                        dispatchCenter->stopTaskPoller(vmName);
                        
                        // 清空任务队列
                        dispatchCenter->clearAllTasksAndResetSlices(vmName);
                        dispatchCenter->clearSliceWaitingStates(vmName);
                        
                    } catch (...) {
                        AddLogInfo(LogLevel::Warning, "[断开连接] 停止VM [" + vmName + "] 时发生异常，继续处理其他VM");
                    }
                }
                
                // 等待任务停止完成
                std::this_thread::sleep_for(std::chrono::milliseconds(300));
                
                // 第3.2步：安全停止所有VM控制器
                try {
                    // 删除冗余的开始安全停止VM控制器日志
                    
                    // 使用更简单的停止方式，避免复杂的线程操作
                    std::vector<std::string> vmNames;
                    {
                        // 快速获取VM名称列表
                        for (const auto& vmName : connectedVMs) {
                            vmNames.push_back(vmName);
                        }
                    }
                    
                    // 逐个安全停止VM
                    for (const auto& vmName : vmNames) {
                        try {
                            if (dispatchCenter->removeVMController(vmName)) {
                                // 删除冗余的VM已安全停止日志
                            }
                        } catch (...) {
                            AddLogInfo(LogLevel::Warning, "[断开连接] 停止VM [" + vmName + "] 控制器时发生异常");
                        }
                    }
                    
                } catch (...) {
                    AddLogInfo(LogLevel::Warning, "[断开连接] 停止VM控制器时发生异常");
                }
                
                // 第3.3步：等待线程自然退出
                // 删除冗余的等待线程自然退出日志
                std::this_thread::sleep_for(std::chrono::milliseconds(500));
                
                // 第3.4步：后台更新状态（工作线程中的最后清理）
                try {
                    // 清空VMStateManager中的连接状态，完全恢复到初始状态
                    VMStateManager::getInstance()->setAvailableVMs({});
                    VMStateManager::getInstance()->setVMsConnected(false);
                    VMStateManager::getInstance()->setConnected(false);
                    VMStateManager::getInstance()->setConnecting(false);
                    
                    // 重置所有VM的UI状态为断开连接
                    for (const auto& vmName : connectedVMs) {
                        VMStateManager::getInstance()->setUIState(vmName, VMUIState::DISCONNECTED);
                    }
                    
                    // 删除冗余的所有状态重置日志
                } catch (...) {
                    AddLogInfo(LogLevel::Warning, "[断开连接] 更新UI状态时发生异常");
                }
                
                // 删除冗余的断开连接流程完成日志
                
            } catch (...) {
                AddLogInfo(LogLevel::Error, "[断开连接] 工作线程中发生未知异常");
            }
        });
        
        // 第4步：立即更新UI状态，让用户看到响应
        try {
            // 立即重置所有全局状态到初始状态
            VMStateManager::getInstance()->setAvailableVMs({});
            VMStateManager::getInstance()->setVMsConnected(false);
            VMStateManager::getInstance()->setConnected(false);
            VMStateManager::getInstance()->setConnecting(false);
            
            // 重置所有相关VM的UI状态为断开连接
            for (const auto& vmName : connectedVMs) {
                VMStateManager::getInstance()->setUIState(vmName, VMUIState::DISCONNECTED);
            }
            
            // 删除冗余的步骤4UI状态重置日志
        } catch (...) {
            AddLogInfo(LogLevel::Warning, "[主程序] 步骤4：更新UI状态时发生异常");
        }
        
        // 删除冗余的断开连接请求提交日志
    }
    ImGui::EndDisabled();
    ImGui::PopStyleColor(3); // 恢复断开连接按钮颜色设置

    // 第2行：任务控制按钮
    ImGui::BeginDisabled(!btnState.enableStartTask);
    // --- 开始任务按钮蓝色美化 ---
    ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.0f, 0.4f, 0.85f, 1.0f));        // 蓝色
    ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.0f, 0.55f, 1.0f, 1.0f)); // 悬停更亮
    ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.0f, 0.3f, 0.7f, 1.0f));   // 按下更暗
    if (ImGui::Button("开始任务", ImVec2(100, 35))) {
        // 首先检查vision config是否可以用于任务执行
        std::string visionConfigError;
        bool canStartTask = VisionConfigEditor::IsConfigReadyForTask(&visionConfigError);
        
        if (!canStartTask) {
            AddLogInfo(LogLevel::Warning, visionConfigError);
        } else {
            // 然后检查执行栏中是否添加了脚本文件路径
            const std::vector<std::string>& selectedTasks = GetSelectedTasks();
            if (selectedTasks.empty()) {
                AddLogInfo(LogLevel::Warning, "请先到任务设置中，执行栏添加任务！");
            } else {
            // 直接使用 DispatchCenter 检查虚拟机连接状态
            std::vector<std::string> connectedVMs;
            bool connectionCheckSuccess = false;
            try {
                connectedVMs = DispatchCenter::getInstance()->getConnectedVMNames();
                connectionCheckSuccess = true;
            } catch (const std::exception& e) {
                AddLogInfo(LogLevel::Error, "获取已连接虚拟机列表时发生异常: " + std::string(e.what()));
            } catch (...) {
                AddLogInfo(LogLevel::Error, "获取已连接虚拟机列表时发生未知异常");
            }
            
            if (!connectionCheckSuccess) {
                // 连接检查失败，不执行后续操作
            } else if (connectedVMs.empty()) {
                AddLogInfo(LogLevel::Warning, "没有虚拟机连接，请先点击\"开始连接\"按钮");
            } else {
                // 获取当前选中的虚拟机名称
                std::string selectedVmName;
                
                // 如果当前选择索引有效，则使用该索引对应的虚拟机
                // 否则使用第一个连接的虚拟机
                if (current_selected_item < connectedVMs.size()) {
                    selectedVmName = connectedVMs[current_selected_item];
                } else if (!connectedVMs.empty()) {
                    selectedVmName = connectedVMs[0];
                    AddLogInfo(LogLevel::Warning, "当前选择的虚拟机无效，将使用第一个可用的虚拟机: " + selectedVmName);
                }
                
                if (!selectedVmName.empty()) {
                    // 在执行任务前，确保选择的虚拟机确实连接且视觉处理器可用
                    bool canExecuteTask = true;
                    try {
                        auto* dispatchCenter = DispatchCenter::getInstance();
                        if (!dispatchCenter) {
                            AddLogInfo(LogLevel::Error, "DispatchCenter不可用，无法执行任务");
                            canExecuteTask = false;
                        } else {
                            // 检查虚拟机控制器是否存在
                            VMControllerInfo* controllerInfo = dispatchCenter->getVMControllerInfo(selectedVmName);
                            if (!controllerInfo) {
                                AddLogInfo(LogLevel::Error, "虚拟机 [" + selectedVmName + "] 控制器不存在，无法执行任务");
                                canExecuteTask = false;
                            } else if (!dispatchCenter->isVMConnected(selectedVmName)) {
                                AddLogInfo(LogLevel::Error, "虚拟机 [" + selectedVmName + "] 未连接，无法执行任务");
                                canExecuteTask = false;
                            } else {
                                // 尝试获取或创建视觉处理器
                                VMVision* visionProcessor = dispatchCenter->getVisionProcessor(selectedVmName, std::chrono::milliseconds(3000));
                                if (!visionProcessor) {
                                    AddLogInfo(LogLevel::Warning, "虚拟机 [" + selectedVmName + "] 视觉处理器不可用，尝试启动视觉线程");
                                    if (!turnOnVisionThread(selectedVmName)) {
                                        AddLogInfo(LogLevel::Error, "虚拟机 [" + selectedVmName + "] 视觉线程启动失败，任务可能无法正常执行");
                                    } else {
                                        AddLogInfo(LogLevel::Info, "虚拟机 [" + selectedVmName + "] 视觉线程已重新启动");
                                    }
                                }
                            }
                        }
                    } catch (const std::exception& e) {
                        AddLogInfo(LogLevel::Error, "检查虚拟机 [" + selectedVmName + "] 状态时发生异常: " + std::string(e.what()));
                        canExecuteTask = false;
                    } catch (...) {
                        AddLogInfo(LogLevel::Error, "检查虚拟机 [" + selectedVmName + "] 状态时发生未知异常");
                        canExecuteTask = false;
                    }
                    
                    if (canExecuteTask) {
                        // 在工作线程中执行任务，避免阻塞UI
                        // 删除冗余的在虚拟机上执行任务日志
                        
                        // 创建一个安全的副本，传递给工作线程
                        std::string vmName = selectedVmName;
                        
                        // 使用 g_taskManager 来管理任务执行，避免直接创建线程
                        g_taskManager.addTask([vmName]() {
                            try {
                                // 在工作线程开始时设置线程本地存储的虚拟机名称
                                SetThreadLocalVmName(vmName);
                                
                                // 在工作线程中执行任务，使用指定的虚拟机名称
                                ExecuteSelectedTasks(vmName);
                                
                                // 任务执行完成后，更新UI状态为TASK_STOPPED
                                VMStateManager::getInstance()->setUIState(vmName, VMUIState::TASK_STOPPED);
                                AddLogInfo(LogLevel::Info, "虚拟机 [" + vmName + "] 所有任务执行完成");
                            } catch (const std::exception& e) {
                                AddLogInfo(LogLevel::Error, "虚拟机 [" + vmName + "] 执行任务时发生异常: " + std::string(e.what()));
                                // 任务执行异常时也更新为TASK_STOPPED状态
                                VMStateManager::getInstance()->setUIState(vmName, VMUIState::TASK_STOPPED);
                            } catch (...) {
                                AddLogInfo(LogLevel::Error, "虚拟机 [" + vmName + "] 执行任务时发生未知异常");
                                // 任务执行异常时也更新为TASK_STOPPED状态
                                VMStateManager::getInstance()->setUIState(vmName, VMUIState::TASK_STOPPED);
                            }
                        });
                        VMStateManager::getInstance()->setUIState(selectedVmName, VMUIState::TASK_RUNNING);
                    }
                } else {
                    AddLogInfo(LogLevel::Warning, "没有可用的虚拟机");
                }
            }
            }
        }
    }
    ImGui::PopStyleColor(3);
    ImGui::EndDisabled();

    ImGui::SameLine();
    ImGui::BeginDisabled(!btnState.enableStopTask);
    // --- 停止任务按钮橘色美化 ---
    ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(1.0f, 0.55f, 0.0f, 1.0f));        // 橘色
    ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(1.0f, 0.7f, 0.2f, 1.0f));  // 悬停更亮
    ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.85f, 0.45f, 0.0f, 1.0f)); // 按下更暗
    if (ImGui::Button("停止任务", ImVec2(100, 35))) {
        // 直接使用 DispatchCenter 检查虚拟机连接状态
        std::vector<std::string> connectedVMs;
        try {
            connectedVMs = DispatchCenter::getInstance()->getConnectedVMNames();
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "获取已连接虚拟机列表时发生异常: " + std::string(e.what()));
            VMStateManager::getInstance()->setUIState(selectedVmName, VMUIState::TASK_STOPPED);
            return;
        } catch (...) {
            AddLogInfo(LogLevel::Error, "获取已连接虚拟机列表时发生未知异常");
            VMStateManager::getInstance()->setUIState(selectedVmName, VMUIState::TASK_STOPPED);
            return;
        }

        // 获取当前选中的虚拟机名称
        std::string currentVmName;
        if (current_selected_item < connectedVMs.size()) {
            currentVmName = connectedVMs[current_selected_item];
        } else if (!connectedVMs.empty()) {
            currentVmName = connectedVMs[0];
            AddLogInfo(LogLevel::Warning, "当前选择的虚拟机无效，将使用第一个可用的虚拟机: " + currentVmName);
        }
        
        if (currentVmName.empty()) {
            AddLogInfo(LogLevel::Warning, "没有可用的虚拟机来停止任务");
            VMStateManager::getInstance()->setUIState(selectedVmName, VMUIState::TASK_STOPPED);
            return;
        }

        // 优化：完整的停止任务逻辑 - 停止轮询器 + 清空任务队列 + 重置状态
        try {
            auto* dispatchCenter = DispatchCenter::getInstance();
            if (dispatchCenter) {
                AddLogInfo(LogLevel::Info, "正在停止虚拟机 [" + currentVmName + "] 的任务...");
                
                // 1. 停止任务轮询器，阻止新任务被分发
                dispatchCenter->stopTaskPoller(currentVmName);
                
                // 2. 清空所有切片的任务队列并重置切片状态
                dispatchCenter->clearAllTasksAndResetSlices(currentVmName);
                
                // 3. 清除所有等待状态，让正在等待的任务能正常结束
                dispatchCenter->clearSliceWaitingStates(currentVmName);
                
                AddLogInfo(LogLevel::Info, "虚拟机 [" + currentVmName + "] 任务已完全停止（轮询器已停止，任务队列已清空，状态已重置）");
            } else {
                AddLogInfo(LogLevel::Error, "DispatchCenter不可用，无法停止任务");
            }
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "停止虚拟机 [" + currentVmName + "] 任务时发生异常: " + std::string(e.what()));
        } catch (...) {
            AddLogInfo(LogLevel::Error, "停止虚拟机 [" + currentVmName + "] 任务时发生未知异常");
        }

        VMStateManager::getInstance()->setUIState(selectedVmName, VMUIState::TASK_STOPPED);
    }
    ImGui::PopStyleColor(3);
    ImGui::EndDisabled();

    // 第3行：状态控制按钮
    ImGui::BeginDisabled(!btnState.enablePause);
    if (ImGui::Button("暂停当前", ImVec2(100, 35))) {
        // 直接使用 DispatchCenter 检查虚拟机连接状态，避免使用互斥锁
        std::vector<std::string> connectedVMs;
        try {
            connectedVMs = DispatchCenter::getInstance()->getConnectedVMNames();
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "获取已连接虚拟机列表时发生异常: " + std::string(e.what()));
            return;
        } catch (...) {
            AddLogInfo(LogLevel::Error, "获取已连接虚拟机列表时发生未知异常");
            return;
        }

        // 获取当前选中的虚拟机名称
        std::string selectedVmName;

        // 如果当前选择索引有效，则使用该索引对应的虚拟机
        // 否则使用第一个连接的虚拟机
        if (current_selected_item < connectedVMs.size()) {
            selectedVmName = connectedVMs[current_selected_item];
        }
        else if (!connectedVMs.empty()) {
            selectedVmName = connectedVMs[0];
            AddLogInfo(LogLevel::Warning, "当前选择的虚拟机无效，将使用第一个可用的虚拟机: " + selectedVmName);
        }
        else {
            AddLogInfo(LogLevel::Warning, "没有可用的虚拟机");
            return;
        }

        // 创建一个安全的副本，传递给工作线程
        std::string vmName = selectedVmName;
        
        // 执行暂停操作 - 暂停当前虚拟机的所有切片
        try {
            DispatchCenter::getInstance()->pauseAllSlices(vmName);
            AddLogInfo(LogLevel::Info, "虚拟机 [" + vmName + "] 的所有切片已暂停");
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "暂停虚拟机 [" + vmName + "] 的所有切片时发生异常: " + std::string(e.what()));
            return;
        } catch (...) {
            AddLogInfo(LogLevel::Error, "暂停虚拟机 [" + vmName + "] 的所有切片时发生未知异常");
            return;
        }
        
        VMStateManager::getInstance()->setUIState(selectedVmName, VMUIState::TASK_PAUSED);
    }
    ImGui::EndDisabled();
    
    ImGui::SameLine();
    ImGui::BeginDisabled(!btnState.enableResume);
    if (ImGui::Button("继续当前", ImVec2(100, 35))) {
        // 直接使用 DispatchCenter 检查虚拟机连接状态，避免使用互斥锁
        std::vector<std::string> connectedVMs;
        try {
            connectedVMs = DispatchCenter::getInstance()->getConnectedVMNames();
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "获取已连接虚拟机列表时发生异常: " + std::string(e.what()));
            return;
        } catch (...) {
            AddLogInfo(LogLevel::Error, "获取已连接虚拟机列表时发生未知异常");
            return;
        }

        // 获取当前选中的虚拟机名称
        std::string selectedVmName;

        // 如果当前选择索引有效，则使用该索引对应的虚拟机
        // 否则使用第一个连接的虚拟机
        if (current_selected_item < connectedVMs.size()) {
            selectedVmName = connectedVMs[current_selected_item];
        }
        else if (!connectedVMs.empty()) {
            selectedVmName = connectedVMs[0];
            AddLogInfo(LogLevel::Warning, "当前选择的虚拟机无效，将使用第一个可用的虚拟机: " + selectedVmName);
        }
        else {
            AddLogInfo(LogLevel::Warning, "没有可用的虚拟机");
            return;
        }

        // 创建一个安全的副本，传递给工作线程
        std::string vmName = selectedVmName;

        // 执行恢复操作 - 恢复当前虚拟机的所有暂停切片
        try {
            DispatchCenter::getInstance()->resumeAllPausedSlices(vmName);
            AddLogInfo(LogLevel::Info, "虚拟机 [" + vmName + "] 的所有暂停切片已恢复");
        } catch (const std::exception& e) {
            AddLogInfo(LogLevel::Error, "恢复虚拟机 [" + vmName + "] 的所有切片时发生异常: " + std::string(e.what()));
            return;
        } catch (...) {
            AddLogInfo(LogLevel::Error, "恢复虚拟机 [" + vmName + "] 的所有切片时发生未知异常");
            return;
        }
        
        // 检查是否有任务需要执行，决定UI状态
        VMControllerInfo* controllerInfo = nullptr;
        bool hasTasks = false;
        try {
            controllerInfo = DispatchCenter::getInstance()->getVMControllerInfo(selectedVmName);
            if (controllerInfo && controllerInfo->taskQueue) {
                hasTasks = !controllerInfo->taskQueue->empty();
            }
        } catch (...) {
            // 获取任务信息失败，使用默认状态
        }
        
        if (hasTasks) {
            AddLogInfo(LogLevel::Info, "虚拟机 [" + vmName + "] 已恢复，将继续执行任务队列中的任务");
            VMStateManager::getInstance()->setUIState(selectedVmName, VMUIState::TASK_RUNNING);
        } else {
            AddLogInfo(LogLevel::Info, "虚拟机 [" + vmName + "] 已恢复，等待新任务");
            VMStateManager::getInstance()->setUIState(selectedVmName, VMUIState::CONNECTED_IDLE);
        }
    }
    ImGui::EndDisabled();

    ImGui::EndChild(); // rightthird
    ImGui::PopStyleColor(); // 释放 ImGuiCol_Border 
    ImGui::PopStyleVar();   // 释放 ImGuiStyleVar_ChildRounding

    ImGui::BeginChild("rightthird", ImVec2(300.0f, 0.0f));// rightsecond
    bool ShowAppLog = false;
    ShowLog(&ShowAppLog); //调用log日志框
    ImGui::EndChild(); // rightsecond

    ImGui::PopStyleColor(); // 释放 ImGuiCol_BorderShadow

    //右边组件结束
    ImGui::EndChild(); // rightContent
    ImGui::EndChild(); // bottomArea
    ImGui::EndChild(); // RightpArea
    ImGui::End(); // MainBackground
}

// ==========================================================================================
// ================================ 切片配置UI ========================================
// ==========================================================================================

// 显示切片配置UI
void ShowSliceConfigurationUI(const std::string& vmName, bool vmConnected) {
    if (vmName.empty()) {
        return;
    }
    
    // 静态变量存储UI状态
    static std::map<std::string, std::pair<int, int>> tempSliceConfigs; // 临时存储用户输入
    static std::map<std::string, bool> configChanged; // 标记配置是否有更改
    
    // 获取当前虚拟机的切片配置
    auto dispatchCenter = DispatchCenter::getInstance();
    
    // 🔧 只在第一次初始化时同步配置
    if (tempSliceConfigs.find(vmName) == tempSliceConfigs.end()) {
        if (vmConnected && dispatchCenter) {
            auto currentConfig = dispatchCenter->getVMSliceConfiguration(vmName);
            tempSliceConfigs[vmName] = currentConfig;
            AddLogInfo(LogLevel::Debug, "[UI] " + vmName + " 切片配置初始化: " + 
                       std::to_string(currentConfig.first) + "x" + std::to_string(currentConfig.second));
        } else {
            // 设置默认配置：1行2列
            tempSliceConfigs[vmName] = std::make_pair(1, 2);
            AddLogInfo(LogLevel::Debug, "[UI] " + vmName + " 使用默认切片配置: 1x2");
        }
        configChanged[vmName] = false;
    }
    
    // 获取临时配置
    int& tempRows = tempSliceConfigs[vmName].first;
    int& tempCols = tempSliceConfigs[vmName].second;
    
    ImGui::Separator();
    

    
    // 显示标题和配置验证信息
    std::string titleText = "切片配置 [" + vmName + "]";
    if (ScreenFrameDistributor::isValidSliceConfiguration(tempRows, tempCols)) {
        int totalSlices = tempRows * tempCols;
        titleText += " - 总共: " + std::to_string(totalSlices) + " 片";
        ImGui::Text("%s", titleText.c_str());
    } else {
        titleText += " - ";
        ImGui::Text("%s", titleText.c_str());
        ImGui::SameLine();
        ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "配置无效!");
    }
    
    // 获取当前虚拟机的UI状态（用于判断是否在执行任务）
    VMUIState uiState = VMUIState::DISCONNECTED;
    if (!vmName.empty()) {
        uiState = VMStateManager::getInstance()->getUIState(vmName);
    }
    bool taskRunningOrPaused = (uiState == VMUIState::TASK_RUNNING || uiState == VMUIState::TASK_PAUSED);

    // 根据连接状态和任务状态决定是否禁用输入控件
    if (!vmConnected || taskRunningOrPaused) {
        ImGui::BeginDisabled();
    }
    
    // 🔧 重新设计布局：使用表格式布局避免重复显示
    ImGui::Columns(2, "SliceConfig", false);
    ImGui::SetColumnWidth(0, 120);
    
    // 第一列：行数配置
    ImGui::Text("行数:");
    ImGui::NextColumn();
    
    // 行数控制：- [数字] +
    if (ImGui::Button("-##row", ImVec2(25, 0))) {
        int oldRows = tempRows;
        tempRows--;
        if (tempRows < 1) tempRows = 1;
        if (tempRows != oldRows) {
            configChanged[vmName] = true;
        }
    }
    ImGui::SameLine();
    
    // 显示当前数值，不使用InputInt的步进按钮
    ImGui::SetNextItemWidth(40);
    if (ImGui::InputInt("##rows", &tempRows, 0, 0)) {
        if (tempRows < 1) tempRows = 1;
        if (tempRows > 4) tempRows = 4;
        configChanged[vmName] = true;
    }
    
    ImGui::SameLine();
    if (ImGui::Button("+##row", ImVec2(25, 0))) {
        int oldRows = tempRows;
        tempRows++;
        if (tempRows > 4) tempRows = 4;
        if (tempRows != oldRows) {
            configChanged[vmName] = true;
        }
    }
    
    ImGui::NextColumn();
    
    // 第二列：列数配置
    ImGui::Text("列数:");
    ImGui::NextColumn();
    
    // 列数控制：- [数字] +
    if (ImGui::Button("-##col", ImVec2(25, 0))) {
        int oldCols = tempCols;
        tempCols--;
        if (tempCols < 1) tempCols = 1;
        if (tempCols != oldCols) {
            configChanged[vmName] = true;
        }
    }
    ImGui::SameLine();
    
    // 显示当前数值，不使用InputInt的步进按钮
    ImGui::SetNextItemWidth(40);
    if (ImGui::InputInt("##cols", &tempCols, 0, 0)) {
        if (tempCols < 1) tempCols = 1;
        if (tempCols > 4) tempCols = 4;
        configChanged[vmName] = true;
    }
    
    ImGui::SameLine();
    if (ImGui::Button("+##col", ImVec2(25, 0))) {
        int oldCols = tempCols;
        tempCols++;
        if (tempCols > 4) tempCols = 4;
        if (tempCols != oldCols) {
            configChanged[vmName] = true;
        }
    }
    
    ImGui::Columns(1); // 恢复到单列布局
    
    // 恢复控件启用状态
    if (!vmConnected || taskRunningOrPaused) {
        ImGui::EndDisabled();
    }
    
    // 显示切片编号预览
    ImGui::BeginChild("SlicePreview", ImVec2(0, 80), true);
    
    // 获取当前连接的虚拟机信息
    bool isVmConnected = vmConnected && dispatchCenter;
    
    for (int row = 0; row < tempRows; row++) {
        for (int col = 0; col < tempCols; col++) {
            int sliceNumber = row * tempCols + col + 1;
            
            if (col > 0) ImGui::SameLine();
            
            // 获取切片状态 - 使用新的状态管理器
            bool isSlicePaused = false;
            bool isSliceRunning = false;
            bool isSliceWaiting = false;
            
            if (isVmConnected) {
                auto* stateManager = VMStateManager::getInstance();
                SliceState sliceState = stateManager->getSliceState(vmName, sliceNumber);
                
                isSlicePaused = (sliceState == SliceState::PAUSED_STATE);
                isSliceRunning = (sliceState == SliceState::RUNNING_STATE);
                isSliceWaiting = (sliceState == SliceState::WAITING_SCREEN_STILL_STATE || 
                                 sliceState == SliceState::WAITING_VISUAL_MATCH_STATE);
            }
            
            // 根据切片状态设置颜色
            ImVec4 buttonColor;
            if (!isVmConnected) {
                // 虚拟机未连接：灰色
                buttonColor = ImVec4(0.5f, 0.5f, 0.5f, 1.0f);
            } else if (isSlicePaused) {
                // 切片暂停：深红色
                buttonColor = ImVec4(0.8f, 0.2f, 0.2f, 1.0f);
            } else if (isSliceWaiting) {
                // 切片等待中：橙色
                buttonColor = ImVec4(1.0f, 0.6f, 0.2f, 1.0f);
            } else if (isSliceRunning) {
                // 切片运行中：浅蓝色
                buttonColor = ImVec4(0.2f, 0.6f, 1.0f, 1.0f);
            } else {
                // 默认状态：浅灰色
                buttonColor = ImVec4(0.7f, 0.7f, 0.7f, 1.0f);
            }
            
            // 设置按钮颜色
            ImGui::PushStyleColor(ImGuiCol_Button, buttonColor);
            ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(buttonColor.x * 1.2f, buttonColor.y * 1.2f, buttonColor.z * 1.2f, 1.0f));
            ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(buttonColor.x * 0.8f, buttonColor.y * 0.8f, buttonColor.z * 0.8f, 1.0f));
            
            // 创建按钮标识符
            std::string buttonLabel = std::to_string(sliceNumber);
            std::string buttonId = buttonLabel + "##slice_" + std::to_string(sliceNumber) + "_" + vmName;
            
            // 显示切片按钮
            if (ImGui::Button(buttonId.c_str(), ImVec2(40, 30))) {
                // 单击事件（暂时留空，主要功能通过双击实现）
            }
            
            // 检测双击事件
            if (ImGui::IsItemHovered() && ImGui::IsMouseDoubleClicked(ImGuiMouseButton_Left) && isVmConnected) {
                try {
                    if (isSlicePaused) {
                        // 切片已暂停，恢复切片
                        dispatchCenter->resumeSlice(vmName, sliceNumber);
                        AddLogInfo(LogLevel::Info, "切片 " + std::to_string(sliceNumber) + " 已恢复");
                    } else {
                        // 切片未暂停，暂停切片
                        dispatchCenter->pauseSlice(vmName, sliceNumber);
                        AddLogInfo(LogLevel::Info, "切片 " + std::to_string(sliceNumber) + " 已暂停");
                    }
                } catch (const std::exception& e) {
                    AddLogInfo(LogLevel::Error, "操作切片 " + std::to_string(sliceNumber) + " 时发生异常: " + std::string(e.what()));
                } catch (...) {
                    AddLogInfo(LogLevel::Error, "操作切片 " + std::to_string(sliceNumber) + " 时发生未知异常");
                }
            }
            
            // 显示工具提示
            if (ImGui::IsItemHovered()) {
                ImGui::BeginTooltip();
                ImGui::Text("切片 %d", sliceNumber);
                if (!isVmConnected) {
                    ImGui::Text("状态: 虚拟机未连接");
                } else if (isSlicePaused) {
                    ImGui::Text("状态: 已暂停");
                    ImGui::Text("双击恢复");
                } else if (isSliceWaiting) {
                    ImGui::Text("状态: 等待中");
                    ImGui::Text("双击暂停");
                } else if (isSliceRunning) {
                    ImGui::Text("状态: 运行中");
                    ImGui::Text("双击暂停");
                } else {
                    ImGui::Text("状态: 就绪");
                    ImGui::Text("双击暂停");
                }
                ImGui::EndTooltip();
            }
            
            // 恢复按钮颜色
            ImGui::PopStyleColor(3);
        }
    }
    
    ImGui::EndChild();
    

    
    // 应用/重置按钮
    if (vmConnected && configChanged[vmName]) {
        // 只有配置有效且已连接时才能应用
        bool configValid = ScreenFrameDistributor::isValidSliceConfiguration(tempRows, tempCols);
        if (!configValid) {
            ImGui::BeginDisabled();
        }
        
        if (ImGui::Button("应用配置", ImVec2(80, 25))) {
            // 应用配置到DispatchCenter
            if (dispatchCenter) {
                AddLogInfo(LogLevel::Info, "[UI] 正在应用 " + vmName + " 的切片配置: " + 
                           std::to_string(tempRows) + "x" + std::to_string(tempCols));
                dispatchCenter->setVMSliceConfiguration(vmName, tempRows, tempCols);
                configChanged[vmName] = false;
                AddLogInfo(LogLevel::Info, "[UI] 虚拟机 [" + vmName + "] 切片配置已成功更新为: " + 
                           ScreenFrameDistributor::getSliceConfigurationDescription(tempRows, tempCols));
            }
        }
        
        if (!configValid) {
            ImGui::EndDisabled();
        }
        
        ImGui::SameLine();
        if (ImGui::Button("重置", ImVec2(50, 25))) {
            // 恢复到当前DispatchCenter中的配置
            if (dispatchCenter) {
                auto currentConfig = dispatchCenter->getVMSliceConfiguration(vmName);
                tempSliceConfigs[vmName] = currentConfig;
                configChanged[vmName] = false;
            }
        }
    } else {
        // 配置未更改或未连接时显示状态
        ImGui::BeginDisabled();
        if (!vmConnected) {
            ImGui::Button("需要连接", ImVec2(80, 25));
        } else {
            ImGui::Button("已应用", ImVec2(80, 25));
        }
        ImGui::EndDisabled();
        
        ImGui::SameLine();
        if (!vmConnected) {
            ImGui::TextDisabled("虚拟机未连接");
        } else {
            ImGui::TextDisabled("配置无更改");
        }
    }
    
    ImGui::Separator();
}