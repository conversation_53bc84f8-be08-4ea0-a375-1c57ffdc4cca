// ========== 虚拟机1：测试切片1 ==========
//地名与坐标1110,30,170,25
//任务详情栏35,270,365,470
//中间任务执行栏470,270,350,480
//任务栏1050,400,230,200
slice(1) {
	//TEXT_RECOGNIZE(1,1050,400,230,200)
	//TEXT_MATCH(正传猫儿偷鸡,left,1050,400,230,200)
	//VISUAL_MATCH(taskicon,left)
	//TEXT_MATCH(猫儿偷鸡, left, 470,270,350,480)
	//WAIT_FOR_SCREEN_STILL(1110,30,170,25,5,60)
	//MOUSE_MOVE(100, 200)
	
	# 原来的完整匹配（推荐）
	//TEXT_MATCH(正传小鸡咕咕, left, 1050, 400, 230, 200)
	
	# 新增：支持部分匹配的低阈值版本
	# 阈值0.5可以让"小鸡咕咕"匹配到"正传小鸡咕咕"
	TEXT_MATCH(小鸡咕咕, left, 1050, 400, 230, 200, 0.5)
	
	# 另一个部分匹配示例 - 如果OCR识别出"正传猫儿偷鸡"，这样也能匹配
	//TEXT_MATCH(猫儿偷鸡, left, 470, 270, 350, 480, 0.5)
}

// ========== 虚拟机1：测试切片2 ==========  
slice(2) {
    // 这些任务应该与切片1并行执行（因为是不同切片）
}