#pragma once
#include <string>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <thread>
#include <atomic>
#include <chrono>

// 声明渲染 UI 的接口

struct AppLog; // 前向声明

// 日志等级枚举
enum class LogLevel {
    Debug,    // 调试信息
    Info,     // 普通信息
    Warning,  // 警告
    Error     // 错误
};

// 线程安全的日志条目结构
struct LogEntry {
    LogLevel level;
    std::string message;
    std::chrono::system_clock::time_point timestamp;
    
    LogEntry(LogLevel lvl, const std::string& msg) 
        : level(lvl), message(msg), timestamp(std::chrono::system_clock::now()) {}
};

// 线程安全的日志队列
class ThreadSafeLogQueue {
private:
    std::queue<LogEntry> queue_;
    mutable std::mutex mutex_;
    std::condition_variable condition_;
    std::atomic<bool> shutdown_{false};

public:
    void push(const LogEntry& entry);
    bool pop(LogEntry& entry, std::chrono::milliseconds timeout = std::chrono::milliseconds(100));
    void shutdown();
    size_t size() const;
};

void AddLogInfo(LogLevel level, const std::string& message);
void ShowLog(bool* p_open);

// 日志系统管理器
class LogManager {
private:
    static std::unique_ptr<LogManager> instance_;
    static std::mutex instanceMutex_;
    
    ThreadSafeLogQueue logQueue_;
    std::thread logThread_;
    std::atomic<bool> running_{false};
    
    LogManager();
    void logThreadFunction();
    
    // 私有的创建方法，用于在单例中创建实例
    static std::unique_ptr<LogManager> createInstance();
    
public:
    ~LogManager();
    
    static LogManager* getInstance();
    void startup();
    void shutdown();
    void addLog(LogLevel level, const std::string& message);
    
    // 禁用拷贝和赋值
    LogManager(const LogManager&) = delete;
    LogManager& operator=(const LogManager&) = delete;
};
