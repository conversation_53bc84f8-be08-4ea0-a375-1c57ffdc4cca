@echo off
echo ========================================
echo       可执行文件保护脚本
echo ========================================

set EXE_NAME=qnyhMain2.exe
set BUILD_DIR=..\out\build\x64-release\src
set PROTECTED_DIR=..\Protected

:: 检查可执行文件是否存在
if not exist "%BUILD_DIR%\%EXE_NAME%" (
    echo 错误: 找不到 %BUILD_DIR%\%EXE_NAME%
    echo 请先编译项目（Release模式）
    pause
    exit /b 1
)

:: 创建保护后的文件目录
if not exist "%PROTECTED_DIR%" mkdir "%PROTECTED_DIR%"

:: 1. 复制原始文件
echo 步骤1: 复制原始文件...
copy "%BUILD_DIR%\%EXE_NAME%" "%PROTECTED_DIR%\%EXE_NAME%.backup"

:: 2. UPX压缩加密（如果可用）
echo 步骤2: UPX压缩加密...
if exist "upx\upx.exe" (
    upx\upx.exe --best --lzma "%BUILD_DIR%\%EXE_NAME%"
    if %errorlevel% equ 0 (
        echo UPX压缩成功
    ) else (
        echo UPX压缩失败，继续其他保护...
    )
) else (
    echo 警告: UPX未找到，请下载UPX到 tools\upx\ 目录
    echo 下载地址: https://github.com/upx/upx/releases
)

:: 3. 移除调试信息（如果strip可用）
echo 步骤3: 移除调试信息...
if exist "strip.exe" (
    strip.exe "%BUILD_DIR%\%EXE_NAME%"
    echo 调试信息已移除
) else (
    echo 提示: 使用 -s 编译参数可移除调试信息
)

:: 4. 复制保护后的文件
echo 步骤4: 保存保护后的文件...
copy "%BUILD_DIR%\%EXE_NAME%" "%PROTECTED_DIR%\%EXE_NAME%"

:: 5. 复制必要的DLL文件
echo 步骤5: 复制依赖文件...
if exist "%BUILD_DIR%\PaddleOCR.dll" (
    copy "%BUILD_DIR%\PaddleOCR.dll" "%PROTECTED_DIR%\"
    echo PaddleOCR.dll 已复制
)

if exist "%BUILD_DIR%\opencv_world*.dll" (
    copy "%BUILD_DIR%\opencv_world*.dll" "%PROTECTED_DIR%\"
    echo OpenCV DLL 已复制
)

:: 复制配置目录
if exist "%BUILD_DIR%\assets" (
    xcopy "%BUILD_DIR%\assets" "%PROTECTED_DIR%\assets" /E /I /Y
    echo 配置文件已复制
)

echo ========================================
echo 保护完成！
echo 原始文件: %BUILD_DIR%\%EXE_NAME%
echo 保护后文件: %PROTECTED_DIR%\%EXE_NAME%
echo 备份文件: %PROTECTED_DIR%\%EXE_NAME%.backup
echo ========================================
pause 