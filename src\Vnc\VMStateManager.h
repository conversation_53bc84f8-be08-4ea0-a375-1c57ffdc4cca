#pragma once

#include <string>
#include <map>
#include <mutex>
#include <atomic>
#include <memory>
#include <chrono>
#include <vector>

// 取消可能冲突的Windows宏定义
#ifdef ERROR
#undef ERROR
#endif

// VM状态枚举
enum class VMConnectionState : int {
    DISCONNECTED_STATE,
    CONNECTING_STATE,
    CONNECTED_STATE,
    ERROR_STATE
};

enum class VMTaskState : int {
    IDLE_STATE,
    RUNNING_STATE,
    PAUSED_STATE,
    ERROR_STATE
};

enum class SliceState : int {
    IDLE_STATE,
    RUNNING_STATE,
    PAUSED_STATE,
    WAITING_SCREEN_STILL_STATE,
    WAITING_VISUAL_MATCH_STATE,
    ERROR_STATE
};

enum class VMUIState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED_IDLE,
    TASK_RUNNING,
    TASK_PAUSED,
    TASK_STOPPED
};

// 单个切片的状态信息
struct SliceStateInfo {
    SliceState state = SliceState::IDLE_STATE;
    int taskQueueSize = 0;
    std::string lastError;
    std::chrono::steady_clock::time_point lastStateChange;
    
    SliceStateInfo() {
        lastStateChange = std::chrono::steady_clock::now();
    }
};

// 单个VM的完整状态信息
struct VMState {
    // 连接状态
    VMConnectionState connectionState = VMConnectionState::DISCONNECTED_STATE;
    
    // 任务执行状态
    VMTaskState taskState = VMTaskState::IDLE_STATE;
    
    // 脚本执行状态
    bool isScriptRunning = false;
    bool isScriptPaused = false;
    int currentScriptLine = 0;
    int totalScriptLines = 0;
    int currentSliceForScript = -1;
    
    // 切片状态映射 (切片号 -> 切片状态)
    std::map<int, SliceStateInfo> sliceStates;
    
    // 任务轮询器状态
    bool isPollerRunning = false;
    bool isPollerPaused = false;
    
    // 错误信息
    std::string lastError;
    std::chrono::steady_clock::time_point lastErrorTime;
    
    // 统计信息
    int totalTasksExecuted = 0;
    int totalTasksFailed = 0;
    std::chrono::steady_clock::time_point startTime;
    
    VMUIState uiState = VMUIState::DISCONNECTED;
    
    VMState() {
        startTime = std::chrono::steady_clock::now();
        lastErrorTime = std::chrono::steady_clock::now();
    }
};

// VM状态管理器类 - 线程安全的状态管理
class VMStateManager {
public:
    static VMStateManager* getInstance();
    
    // VM基本状态管理
    void setVMConnectionState(const std::string& vmName, VMConnectionState state);
    VMConnectionState getVMConnectionState(const std::string& vmName) const;
    
    void setVMTaskState(const std::string& vmName, VMTaskState state);
    VMTaskState getVMTaskState(const std::string& vmName) const;
    
    // 脚本执行状态管理
    void setScriptRunning(const std::string& vmName, bool running);
    bool isScriptRunning(const std::string& vmName) const;
    
    void setScriptPaused(const std::string& vmName, bool paused);
    bool isScriptPaused(const std::string& vmName) const;
    
    void setCurrentScriptLine(const std::string& vmName, int lineNumber);
    int getCurrentScriptLine(const std::string& vmName) const;
    
    void setTotalScriptLines(const std::string& vmName, int totalLines);
    int getTotalScriptLines(const std::string& vmName) const;
    
    void setCurrentSliceForScript(const std::string& vmName, int sliceNumber);
    int getCurrentSliceForScript(const std::string& vmName) const;
    
    // 切片状态管理
    void setSliceState(const std::string& vmName, int sliceNumber, SliceState state);
    SliceState getSliceState(const std::string& vmName, int sliceNumber) const;
    
    void setSliceTaskQueueSize(const std::string& vmName, int sliceNumber, int queueSize);
    int getSliceTaskQueueSize(const std::string& vmName, int sliceNumber) const;
    
    void setSliceError(const std::string& vmName, int sliceNumber, const std::string& error);
    std::string getSliceError(const std::string& vmName, int sliceNumber) const;
    
    // 任务轮询器状态管理
    void setPollerRunning(const std::string& vmName, bool running);
    bool isPollerRunning(const std::string& vmName) const;
    
    void setPollerPaused(const std::string& vmName, bool paused);
    bool isPollerPaused(const std::string& vmName) const;
    
    // 错误管理
    void setLastError(const std::string& vmName, const std::string& error);
    std::string getLastError(const std::string& vmName) const;
    
    // 统计信息
    void incrementTasksExecuted(const std::string& vmName);
    void incrementTasksFailed(const std::string& vmName);
    int getTotalTasksExecuted(const std::string& vmName) const;
    int getTotalTasksFailed(const std::string& vmName) const;
    
    // 便利方法
    bool isVMConnected(const std::string& vmName) const;
    bool isSlicePaused(const std::string& vmName, int sliceNumber) const;
    bool isSliceRunning(const std::string& vmName, int sliceNumber) const;
    bool isSliceWaiting(const std::string& vmName, int sliceNumber) const;
    bool isSliceWaitingForScreenStill(const std::string& vmName, int sliceNumber) const;
    bool isSliceWaitingForVisualMatch(const std::string& vmName, int sliceNumber) const;
    
    // 获取所有切片状态（用于UI显示）
    std::map<int, SliceState> getAllSliceStates(const std::string& vmName) const;
    
    // 获取VM完整状态（用于调试）
    VMState getVMState(const std::string& vmName) const;
    
    // 初始化和清理
    void initializeVM(const std::string& vmName);
    void removeVM(const std::string& vmName);
    void clear();
    
    // 新增全局连接状态管理接口
    void setConnecting(bool connecting);
    bool isConnecting() const;
    void setConnected(bool connected);
    bool isConnected() const;
    void setAvailableVMs(const std::vector<std::string>& vms);
    std::vector<std::string> getAvailableVMs() const;
    void setVMsConnected(bool connected);
    bool getVMsConnected() const;
    
    // 新增UI状态管理接口
    void setUIState(const std::string& vmName, VMUIState state);
    VMUIState getUIState(const std::string& vmName) const;
    
    // 禁用拷贝构造和赋值
    VMStateManager(const VMStateManager&) = delete;
    VMStateManager& operator=(const VMStateManager&) = delete;

private:
    VMStateManager() = default;
    ~VMStateManager() = default;
    
    // 内部辅助方法
    VMState* getVMStatePtr(const std::string& vmName) const;
    SliceStateInfo* getSliceStatePtr(const std::string& vmName, int sliceNumber) const;
    
    // 静态实例
    static VMStateManager* instance_;
    static std::mutex instanceMutex_;
    
    // 状态存储
    mutable std::mutex statesMutex_;
    std::map<std::string, std::unique_ptr<VMState>> vmStates_;
}; 