#include "scripteditor.h"
#include "imgui.h"
#include "imgui_stdlib.h" // 使用 std::string 和 ImGui::InputText 时需要

#include <fstream>
#include <filesystem> // 需要 C++17
#include <iostream>   // 用于“运行”功能的占位符
#include <vector>
#include <string>

namespace fs = std::filesystem;

// 将所有代码都包含在 MyScriptEditor 命名空间中
namespace MyScriptEditor {

    // --- 状态变量 ---
    // 将静态变量移入命名空间
    static std::vector<Script> availableScripts;
    static std::vector<std::string> availableDirs;
    static int selectedDirIndex = 0;
    static int selectedScriptIndex = -1;
    // 确保 selectedDirPath 总是指向一个有效目录
    static std::string selectedDirPath = "./scripts";
    static std::string statusMessage = "";
    // 展示弹出框
    static bool showDeleteConfirmDialog = false;
    static Script* scriptToDelete = nullptr;

    // --- 辅助函数声明 ---
    static void LoadScriptsFromDirectory(const std::string& dirPath);
    static void LoadDirectories();

    // --- 核心逻辑 ---

    // 扫描“./scripts”基础目录以查找子目录
    static void LoadDirectories() {
        availableDirs.clear();
        availableDirs.push_back("./script"); // 总是包含根脚本目录

        std::string scriptsDir = "./script";
        if (!fs::exists(scriptsDir)) {
            try {
                fs::create_directories(scriptsDir);
                statusMessage = "已创建基础脚本目录: ./script/";
            }
            catch (const fs::filesystem_error& e) {
                statusMessage = "创建基础目录时出错: ";
                statusMessage += e.what();
                return;
            }
        }

        try {
            // 遍历 './scripts' 目录下的所有条目
            for (const auto& entry : fs::directory_iterator(scriptsDir)) {
                if (entry.is_directory()) { // 如果条目是目录
                    availableDirs.push_back(entry.path().string()); // 添加到目录列表
                }
            }
        }
        catch (const fs::filesystem_error& e) {
            statusMessage = "列出目录时出错: ";
            statusMessage += e.what();
        }

        // 确保 selectedDirIndex 是一个有效索引
        if (selectedDirIndex >= availableDirs.size()) {
            selectedDirIndex = 0; // 如果之前的选择无效，默认选择第一个
        }
        if (!availableDirs.empty()) {
            selectedDirPath = availableDirs[selectedDirIndex]; // 更新 selectedDirPath
        }
        else {
            selectedDirPath = "./scripts"; // 如果没有目录，确保有默认路径
        }
    }

    // 从给定的目录路径加载所有 .txt 脚本
    static void LoadScriptsFromDirectory(const std::string& dirPath) {
        availableScripts.clear();
        selectedScriptIndex = -1; // 重置选择

        if (!fs::exists(dirPath)) {
            statusMessage = "目录不存在: " + dirPath;
            return;
        }

        try {
            for (const auto& entry : fs::directory_iterator(dirPath)) {
                if (entry.is_regular_file() && entry.path().extension() == ".txt") {
                    Script script;
                    script.name = entry.path().stem().string();
                    script.filePath = entry.path().string();
                    availableScripts.push_back(script);
                }
            }
            statusMessage = "脚本已从以下路径加载: " + dirPath;
        }
        catch (const fs::filesystem_error& e) {
            statusMessage = "列出脚本时出错: ";
            statusMessage += e.what();
        }
    }

    // 这是 MyScriptEditor::LoadScripts 的定义，它在 .h 文件中声明
    void LoadScripts() {
        LoadDirectories(); // 加载目录
        // 初始加载时，确保 selectedDirPath 是一个有效路径，例如第一个可用目录
        if (!availableDirs.empty()) {
            selectedDirPath = availableDirs[selectedDirIndex]; // 根据当前选中的索引加载脚本
        }
        else {
            selectedDirPath = "./scripts"; // 如果没有目录，使用默认路径
        }
        LoadScriptsFromDirectory(selectedDirPath); // 加载脚本
    }

    // 加载指定脚本文件的内容 (已在命名空间内，无需修改)
    void LoadScriptContent(Script& script) {
        if (script.filePath.empty()) {
            statusMessage = "正在编辑新脚本: " + script.name;
            return;
        }
        std::ifstream file(script.filePath);
        if (file.is_open()) {
            script.content.assign((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
            file.close();
            statusMessage = "脚本已加载: " + script.name;
        }
        else {
            statusMessage = "加载脚本内容时出错: " + script.name;
            script.content = "// 无法从以下路径加载脚本内容: " + script.filePath;
        }
    }

    // 保存脚本内容到文件 (已在命名空间内，无需修改)
    void SaveScript(const Script& script) {
        if (script.filePath.empty()) {
            statusMessage = "错误: 文件路径为空 '" + script.name + "'. 请使用 '保存到磁盘'.";
            return;
        }
        std::ofstream file(script.filePath);
        if (file.is_open()) {
            file << script.content;
            file.close();
            statusMessage = "脚本已保存: " + script.filePath;
        }
        else {
            statusMessage = "保存脚本时出错: " + script.filePath;
        }
    }

    // --- UI 渲染 ---

    // 显示脚本编辑器的主窗口 (已在命名空间内，无需修改)
    void ShowScriptEditorWindow(bool* p_open) {
        ImGui::BeginChild("Script Editor", ImVec2(0, 0), true);
        // 如果目录列表为空，则进行初始加载
        if (availableDirs.empty()) {
            LoadDirectories();
            if (!availableDirs.empty()) {
                selectedDirPath = availableDirs[selectedDirIndex];
            }
            else {
                selectedDirPath = "./scripts";
            }
            LoadScriptsFromDirectory(selectedDirPath);
        }

        // 显示状态消息
        if (!statusMessage.empty()) {
            ImGui::TextColored(ImVec4(0.4f, 1.0f, 0.4f, 1.0f), "  当前状态: %s", statusMessage.c_str());
            ImGui::Separator();
        }

        // 设置三列布局
        ImGui::Columns(3, "ScriptEditorLayout");
        ImGui::SetColumnWidth(0, 160); // 第0列（目录列表）的宽度
        ImGui::SetColumnWidth(1, 160); // 第1列（脚本列表）的宽度

        // --- 第0列：目录列表 ---
        ImGui::BeginChild("DirectoryListPane");
        ImGui::Text("文件夹分类:");
        if (ImGui::Button("刷新列表", ImVec2(-1, 0))) {
            LoadDirectories();
            LoadScriptsFromDirectory(selectedDirPath); // 重新加载当前目录的脚本
        }
        ImGui::Separator();

        // 使用 BeginChild 替代 BeginListBox
        ImGui::BeginChild("DirectoryListInner", ImVec2(0, 0), true);
        for (int i = 0; i < availableDirs.size(); ++i) {
            const bool is_selected = (selectedDirIndex == i);
            fs::path dirPath(availableDirs[i]);
            // 对于根目录 "./scripts"，直接显示 "./scripts"
            // 对于子目录，显示其文件名部分
            std::string dirDisplayName = (dirPath == "./scripts" || dirPath.filename().empty()) ? "./scripts" : dirPath.filename().string();

            if (ImGui::Selectable(dirDisplayName.c_str(), is_selected)) {
                if (selectedDirIndex != i) {
                    selectedDirIndex = i;
                    selectedDirPath = availableDirs[i]; // 更新当前选中的目录路径
                    LoadScriptsFromDirectory(selectedDirPath); // 为新选中的目录加载脚本
                }
            }
            if (is_selected) {
                ImGui::SetItemDefaultFocus();
            }
        }
        ImGui::EndChild(); // 结束内部子窗口
        ImGui::EndChild();
        ImGui::NextColumn();

        // --- 第1列：脚本列表 ---
        ImGui::BeginChild("ScriptListPane");
        ImGui::Text("脚本列表:");
        if (ImGui::Button("新建脚本", ImVec2(-1, 0))) {
            selectedScriptIndex = availableScripts.size();
            Script newScript;
            newScript.name = "NewScript" + std::to_string(availableScripts.size());
            newScript.filePath = ""; // 新脚本尚无路径
            newScript.content = "// 新脚本位于 " + selectedDirPath + "\n// 请编辑名称后使用 '保存到磁盘'.";
            availableScripts.push_back(newScript);
            statusMessage = "已创建新脚本草稿: " + newScript.name;
        }
        ImGui::Separator();

        // 使用 BeginChild 替代 BeginListBox
        ImGui::BeginChild("ScriptListInner", ImVec2(0, 0), true);
        for (int i = 0; i < availableScripts.size(); ++i) {
            const bool is_selected = (selectedScriptIndex == i);
            if (ImGui::Selectable(availableScripts[i].name.c_str(), is_selected)) {
                if (selectedScriptIndex != i || availableScripts[i].content.empty()) {
                    selectedScriptIndex = i;
                    if (!availableScripts[selectedScriptIndex].filePath.empty()) {
                        LoadScriptContent(availableScripts[selectedScriptIndex]);
                    }
                    else {
                        statusMessage = "正在编辑新脚本: " + availableScripts[selectedScriptIndex].name;
                    }
                }
            }
            if (is_selected) {
                ImGui::SetItemDefaultFocus();
            }
        }
        ImGui::EndChild(); // 结束内部子窗口
        ImGui::EndChild();
        ImGui::NextColumn();


        // --- 第2列：编辑器窗格 ---
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(10, 10)); // 窗格内边距
        ImGui::BeginChild("ScriptEditPane");
        if (selectedScriptIndex >= 0 && selectedScriptIndex < availableScripts.size()) {
            Script& currentScript = availableScripts[selectedScriptIndex];
            std::string oldFilePath = currentScript.filePath;

            // 记录原始文件名（不含扩展名）
            std::string originalFileName;
            if (!oldFilePath.empty()) {
                fs::path p(oldFilePath);
                originalFileName = p.stem().string();
            }

            // 编辑名称
            ImGui::InputText("修改脚本名称", &currentScript.name);
            {
                // 调整按钮样式以增加内部填充
                ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(5, 5));
                {   // 绿色按钮样式作用域
                    ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.8f, 0.2f, 1.0f));       // 正常状态为绿色
                    ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.3f, 0.9f, 0.3f, 1.0f)); // 鼠标悬停时较亮的绿色
                    ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.1f, 0.7f, 0.1f, 1.0f));  // 点击时较深的绿色

                    if (ImGui::Button("保存到磁盘")) {
                        std::string newFilePath = selectedDirPath + "/" + currentScript.name + ".txt";
                        // 如果是已存在脚本且名称被修改，先重命名
                        if (!currentScript.filePath.empty() && currentScript.filePath != newFilePath) {
                            try {
                                fs::rename(currentScript.filePath, newFilePath);
                                currentScript.filePath = newFilePath;
                                statusMessage = "脚本已重命名并保存: " + currentScript.filePath;
                            }
                            catch (const fs::filesystem_error& e) {
                                statusMessage = "重命名文件失败: " + std::string(e.what());
                            }
                        }
                        else {
                            currentScript.filePath = newFilePath;
                        }
                        SaveScript(currentScript);

                        LoadScriptsFromDirectory(selectedDirPath);
                        for (int i = 0; i < availableScripts.size(); ++i) {
                            if (availableScripts[i].filePath == currentScript.filePath) {
                                selectedScriptIndex = i;
                                LoadScriptContent(availableScripts[i]);
                                break;
                            }
                        }
                    }
                    ImGui::PopStyleColor(3); // 恢复按钮样式
                }
                ImGui::SameLine();
                ImGui::Dummy(ImVec2(10.0f, 0.0f)); // 按钮之间添加水平间距
                ImGui::SameLine();

                {   // 红色按钮样式作用域
                    ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.8f, 0.2f, 0.2f, 1.0f));
                    ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.9f, 0.3f, 0.3f, 1.0f));
                    ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.7f, 0.1f, 0.1f, 1.0f));
                    if (ImGui::Button("删除脚本")) {
                        showDeleteConfirmDialog = true;
                        scriptToDelete = &currentScript;
                    }
                    ImGui::PopStyleColor(3); // 恢复按钮样式
                }
                ImGui::PopStyleVar(); // 恢复按钮内边距样式
            }


            ImGui::Separator();
            ImGui::Text("正在编辑文件: %s", currentScript.filePath.empty() ? (currentScript.name + " (新文件, 未保存)").c_str() : currentScript.filePath.c_str());
            ImGui::InputTextMultiline("##ScriptContent", &currentScript.content, ImVec2(-1, -1), ImGuiInputTextFlags_AllowTabInput);
        }
        else {
            ImGui::TextWrapped("从脚本列表中选择一个脚本，或者点击'新建文件'去创建一个脚本.");
        }
        ImGui::EndChild();
        ImGui::PopStyleVar(); // 恢复默认内边距

        // 删除确认对话框
        if (showDeleteConfirmDialog && scriptToDelete != nullptr) {
            ImGui::OpenPopup("删除确认");
            ImGui::SetNextWindowPos(ImGui::GetMainViewport()->GetCenter(), ImGuiCond_Appearing, ImVec2(0.5f, 0.5f));
            if (ImGui::BeginPopupModal("删除确认", nullptr, ImGuiWindowFlags_AlwaysAutoResize)) {
                ImGui::Text("确定要删除以下脚本吗？");
                ImGui::Text("%s", scriptToDelete->name.c_str());
                ImGui::Separator();

                if (ImGui::Button("确定", ImVec2(120, 0))) {
                    if (!scriptToDelete->filePath.empty()) {
                        try {
                            if (fs::exists(scriptToDelete->filePath) && fs::remove(scriptToDelete->filePath)) {
                                statusMessage = "脚本已删除: " + scriptToDelete->filePath;
                                LoadScriptsFromDirectory(selectedDirPath);
                                selectedScriptIndex = -1;
                            }
                            else {
                                statusMessage = "删除脚本失败: 文件不存在或无法删除";
                            }
                        }
                        catch (const fs::filesystem_error& e) {
                            statusMessage = "删除脚本时出错: " + std::string(e.what());
                        }
                    }
                    else {
                        availableScripts.erase(availableScripts.begin() + selectedScriptIndex);
                        selectedScriptIndex = -1;
                        statusMessage = "已移除未保存的脚本";
                    }
                    ImGui::CloseCurrentPopup();
                    showDeleteConfirmDialog = false;
                    scriptToDelete = nullptr;
                }
                ImGui::SameLine();
                if (ImGui::Button("取消", ImVec2(120, 0))) {
                    ImGui::CloseCurrentPopup();
                    showDeleteConfirmDialog = false;
                    scriptToDelete = nullptr;
                }
                ImGui::EndPopup();
            }
        }


        ImGui::Columns(1); // 恢复单列布局
        ImGui::EndChild();
    }
} // 结束 MyScriptEditor 命名空间