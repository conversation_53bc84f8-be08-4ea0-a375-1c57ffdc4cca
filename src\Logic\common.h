#pragma once

#include <string>
#include <vector>
#include <functional>
#include <map>
#include <opencv2/core/types.hpp>

// 前向声明
class DispatchCenter;
class VMControllerInfo;
class IVMTask;

// 图像匹配函数声明
cv::Point performVisualMatch(const std::string& vmName, const std::string& templateName, int sliceNumber);
bool wordsVisualMatch(const std::string& vmName, const std::string& matchWords, int sliceNumber);

// 默认虚拟机名称，可以在 UI 中设置
extern std::string DefaultVmName;

// 线程本地存储相关函数
void SetThreadLocalVmName(const std::string& vmName);
std::string GetThreadLocalVmName();

// 文本识别功能
#include <Vnc/VMTasks.h>  // 前向声明，避免循环包含
TextRecognizeResult textRecognize(const std::string& vmName, cv::Rect roi, int sliceNumber);

// ==========================================================================================
// ========================== 脚本解析和任务执行功能声明 ===========================
// ==========================================================================================

// 主要的脚本执行入口函数
void ExecuteSelectedTasks(const std::string& vmName);
void loadAndRunScriptFromString(const std::string& vmName, const std::string& scriptContent);

// 脚本解析核心函数
void parseScriptContent(const std::string& vmName, const std::string& scriptContent, VMControllerInfo* controllerInfo);
void parseAndExecuteCommand(const std::string& vmName, const std::string& line, int lineNumber, 
                           int sliceNumber, const std::string& scriptContent, 
                           const std::map<std::string, int>& labelMap);

// 工具函数
std::string trim(const std::string& str);
std::vector<std::string> parseArguments(const std::string& argsStr);

// 任务创建函数
void createTextMatchTask(const std::string& vmName, const std::vector<std::string>& args, 
                        int sliceNumber, DispatchCenter* dispatchCenter);
void createVisualMatchTask(const std::string& vmName, const std::vector<std::string>& args, 
                          int sliceNumber, DispatchCenter* dispatchCenter);
void createMouseMoveTask(const std::string& vmName, const std::vector<std::string>& args, 
                        int sliceNumber, DispatchCenter* dispatchCenter);
void createMouseClickTask(const std::string& vmName, const std::vector<std::string>& args, 
                         int sliceNumber, const std::string& clickType, DispatchCenter* dispatchCenter);
void createKeyboardInputTask(const std::string& vmName, const std::vector<std::string>& args, 
                           int sliceNumber, bool isTitleBar, DispatchCenter* dispatchCenter);
void createDelayTask(const std::string& vmName, const std::vector<std::string>& args, 
                    int sliceNumber, DispatchCenter* dispatchCenter);
void createLabelLocateTask(const std::string& vmName, const std::vector<std::string>& args, 
                          int sliceNumber, const std::string& scriptContent, DispatchCenter* dispatchCenter);
void createWaitForScreenStillTask(const std::string& vmName, const std::vector<std::string>& args, 
                                 int sliceNumber, DispatchCenter* dispatchCenter);
void createTextRecognizeTask(const std::string& vmName, const std::vector<std::string>& args, 
                           int sliceNumber, DispatchCenter* dispatchCenter);
void createVisionMouseDragTask(const std::string& vmName, const std::vector<std::string>& args, 
                              int sliceNumber, DispatchCenter* dispatchCenter);
void createWaitForVisualMatchTask(const std::string& vmName, const std::vector<std::string>& args, 
                                 int sliceNumber, DispatchCenter* dispatchCenter);

// ========================== 新增：解析与执行分离的数据结构 ==========================

// 解析出的单个任务信息
struct ParsedTask {
    std::string command;           // 命令名称（如 TEXT_MATCH, VISUAL_MATCH等）
    std::vector<std::string> args; // 命令参数
    int lineNumber;               // 脚本行号
    std::string originalLine;     // 原始脚本行
    
    ParsedTask(const std::string& cmd, const std::vector<std::string>& arguments, 
               int line, const std::string& original)
        : command(cmd), args(arguments), lineNumber(line), originalLine(original) {}
};

// 单个切片的解析结果
struct SliceParseResult {
    int sliceNumber = -1;                   // 切片号
    std::vector<ParsedTask> tasks;          // 该切片的所有任务
    int taskCount = 0;                      // 任务数量
    
    // 默认构造函数（为std::map所需）
    SliceParseResult() = default;
    
    // 带参数构造函数
    explicit SliceParseResult(int slice) : sliceNumber(slice) {}
    
    void addTask(const ParsedTask& task) {
        tasks.push_back(task);
        taskCount++;
    }
};

// 完整脚本的解析结果
struct ScriptParseResult {
    std::map<int, SliceParseResult> sliceResults;  // 切片号 -> 切片解析结果
    std::map<std::string, int> labelMap;           // 标签映射
    int totalTasks = 0;                            // 总任务数
    bool parseSuccess = true;                      // 解析是否成功
    std::string errorMessage;                      // 错误信息
    
    void addSliceResult(int sliceNumber, const SliceParseResult& result) {
        sliceResults[sliceNumber] = result;
        totalTasks += result.taskCount;
    }
    
    std::vector<int> getSliceNumbers() const {
        std::vector<int> slices;
        for (const auto& pair : sliceResults) {
            slices.push_back(pair.first);
        }
        return slices;
    }
};

// ========================== 函数声明修改 ==========================

// 新增：解析与执行分离的函数
ScriptParseResult parseScriptToResult(const std::string& vmName, const std::string& scriptContent, VMControllerInfo* controllerInfo);
bool loadParsedTasksToQueues(const std::string& vmName, const ScriptParseResult& parseResult, VMControllerInfo* controllerInfo);
bool executeTaskCreation(const std::string& vmName, const ParsedTask& parsedTask, int sliceNumber,
                        const std::string& scriptContent, const std::map<std::string, int>& labelMap, 
                        DispatchCenter* dispatchCenter);
std::shared_ptr<IVMTask> createTaskFromParsedTask(const std::string& vmName, const ParsedTask& parsedTask, 
                                                  const std::string& scriptContent, const std::map<std::string, int>& labelMap, 
                                                  DispatchCenter* dispatchCenter);

// 辅助函数
ParsedTask parseCommandLine(const std::string& line, int lineNumber, const std::string& originalLine);

// 修改：原有解析函数（保留兼容性）
void parseScriptContent(const std::string& vmName, const std::string& scriptContent, VMControllerInfo* controllerInfo);
