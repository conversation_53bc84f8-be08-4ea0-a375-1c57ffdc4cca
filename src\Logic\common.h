#pragma once

#include <string>
#include <vector>
#include <functional>
#include <map>
#include <opencv2/core/types.hpp>

// 前向声明
class DispatchCenter;
class VMControllerInfo;

// 图像匹配函数声明
cv::Point performVisualMatch(const std::string& vmName, const std::string& templateName, int sliceNumber);
bool wordsVisualMatch(const std::string& vmName, const std::string& matchWords, int sliceNumber);

// 默认虚拟机名称，可以在 UI 中设置
extern std::string DefaultVmName;

// 线程本地存储相关函数
void SetThreadLocalVmName(const std::string& vmName);
std::string GetThreadLocalVmName();

// 文本识别功能
#include <Vnc/VMTasks.h>  // 前向声明，避免循环包含
TextRecognizeResult textRecognize(const std::string& vmName, cv::Rect roi, int sliceNumber);

// ==========================================================================================
// ========================== 脚本解析和任务执行功能声明 ===========================
// ==========================================================================================

// 主要的脚本执行入口函数
void ExecuteSelectedTasks(const std::string& vmName);
void loadAndRunScriptFromString(const std::string& vmName, const std::string& scriptContent);

// 脚本解析核心函数
void parseScriptContent(const std::string& vmName, const std::string& scriptContent, VMControllerInfo* controllerInfo);
void parseAndExecuteCommand(const std::string& vmName, const std::string& line, int lineNumber, 
                           int sliceNumber, const std::string& scriptContent, 
                           const std::map<std::string, int>& labelMap);

// 工具函数
std::string trim(const std::string& str);
std::vector<std::string> parseArguments(const std::string& argsStr);

// 任务创建函数
void createTextMatchTask(const std::string& vmName, const std::vector<std::string>& args, 
                        int sliceNumber, DispatchCenter* dispatchCenter);
void createVisualMatchTask(const std::string& vmName, const std::vector<std::string>& args, 
                          int sliceNumber, DispatchCenter* dispatchCenter);
void createMouseMoveTask(const std::string& vmName, const std::vector<std::string>& args, 
                        int sliceNumber, DispatchCenter* dispatchCenter);
void createMouseClickTask(const std::string& vmName, const std::vector<std::string>& args, 
                         int sliceNumber, const std::string& clickType, DispatchCenter* dispatchCenter);
void createKeyboardInputTask(const std::string& vmName, const std::vector<std::string>& args, 
                           int sliceNumber, bool isTitleBar, DispatchCenter* dispatchCenter);
void createDelayTask(const std::string& vmName, const std::vector<std::string>& args, 
                    int sliceNumber, DispatchCenter* dispatchCenter);
void createLabelLocateTask(const std::string& vmName, const std::vector<std::string>& args, 
                          int sliceNumber, const std::string& scriptContent, DispatchCenter* dispatchCenter);
void createWaitForScreenStillTask(const std::string& vmName, const std::vector<std::string>& args, 
                                 int sliceNumber, DispatchCenter* dispatchCenter);
void createTextRecognizeTask(const std::string& vmName, const std::vector<std::string>& args, 
                           int sliceNumber, DispatchCenter* dispatchCenter);
void createVisionMouseDragTask(const std::string& vmName, const std::vector<std::string>& args, 
                              int sliceNumber, DispatchCenter* dispatchCenter);
void createWaitForVisualMatchTask(const std::string& vmName, const std::vector<std::string>& args, 
                                 int sliceNumber, DispatchCenter* dispatchCenter);
