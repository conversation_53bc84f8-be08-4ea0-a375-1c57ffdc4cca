#pragma once
#include <string>
#include <array>
#include <chrono>
#include <cstdint>

// ===========================================
//           字符串混淆和加密工具
// ===========================================

namespace StringObfuscation {
    
    // 简单的XOR加密key（运行时生成）
    constexpr char XOR_KEY = 0x5A;
    
    // 编译时字符串加密模板
    template<size_t N>
    class EncryptedString {
    private:
        std::array<char, N> encrypted_data;
        
    public:
        // 编译时加密构造函数
        constexpr EncryptedString(const char (&str)[N]) : encrypted_data{} {
            for (size_t i = 0; i < N; ++i) {
                encrypted_data[i] = str[i] ^ XOR_KEY;
            }
        }
        
        // 运行时解密
        std::string decrypt() const {
            std::string result;
            result.reserve(N - 1); // -1 for null terminator
            
            for (size_t i = 0; i < N - 1; ++i) {
                result += static_cast<char>(encrypted_data[i] ^ XOR_KEY);
            }
            
            return result;
        }
        
        // 获取解密后的C风格字符串（临时使用）
        const char* c_str() const {
            static thread_local std::string temp = decrypt();
            return temp.c_str();
        }
    };
    
    // 更复杂的加密函数（运行时）
    inline std::string complexEncrypt(const std::string& input) {
        std::string result = input;
        
        // 多重XOR加密
        for (size_t i = 0; i < result.length(); ++i) {
            result[i] ^= XOR_KEY;
            result[i] ^= (i % 255); // 位置相关的加密
            result[i] ^= 0x33;      // 额外的混淆
        }
        
        return result;
    }
    
    // 解密函数
    inline std::string complexDecrypt(const std::string& encrypted) {
        std::string result = encrypted;
        
        // 逆向解密
        for (size_t i = 0; i < result.length(); ++i) {
            result[i] ^= 0x33;
            result[i] ^= (i % 255);
            result[i] ^= XOR_KEY;
        }
        
        return result;
    }
    
    // 动态密钥生成
    inline char generateDynamicKey() {
        static bool initialized = false;
        static char dynamic_key = XOR_KEY;
        
        if (!initialized) {
            // 基于当前时间和程序地址生成动态密钥
            auto now = std::chrono::high_resolution_clock::now().time_since_epoch().count();
            dynamic_key = static_cast<char>((now ^ reinterpret_cast<uintptr_t>(&initialized)) & 0xFF);
            initialized = true;
        }
        
        return dynamic_key;
    }
}

// ===========================================
//              便捷宏定义
// ===========================================

// 加密字符串字面量宏
#define ENCRYPT_STR(str) StringObfuscation::EncryptedString<sizeof(str)>(str).decrypt()

// 加密C风格字符串宏  
#define ENCRYPT_CSTR(str) StringObfuscation::EncryptedString<sizeof(str)>(str).c_str()

// 条件编译：Release模式启用加密，Debug模式直接使用原字符串
#ifdef NDEBUG
    #define SAFE_STR(str) ENCRYPT_STR(str)
    #define SAFE_CSTR(str) ENCRYPT_CSTR(str)
#else
    #define SAFE_STR(str) std::string(str)
    #define SAFE_CSTR(str) str
#endif

// ===========================================
//           敏感信息保护宏
// ===========================================

// 保护VNC连接信息
#define PROTECT_VNC_HOST(ip) StringObfuscation::complexEncrypt(ip)
#define UNPROTECT_VNC_HOST(encrypted_ip) StringObfuscation::complexDecrypt(encrypted_ip)

// 保护日志中的敏感信息
#define SAFE_LOG(level, msg) AddLogInfo(level, SAFE_STR(msg))

// 保护错误消息
#define SAFE_ERROR(msg) SAFE_STR("Error: " msg)

// ===========================================
//              使用示例
// ===========================================

/*
使用方法：

1. 加密普通字符串：
   std::string safe_message = SAFE_STR("这是敏感信息");

2. 加密日志信息：
   SAFE_LOG(LogLevel::Info, "用户登录成功");

3. 保护VNC连接信息：
   std::string encrypted_ip = PROTECT_VNC_HOST("*************");
   std::string original_ip = UNPROTECT_VNC_HOST(encrypted_ip);

4. 加密错误消息：
   throw std::runtime_error(SAFE_ERROR("连接失败"));
*/ 